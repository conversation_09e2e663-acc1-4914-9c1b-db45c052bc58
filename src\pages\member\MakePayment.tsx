import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { 
  CreditCard, 
  Smartphone,
  Building2,
  Wallet,
  Shield,
  Calculator,
  Clock,
  CheckCircle,
  AlertTriangle,
  Loader2,
  Receipt,
  ArrowRight,
  QrCode,
  Banknote,
  Percent,
  Info
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/hooks/use-toast';
import { mockBookings, mockHostels, getHostelById } from '@/data/mockData';
import { 
  PaymentGateway, 
  PaymentMethod, 
  FeeCalculation, 
  PaymentRequest, 
  PaymentResponse,
  PaymentGatewayConfig 
} from '@/types/payment';

// Mock gateway configurations
const mockGatewayConfigs: PaymentGatewayConfig[] = [
  {
    gateway: 'phonepe',
    isEnabled: true,
    priority: 1,
    credentials: { merchantId: 'PHONEPE123', apiKey: 'pk_test_123', secretKey: 'sk_test_123', webhookSecret: 'whsec_123' },
    supportedMethods: ['upi', 'card', 'wallet'],
    fees: { percentage: 1.5, fixed: 2 },
    limits: { minAmount: 100, maxAmount: 100000 },
    settings: { autoCapture: true, timeout: 15, retryAttempts: 3 }
  },
  {
    gateway: 'cashfree',
    isEnabled: true,
    priority: 2,
    credentials: { merchantId: 'CASHFREE123', apiKey: 'pk_test_456', secretKey: 'sk_test_456', webhookSecret: 'whsec_456' },
    supportedMethods: ['upi', 'card', 'netbanking', 'wallet'],
    fees: { percentage: 1.8, fixed: 3 },
    limits: { minAmount: 50, maxAmount: 200000 },
    settings: { autoCapture: true, timeout: 20, retryAttempts: 2 }
  }
];

interface MakePaymentProps {
  bookingId?: string;
  prefilledAmount?: number;
  onPaymentSuccess?: (paymentId: string) => void;
  onPaymentFailure?: (error: string) => void;
}

export const MakePayment: React.FC<MakePaymentProps> = ({
  bookingId,
  prefilledAmount,
  onPaymentSuccess,
  onPaymentFailure
}) => {
  const { user } = useAuth();
  const [selectedBooking, setSelectedBooking] = useState<string>(bookingId || '');
  const [selectedGateway, setSelectedGateway] = useState<PaymentGateway>('phonepe');
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod>('upi');
  const [customAmount, setCustomAmount] = useState<string>(prefilledAmount?.toString() || '');
  const [isCalculating, setIsCalculating] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [feeCalculation, setFeeCalculation] = useState<FeeCalculation | null>(null);
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);
  const [paymentStep, setPaymentStep] = useState<'details' | 'processing' | 'success' | 'failed'>('details');
  const [gatewayStatus, setGatewayStatus] = useState<Record<PaymentGateway, boolean>>({
    phonepe: true,
    cashfree: true,
    razorpay: false
  });

  // Get user's bookings
  const currentUserId = user?.id || '11'; // Fallback for demo
  const userBookings = mockBookings.filter(b => b.userId === currentUserId && b.status === 'confirmed');
  const currentBooking = userBookings.find(b => b.id === selectedBooking);
  const hostel = currentBooking ? getHostelById(currentBooking.hostelId) : null;

  // Calculate fees when booking or amount changes
  useEffect(() => {
    if (currentBooking && customAmount) {
      calculateFees();
    }
  }, [currentBooking, customAmount, selectedGateway]);

  const calculateFees = async () => {
    if (!currentBooking || !hostel) return;
    
    setIsCalculating(true);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 800));
    
    const amount = parseFloat(customAmount) || 0;
    const gatewayConfig = mockGatewayConfigs.find(g => g.gateway === selectedGateway);
    const gatewayFee = gatewayConfig ? (amount * gatewayConfig.fees.percentage / 100) + gatewayConfig.fees.fixed : 0;
    
    const calculation: FeeCalculation = {
      bedCharges: {
        baseRate: hostel.bedTypes[currentBooking.bedType].monthlyRate,
        duration: 1,
        total: amount * 0.7 // 70% of payment for bed charges
      },
      foodCharges: {
        dailyRate: hostel.foodPackages.withMeals.dailyRate,
        days: 30,
        total: amount * 0.25 // 25% for food charges
      },
      additionalServices: [
        { name: 'Laundry Service', amount: 500, description: 'Monthly laundry package' },
        { name: 'WiFi Premium', amount: 300, description: 'High-speed internet' }
      ],
      discounts: [
        { name: 'Early Payment Discount', amount: 200, type: 'fixed' },
        { name: 'Loyalty Discount', amount: 5, type: 'percentage' }
      ],
      taxes: [
        { name: 'GST', amount: amount * 0.18, rate: 18 }
      ],
      securityDeposit: 0,
      lateFees: 0,
      subtotal: amount,
      totalDiscount: 200 + (amount * 0.05),
      totalTax: amount * 0.18,
      grandTotal: amount + gatewayFee
    };
    
    setFeeCalculation(calculation);
    setIsCalculating(false);
  };

  const getMethodIcon = (method: PaymentMethod) => {
    switch (method) {
      case 'card': return <CreditCard className="h-4 w-4" />;
      case 'upi': return <Smartphone className="h-4 w-4" />;
      case 'netbanking': return <Building2 className="h-4 w-4" />;
      case 'wallet': return <Wallet className="h-4 w-4" />;
      case 'emi': return <Banknote className="h-4 w-4" />;
      default: return <CreditCard className="h-4 w-4" />;
    }
  };

  const getGatewayLogo = (gateway: PaymentGateway) => {
    // In a real app, these would be actual logo images
    const logos = {
      phonepe: '📱',
      cashfree: '💳',
      razorpay: '⚡'
    };
    return logos[gateway];
  };

  const handlePayment = async () => {
    if (!currentBooking || !feeCalculation) {
      toast({
        title: "Error",
        description: "Please select a booking and calculate fees first",
        variant: "destructive"
      });
      return;
    }

    setIsProcessing(true);
    setShowPaymentDialog(true);
    setPaymentStep('processing');

    try {
      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Mock payment request
      const paymentRequest: PaymentRequest = {
        bookingId: currentBooking.id,
        amount: feeCalculation.grandTotal,
        currency: 'INR',
        paymentMethod: selectedMethod,
        gateway: selectedGateway,
        customerDetails: {
          name: user?.name || 'Test User',
          email: user?.email || '<EMAIL>',
          phone: user?.phone || '+91 9876543210'
        },
        metadata: {
          hostelId: currentBooking.hostelId,
          roomNumber: currentBooking.bedNumber,
          membershipType: 'regular'
        },
        returnUrl: `${window.location.origin}/payment/success`,
        webhookUrl: `${window.location.origin}/api/payment/webhook`
      };

      // Simulate gateway response
      const success = Math.random() > 0.1; // 90% success rate for demo
      
      if (success) {
        setPaymentStep('success');
        const paymentId = `PAY_${Date.now()}`;
        
        toast({
          title: "Payment Successful!",
          description: `Payment of ₹${feeCalculation.grandTotal.toLocaleString()} completed successfully`,
        });

        if (onPaymentSuccess) {
          onPaymentSuccess(paymentId);
        }
      } else {
        throw new Error('Payment failed due to insufficient funds');
      }
    } catch (error) {
      setPaymentStep('failed');
      const errorMessage = error instanceof Error ? error.message : 'Payment processing failed';
      
      toast({
        title: "Payment Failed",
        description: errorMessage,
        variant: "destructive"
      });

      if (onPaymentFailure) {
        onPaymentFailure(errorMessage);
      }
    } finally {
      setIsProcessing(false);
    }
  };

  const availableGateways = mockGatewayConfigs.filter(g => g.isEnabled && gatewayStatus[g.gateway]);
  const selectedGatewayConfig = availableGateways.find(g => g.gateway === selectedGateway);
  const availableMethods = selectedGatewayConfig?.supportedMethods || [];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold tracking-tight">Make Payment</h1>
          <p className="text-muted-foreground">
            Pay your hostel fees securely with multiple payment options
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Receipt className="mr-2 h-4 w-4" />
            View Receipts
          </Button>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Payment Form */}
        <div className="lg:col-span-2 space-y-6">
          {/* Booking Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Building2 className="h-5 w-5" />
                <span>Select Booking</span>
              </CardTitle>
              <CardDescription>
                Choose the booking you want to make payment for
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="booking">Active Bookings</Label>
                <Select value={selectedBooking} onValueChange={setSelectedBooking}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a booking" />
                  </SelectTrigger>
                  <SelectContent>
                    {userBookings.map((booking) => {
                      const hostelInfo = getHostelById(booking.hostelId);
                      return (
                        <SelectItem key={booking.id} value={booking.id}>
                          <div className="flex items-center space-x-2">
                            <span>{hostelInfo?.name}</span>
                            <Badge variant="outline">{booking.bedNumber}</Badge>
                          </div>
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>

              {currentBooking && hostel && (
                <div className="p-4 bg-muted rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Hostel:</span> {hostel.name}
                    </div>
                    <div>
                      <span className="font-medium">Room:</span> {currentBooking.bedNumber}
                    </div>
                    <div>
                      <span className="font-medium">Bed Type:</span> {currentBooking.bedType}
                    </div>
                    <div>
                      <span className="font-medium">Food Package:</span> {currentBooking.foodPackage.replace('_', ' ')}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Payment Amount */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Calculator className="h-5 w-5" />
                <span>Payment Amount</span>
              </CardTitle>
              <CardDescription>
                Enter the amount you want to pay
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="amount">Amount (₹)</Label>
                <Input
                  id="amount"
                  type="number"
                  placeholder="Enter amount"
                  value={customAmount}
                  onChange={(e) => setCustomAmount(e.target.value)}
                  min="100"
                  max="100000"
                />
              </div>

              {currentBooking && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setCustomAmount('15000')}
                  >
                    ₹15,000 (Half Month)
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setCustomAmount('30000')}
                  >
                    ₹30,000 (Full Month)
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setCustomAmount(currentBooking.amount.toString())}
                  >
                    ₹{currentBooking.amount.toLocaleString()} (Total Due)
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Payment Gateway Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5" />
                <span>Payment Gateway</span>
              </CardTitle>
              <CardDescription>
                Choose your preferred payment gateway
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {availableGateways.map((gateway) => (
                  <div
                    key={gateway.gateway}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedGateway === gateway.gateway
                        ? 'border-primary bg-primary/5'
                        : 'border-muted hover:border-primary/50'
                    }`}
                    onClick={() => setSelectedGateway(gateway.gateway)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{getGatewayLogo(gateway.gateway)}</span>
                        <div>
                          <div className="font-medium capitalize">{gateway.gateway}</div>
                          <div className="text-sm text-muted-foreground">
                            Fee: {gateway.fees.percentage}% + ₹{gateway.fees.fixed}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-1">
                        <div className={`w-2 h-2 rounded-full ${gatewayStatus[gateway.gateway] ? 'bg-green-500' : 'bg-red-500'}`} />
                        <span className="text-xs text-muted-foreground">
                          {gatewayStatus[gateway.gateway] ? 'Online' : 'Offline'}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Payment Method Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <CreditCard className="h-5 w-5" />
                <span>Payment Method</span>
              </CardTitle>
              <CardDescription>
                Select how you want to pay
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {availableMethods.map((method) => (
                  <div
                    key={method}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors text-center ${
                      selectedMethod === method
                        ? 'border-primary bg-primary/5'
                        : 'border-muted hover:border-primary/50'
                    }`}
                    onClick={() => setSelectedMethod(method)}
                  >
                    <div className="flex flex-col items-center space-y-2">
                      {getMethodIcon(method)}
                      <span className="text-sm font-medium capitalize">
                        {method === 'netbanking' ? 'Net Banking' : method}
                      </span>
                    </div>
                  </div>
                ))}
              </div>

              {selectedMethod === 'upi' && (
                <Alert>
                  <Smartphone className="h-4 w-4" />
                  <AlertDescription>
                    You'll be redirected to your UPI app to complete the payment
                  </AlertDescription>
                </Alert>
              )}

              {selectedMethod === 'card' && (
                <Alert>
                  <CreditCard className="h-4 w-4" />
                  <AlertDescription>
                    Secure card payment with 256-bit SSL encryption
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Fee Calculation Sidebar */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Receipt className="h-5 w-5" />
                <span>Payment Summary</span>
                {isCalculating && <Loader2 className="h-4 w-4 animate-spin" />}
              </CardTitle>
              <CardDescription>
                Detailed breakdown of charges
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {!feeCalculation && customAmount && currentBooking && (
                <div className="text-center py-8">
                  <Calculator className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">Calculating fees...</p>
                </div>
              )}

              {feeCalculation && (
                <div className="space-y-4">
                  {/* Base Charges */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Bed Charges</span>
                      <span>₹{feeCalculation.bedCharges.total.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Food Charges</span>
                      <span>₹{feeCalculation.foodCharges.total.toLocaleString()}</span>
                    </div>
                  </div>

                  {/* Taxes */}
                  <Separator />
                  <div className="space-y-2">
                    <div className="text-sm font-medium">Taxes</div>
                    {feeCalculation.taxes.map((tax, index) => (
                      <div key={index} className="flex justify-between text-sm">
                        <span>{tax.name} ({tax.rate}%)</span>
                        <span>₹{tax.amount.toLocaleString()}</span>
                      </div>
                    ))}
                  </div>

                  {/* Gateway Fee */}
                  <Separator />
                  <div className="flex justify-between text-sm">
                    <span>Gateway Fee</span>
                    <span>₹{(feeCalculation.grandTotal - feeCalculation.subtotal - feeCalculation.totalTax).toLocaleString()}</span>
                  </div>

                  {/* Total */}
                  <Separator />
                  <div className="flex justify-between font-bold">
                    <span>Total Amount</span>
                    <span>₹{feeCalculation.grandTotal.toLocaleString()}</span>
                  </div>

                  {/* Payment Button */}
                  <Button
                    className="w-full"
                    onClick={handlePayment}
                    disabled={isProcessing || !selectedBooking || !customAmount}
                  >
                    {isProcessing ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      <>
                        Pay ₹{feeCalculation.grandTotal.toLocaleString()}
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </>
                    )}
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Security Info */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-start space-x-3">
                <Shield className="h-5 w-5 text-green-600 mt-0.5" />
                <div className="space-y-1">
                  <div className="text-sm font-medium">Secure Payment</div>
                  <div className="text-xs text-muted-foreground">
                    Your payment is protected by 256-bit SSL encryption and PCI DSS compliance
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Payment Processing Dialog */}
      <Dialog open={showPaymentDialog} onOpenChange={setShowPaymentDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>
              {paymentStep === 'processing' && 'Processing Payment'}
              {paymentStep === 'success' && 'Payment Successful'}
              {paymentStep === 'failed' && 'Payment Failed'}
            </DialogTitle>
            <DialogDescription>
              {paymentStep === 'processing' && 'Please wait while we process your payment...'}
              {paymentStep === 'success' && 'Your payment has been processed successfully'}
              {paymentStep === 'failed' && 'There was an issue processing your payment'}
            </DialogDescription>
          </DialogHeader>

          <div className="flex flex-col items-center space-y-4 py-6">
            {paymentStep === 'processing' && (
              <>
                <Loader2 className="h-12 w-12 animate-spin text-primary" />
                <p className="text-sm text-muted-foreground text-center">
                  Connecting to {selectedGateway}...
                </p>
              </>
            )}

            {paymentStep === 'success' && (
              <>
                <CheckCircle className="h-12 w-12 text-green-600" />
                <div className="text-center">
                  <p className="font-medium">Payment Completed!</p>
                  <p className="text-sm text-muted-foreground">
                    Transaction ID: PAY_{Date.now()}
                  </p>
                </div>
              </>
            )}

            {paymentStep === 'failed' && (
              <>
                <AlertTriangle className="h-12 w-12 text-red-600" />
                <div className="text-center">
                  <p className="font-medium">Payment Failed</p>
                  <p className="text-sm text-muted-foreground">
                    Please try again or contact support
                  </p>
                </div>
              </>
            )}
          </div>

          <DialogFooter>
            {paymentStep === 'success' && (
              <Button onClick={() => setShowPaymentDialog(false)}>
                <Receipt className="mr-2 h-4 w-4" />
                Download Receipt
              </Button>
            )}
            {paymentStep === 'failed' && (
              <Button onClick={() => setShowPaymentDialog(false)}>
                Try Again
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
