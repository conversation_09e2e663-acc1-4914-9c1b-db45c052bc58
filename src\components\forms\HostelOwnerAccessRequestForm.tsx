import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { 
  Building2,
  User,
  Mail,
  Phone,
  MapPin,
  Briefcase,
  Upload,
  FileText,
  CheckCircle,
  AlertTriangle,
  Loader2,
  ArrowRight,
  ArrowLeft,
  X
} from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from '@/hooks/use-toast';

const ownerAccessRequestSchema = z.object({
  // Personal Information
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().min(10, 'Phone number must be at least 10 digits'),
  alternatePhone: z.string().optional(),
  address: z.string().min(10, 'Please enter complete address'),
  city: z.string().min(2, 'City is required'),
  state: z.string().min(2, 'State is required'),
  pincode: z.string().min(6, 'Valid pincode is required'),
  
  // Business Information
  businessName: z.string().optional(),
  businessType: z.string().min(2, 'Business type is required'),
  experienceYears: z.number().min(0).max(50),
  previousHostelExperience: z.boolean(),
  numberOfPropertiesOwned: z.number().min(0).optional(),
  estimatedInvestment: z.number().min(0).optional(),
  targetLocation: z.string().min(2, 'Target location is required'),
  expectedLaunchDate: z.string().optional(),
  
  // Documents
  idProofType: z.enum(['aadhar', 'pan', 'passport', 'driving_license']),
  idProofNumber: z.string().min(5, 'ID proof number is required'),
  
  // Additional Information
  motivation: z.string().min(50, 'Please provide detailed motivation (minimum 50 characters)'),
  businessPlan: z.string().optional(),
});

type OwnerAccessRequestFormData = z.infer<typeof ownerAccessRequestSchema>;

interface HostelOwnerAccessRequestFormProps {
  onSubmit: (data: OwnerAccessRequestFormData, files: Record<string, File>) => Promise<{ success: boolean; requestId?: string; error?: string }>;
  onCancel: () => void;
}

export const HostelOwnerAccessRequestForm: React.FC<HostelOwnerAccessRequestFormProps> = ({
  onSubmit,
  onCancel
}) => {
  const [currentStep, setCurrentStep] = useState<1 | 2 | 3 | 4>(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<Record<string, File>>({});
  const [references, setReferences] = useState<Array<{ name: string; relationship: string; phone: string; email?: string }>>([]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    trigger,
    getValues
  } = useForm<OwnerAccessRequestFormData>({
    resolver: zodResolver(ownerAccessRequestSchema),
    mode: 'onChange',
    defaultValues: {
      previousHostelExperience: false,
      experienceYears: 0
    }
  });

  const watchedPreviousExperience = watch('previousHostelExperience');

  const handleFileUpload = (key: string, file: File) => {
    setUploadedFiles(prev => ({
      ...prev,
      [key]: file
    }));
  };

  const addReference = () => {
    setReferences(prev => [...prev, { name: '', relationship: '', phone: '', email: '' }]);
  };

  const updateReference = (index: number, field: string, value: string) => {
    setReferences(prev => prev.map((ref, i) => 
      i === index ? { ...ref, [field]: value } : ref
    ));
  };

  const removeReference = (index: number) => {
    setReferences(prev => prev.filter((_, i) => i !== index));
  };

  const onFormSubmit = async (data: OwnerAccessRequestFormData) => {
    setIsSubmitting(true);
    
    try {
      const result = await onSubmit(data, uploadedFiles);
      
      if (result.success) {
        toast({
          title: "Request Submitted Successfully!",
          description: `Your hostel owner access request has been submitted. Request ID: ${result.requestId}`,
        });
        onCancel(); // Close the form
      } else {
        toast({
          title: "Submission Failed",
          description: result.error || "There was an error submitting your request. Please try again.",
          variant: "destructive"
        });
      }
      
    } catch (error) {
      console.error('Request submission error:', error);
      toast({
        title: "Submission Failed",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const nextStep = async () => {
    const isValid = await trigger();
    if (isValid) {
      setCurrentStep(prev => Math.min(prev + 1, 4) as 1 | 2 | 3 | 4);
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1) as 1 | 2 | 3 | 4);
  };

  const steps = [
    { number: 1, title: 'Personal Information', icon: User },
    { number: 2, title: 'Business Details', icon: Briefcase },
    { number: 3, title: 'Documents', icon: FileText },
    { number: 4, title: 'Additional Information', icon: Building2 }
  ];

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Progress Steps */}
      <div className="flex items-center justify-center mb-8">
        <div className="flex items-center space-x-4">
          {steps.map((step, index) => {
            const Icon = step.icon;
            return (
              <div key={step.number} className="flex items-center">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium ${
                  step.number <= currentStep 
                    ? 'bg-primary text-primary-foreground' 
                    : 'bg-muted text-muted-foreground'
                }`}>
                  {step.number < currentStep ? <CheckCircle className="h-5 w-5" /> : <Icon className="h-5 w-5" />}
                </div>
                <div className="ml-2 hidden sm:block">
                  <p className={`text-sm font-medium ${
                    step.number <= currentStep ? 'text-foreground' : 'text-muted-foreground'
                  }`}>
                    {step.title}
                  </p>
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-12 h-0.5 mx-4 ${
                    step.number < currentStep ? 'bg-primary' : 'bg-muted'
                  }`} />
                )}
              </div>
            );
          })}
        </div>
      </div>

      <form onSubmit={handleSubmit(onFormSubmit)}>
        {/* Step 1: Personal Information */}
        {currentStep === 1 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Personal Information
              </CardTitle>
              <CardDescription>
                Please provide your personal contact details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Full Name *</Label>
                  <Input
                    id="name"
                    {...register('name')}
                    placeholder="Enter your full name"
                  />
                  {errors.name && (
                    <p className="text-sm text-red-600 mt-1">{errors.name.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    type="email"
                    {...register('email')}
                    placeholder="Enter your email"
                  />
                  {errors.email && (
                    <p className="text-sm text-red-600 mt-1">{errors.email.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="phone">Phone Number *</Label>
                  <Input
                    id="phone"
                    {...register('phone')}
                    placeholder="Enter your phone number"
                  />
                  {errors.phone && (
                    <p className="text-sm text-red-600 mt-1">{errors.phone.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="alternatePhone">Alternate Phone</Label>
                  <Input
                    id="alternatePhone"
                    {...register('alternatePhone')}
                    placeholder="Enter alternate phone number"
                  />
                </div>

                <div className="md:col-span-2">
                  <Label htmlFor="address">Complete Address *</Label>
                  <Textarea
                    id="address"
                    {...register('address')}
                    placeholder="Enter your complete address"
                    rows={3}
                  />
                  {errors.address && (
                    <p className="text-sm text-red-600 mt-1">{errors.address.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="city">City *</Label>
                  <Input
                    id="city"
                    {...register('city')}
                    placeholder="Enter your city"
                  />
                  {errors.city && (
                    <p className="text-sm text-red-600 mt-1">{errors.city.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="state">State *</Label>
                  <Input
                    id="state"
                    {...register('state')}
                    placeholder="Enter your state"
                  />
                  {errors.state && (
                    <p className="text-sm text-red-600 mt-1">{errors.state.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="pincode">Pincode *</Label>
                  <Input
                    id="pincode"
                    {...register('pincode')}
                    placeholder="Enter pincode"
                  />
                  {errors.pincode && (
                    <p className="text-sm text-red-600 mt-1">{errors.pincode.message}</p>
                  )}
                </div>
              </div>

              <div className="flex justify-between pt-4">
                <Button type="button" variant="outline" onClick={onCancel}>
                  Cancel
                </Button>
                <Button type="button" onClick={nextStep}>
                  Next Step
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 2: Business Details */}
        {currentStep === 2 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Briefcase className="h-5 w-5" />
                Business Information
              </CardTitle>
              <CardDescription>
                Tell us about your business background and plans
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="businessName">Business Name</Label>
                  <Input
                    id="businessName"
                    {...register('businessName')}
                    placeholder="Enter your business name (if any)"
                  />
                </div>

                <div>
                  <Label htmlFor="businessType">Business Type *</Label>
                  <Input
                    id="businessType"
                    {...register('businessType')}
                    placeholder="e.g., Real Estate, Hospitality, etc."
                  />
                  {errors.businessType && (
                    <p className="text-sm text-red-600 mt-1">{errors.businessType.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="experienceYears">Years of Business Experience *</Label>
                  <Input
                    id="experienceYears"
                    type="number"
                    {...register('experienceYears', { valueAsNumber: true })}
                    placeholder="Years in business"
                  />
                  {errors.experienceYears && (
                    <p className="text-sm text-red-600 mt-1">{errors.experienceYears.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="numberOfPropertiesOwned">Number of Properties Owned</Label>
                  <Input
                    id="numberOfPropertiesOwned"
                    type="number"
                    {...register('numberOfPropertiesOwned', { valueAsNumber: true })}
                    placeholder="Number of properties you own"
                  />
                </div>

                <div>
                  <Label htmlFor="estimatedInvestment">Estimated Investment (₹)</Label>
                  <Input
                    id="estimatedInvestment"
                    type="number"
                    {...register('estimatedInvestment', { valueAsNumber: true })}
                    placeholder="Estimated investment amount"
                  />
                </div>

                <div>
                  <Label htmlFor="targetLocation">Target Location *</Label>
                  <Input
                    id="targetLocation"
                    {...register('targetLocation')}
                    placeholder="Where do you plan to establish your hostel?"
                  />
                  {errors.targetLocation && (
                    <p className="text-sm text-red-600 mt-1">{errors.targetLocation.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="expectedLaunchDate">Expected Launch Date</Label>
                  <Input
                    id="expectedLaunchDate"
                    type="date"
                    {...register('expectedLaunchDate')}
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="previousHostelExperience"
                  checked={watchedPreviousExperience}
                  onCheckedChange={(checked) => setValue('previousHostelExperience', !!checked)}
                />
                <Label htmlFor="previousHostelExperience">
                  I have previous experience in hostel/accommodation business
                </Label>
              </div>

              <div className="flex justify-between pt-4">
                <Button type="button" variant="outline" onClick={prevStep}>
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Previous
                </Button>
                <Button type="button" onClick={nextStep}>
                  Next Step
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 3: Documents */}
        {currentStep === 3 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Document Upload
              </CardTitle>
              <CardDescription>
                Please upload the required documents for verification
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* ID Proof */}
              <div className="space-y-4">
                <h3 className="font-semibold">Identity Proof *</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="idProofType">ID Proof Type</Label>
                    <Select onValueChange={(value) => setValue('idProofType', value as any)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select ID type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="aadhar">Aadhar Card</SelectItem>
                        <SelectItem value="pan">PAN Card</SelectItem>
                        <SelectItem value="passport">Passport</SelectItem>
                        <SelectItem value="driving_license">Driving License</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="idProofNumber">ID Number</Label>
                    <Input
                      id="idProofNumber"
                      {...register('idProofNumber')}
                      placeholder="Enter ID number"
                    />
                    {errors.idProofNumber && (
                      <p className="text-sm text-red-600 mt-1">{errors.idProofNumber.message}</p>
                    )}
                  </div>
                </div>

                <div>
                  <Label htmlFor="idProofFile">Upload ID Proof</Label>
                  <Input
                    id="idProofFile"
                    type="file"
                    accept=".pdf,.jpg,.jpeg,.png"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) handleFileUpload('idProof', file);
                    }}
                  />
                  {uploadedFiles.idProof && (
                    <p className="text-sm text-green-600 mt-1">
                      ✓ {uploadedFiles.idProof.name}
                    </p>
                  )}
                </div>
              </div>

              {/* Address Proof */}
              <div className="space-y-4">
                <h3 className="font-semibold">Address Proof (Optional)</h3>
                <div>
                  <Label htmlFor="addressProofFile">Upload Address Proof</Label>
                  <Input
                    id="addressProofFile"
                    type="file"
                    accept=".pdf,.jpg,.jpeg,.png"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) handleFileUpload('addressProof', file);
                    }}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Utility bill, bank statement, or rental agreement
                  </p>
                  {uploadedFiles.addressProof && (
                    <p className="text-sm text-green-600 mt-1">
                      ✓ {uploadedFiles.addressProof.name}
                    </p>
                  )}
                </div>
              </div>

              {/* Business Proof */}
              <div className="space-y-4">
                <h3 className="font-semibold">Business Proof (Optional)</h3>
                <div>
                  <Label htmlFor="businessProofFile">Upload Business Documents</Label>
                  <Input
                    id="businessProofFile"
                    type="file"
                    accept=".pdf,.jpg,.jpeg,.png"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) handleFileUpload('businessProof', file);
                    }}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    GST certificate, trade license, or incorporation certificate
                  </p>
                  {uploadedFiles.businessProof && (
                    <p className="text-sm text-green-600 mt-1">
                      ✓ {uploadedFiles.businessProof.name}
                    </p>
                  )}
                </div>
              </div>

              {/* Financial Proof */}
              <div className="space-y-4">
                <h3 className="font-semibold">Financial Proof (Optional)</h3>
                <div>
                  <Label htmlFor="financialProofFile">Upload Financial Documents</Label>
                  <Input
                    id="financialProofFile"
                    type="file"
                    accept=".pdf,.jpg,.jpeg,.png"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) handleFileUpload('financialProof', file);
                    }}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Bank statements, ITR, or financial statements
                  </p>
                  {uploadedFiles.financialProof && (
                    <p className="text-sm text-green-600 mt-1">
                      ✓ {uploadedFiles.financialProof.name}
                    </p>
                  )}
                </div>
              </div>

              <div className="flex justify-between pt-4">
                <Button type="button" variant="outline" onClick={prevStep}>
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Previous
                </Button>
                <Button type="button" onClick={nextStep}>
                  Next Step
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 4: Additional Information */}
        {currentStep === 4 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Additional Information
              </CardTitle>
              <CardDescription>
                Tell us more about your motivation and plans
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label htmlFor="motivation">Motivation & Goals *</Label>
                <Textarea
                  id="motivation"
                  {...register('motivation')}
                  placeholder="Why do you want to become a hostel owner? What are your goals? (minimum 50 characters)"
                  rows={4}
                />
                {errors.motivation && (
                  <p className="text-sm text-red-600 mt-1">{errors.motivation.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="businessPlan">Business Plan (Optional)</Label>
                <Textarea
                  id="businessPlan"
                  {...register('businessPlan')}
                  placeholder="Describe your business plan, target market, unique features, etc."
                  rows={4}
                />
              </div>

              {/* References */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold">References (Optional)</h3>
                  <Button type="button" variant="outline" size="sm" onClick={addReference}>
                    Add Reference
                  </Button>
                </div>

                {references.map((reference, index) => (
                  <div key={index} className="p-4 border rounded-lg space-y-3">
                    <div className="flex justify-between items-center">
                      <h4 className="font-medium">Reference {index + 1}</h4>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeReference(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      <Input
                        placeholder="Name"
                        value={reference.name}
                        onChange={(e) => updateReference(index, 'name', e.target.value)}
                      />
                      <Input
                        placeholder="Relationship"
                        value={reference.relationship}
                        onChange={(e) => updateReference(index, 'relationship', e.target.value)}
                      />
                      <Input
                        placeholder="Phone"
                        value={reference.phone}
                        onChange={(e) => updateReference(index, 'phone', e.target.value)}
                      />
                      <Input
                        placeholder="Email (optional)"
                        value={reference.email}
                        onChange={(e) => updateReference(index, 'email', e.target.value)}
                      />
                    </div>
                  </div>
                ))}
              </div>

              <div className="flex justify-between pt-4">
                <Button type="button" variant="outline" onClick={prevStep}>
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Previous
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting || !uploadedFiles.idProof}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Submitting...
                    </>
                  ) : (
                    'Submit Request'
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </form>
    </div>
  );
};
