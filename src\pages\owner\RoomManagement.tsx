import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Building2,
  Bed,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Eye,
  Edit,
  Users,
  DollarSign,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  Settings,
  Layers
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/hooks/use-toast';
import { Room, RoomStatistics, HostelInfo } from '@/types/roomManagement';
import { HostelSelector } from '@/components/hostel/HostelSelector';
import { FloorFilter } from '@/components/floor/FloorFilter';
import { FloorStatistics } from '@/components/floor/FloorStatistics';
import { AddFloorDialog } from '@/components/floor/AddFloorDialog';
import { getRoomsByHostelId, getRoomStatistics } from '@/data/mockData';
import { AddRoomDialog } from '@/components/room/AddRoomDialog';
import { AddBedDialog } from '@/components/room/AddBedDialog';
import { RoomDetailsDialog } from '@/components/room/RoomDetailsDialog';

export const RoomManagement: React.FC = () => {
  const { user } = useAuth();
  const [rooms, setRooms] = useState<Room[]>([]);
  const [statistics, setStatistics] = useState<RoomStatistics | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);
  const [isAddRoomDialogOpen, setIsAddRoomDialogOpen] = useState(false);
  const [isAddBedDialogOpen, setIsAddBedDialogOpen] = useState(false);
  const [isRoomDetailsDialogOpen, setIsRoomDetailsDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Hostel and Floor Management State
  const [selectedHostel, setSelectedHostel] = useState<HostelInfo | null>(null);
  const [selectedFloors, setSelectedFloors] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    // Set initial loading to false since we don't need to load data until hostel is selected
    setIsLoading(false);
  }, []);

  useEffect(() => {
    if (selectedHostel) {
      loadRoomData();
    }
  }, [selectedHostel, selectedFloors]);

  const loadRoomData = () => {
    if (!selectedHostel) return;

    setIsLoading(true);
    try {
      // Load rooms based on selected hostel and floors
      let roomsData = getRoomsByHostelId(selectedHostel.id);

      // Filter by selected floors if any
      if (selectedFloors.length > 0) {
        roomsData = roomsData.filter(room => selectedFloors.includes(room.floorId));
      }

      setRooms(roomsData);

      // Load statistics using the selected hostel ID
      const statsResult = getRoomStatistics(selectedHostel.id);
      setStatistics(statsResult);

    } catch (error) {
      console.error('Error loading room data:', error);
      toast({
        title: "Error",
        description: "Failed to load room data",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const filteredRooms = rooms.filter(room =>
    room.roomNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
    room.roomType.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusBadge = (status: Room['status']) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case 'maintenance':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Maintenance</Badge>;
      case 'inactive':
        return <Badge variant="destructive">Inactive</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getBedStatusBadge = (status: string) => {
    switch (status) {
      case 'occupied':
        return <Badge className="bg-blue-100 text-blue-800">Occupied</Badge>;
      case 'available':
        return <Badge className="bg-green-100 text-green-800">Available</Badge>;
      case 'maintenance':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700">Maintenance</Badge>;
      case 'reserved':
        return <Badge variant="outline" className="bg-purple-50 text-purple-700">Reserved</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const handleAddRoom = () => {
    setIsAddRoomDialogOpen(true);
  };

  const handleAddBed = (room: Room) => {
    setSelectedRoom(room);
    setIsAddBedDialogOpen(true);
  };

  const handleViewRoom = (room: Room) => {
    setSelectedRoom(room);
    setIsRoomDetailsDialogOpen(true);
  };

  const handleRoomAdded = () => {
    loadRoomData();
    setIsAddRoomDialogOpen(false);
    toast({
      title: "Room Request Submitted",
      description: "Your room addition request has been submitted for admin approval.",
    });
  };

  const handleBedAdded = () => {
    loadRoomData();
    setIsAddBedDialogOpen(false);
    toast({
      title: "Beds Added Successfully",
      description: "New beds have been added to the room.",
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading room data...</p>
        </div>
      </div>
    );
  }

  const handleHostelChange = (hostelId: string, hostel: HostelInfo) => {
    setSelectedHostel(hostel);
    setSelectedFloors([]); // Reset floor selection when hostel changes
  };

  const handleFloorSelectionChange = (floorIds: string[]) => {
    setSelectedFloors(floorIds);
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Room & Floor Management</h1>
          <p className="text-muted-foreground">
            Manage your hostel rooms, beds, and floor configurations
          </p>
        </div>
        <div className="flex space-x-2">
          {selectedHostel && (
            <AddFloorDialog
              hostel={selectedHostel}
              ownerId={user?.id || '2'}
              onFloorAdded={loadRoomData}
              trigger={
                <Button variant="outline">
                  <Layers className="mr-2 h-4 w-4" />
                  Add Floor
                </Button>
              }
            />
          )}
          <Button onClick={handleAddRoom}>
            <Plus className="mr-2 h-4 w-4" />
            Add Room
          </Button>
        </div>
      </div>

      {/* Hostel Selection */}
      <HostelSelector
        ownerId={user?.id || '2'}
        selectedHostelId={selectedHostel?.id}
        onHostelChange={handleHostelChange}
        className="mb-6"
      />

      {/* Main Content - Only show if hostel is selected */}
      {selectedHostel ? (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="floors">Floor Management</TabsTrigger>
            <TabsTrigger value="rooms">Room Management</TabsTrigger>
            <TabsTrigger value="statistics">Statistics</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {/* Quick Statistics */}
            {statistics && (
              <div className="grid gap-4 md:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Rooms</CardTitle>
                    <Building2 className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{statistics.totalRooms}</div>
                    <p className="text-xs text-muted-foreground">
                      Active rooms in your hostel
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Beds</CardTitle>
                    <Bed className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{statistics.totalBeds}</div>
                    <p className="text-xs text-muted-foreground">
                      Available bed spaces
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Occupancy Rate</CardTitle>
                    <Users className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{statistics.occupancyRate.toFixed(1)}%</div>
                    <p className="text-xs text-muted-foreground">
                      Current occupancy
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">₹{statistics.revenueStats.monthlyRevenue.toLocaleString()}</div>
                    <p className="text-xs text-muted-foreground">
                      Projected monthly income
                    </p>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Quick Actions */}
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                  <CardDescription>Common management tasks</CardDescription>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button onClick={handleAddRoom} className="w-full justify-start">
                    <Plus className="mr-2 h-4 w-4" />
                    Add New Room
                  </Button>
                  <AddFloorDialog
                    hostel={selectedHostel}
                    ownerId={user?.id || '2'}
                    onFloorAdded={loadRoomData}
                    trigger={
                      <Button variant="outline" className="w-full justify-start">
                        <Layers className="mr-2 h-4 w-4" />
                        Add New Floor
                      </Button>
                    }
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Recent Activity</CardTitle>
                  <CardDescription>Latest updates and changes</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm">Room 101 bed added</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-yellow-500" />
                      <span className="text-sm">Floor 4 request pending</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <AlertCircle className="h-4 w-4 text-red-500" />
                      <span className="text-sm">Room 203 maintenance needed</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Floor Management Tab */}
          <TabsContent value="floors" className="space-y-6">
            <div className="grid gap-6 lg:grid-cols-3">
              <div className="lg:col-span-1">
                <FloorFilter
                  hostelId={selectedHostel.id}
                  selectedFloors={selectedFloors}
                  onFloorSelectionChange={handleFloorSelectionChange}
                />
              </div>
              <div className="lg:col-span-2">
                <FloorStatistics
                  hostelId={selectedHostel.id}
                  selectedFloors={selectedFloors}
                />
              </div>
            </div>
          </TabsContent>

          {/* Room Management Tab */}
          <TabsContent value="rooms" className="space-y-6">
            {/* Room Management */}
            <Card>
        <CardHeader>
          <CardTitle>Rooms & Beds</CardTitle>
          <CardDescription>
            Manage your room configurations and bed assignments
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Search and Filters */}
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search rooms by number or type..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              Filters
            </Button>
          </div>

          {/* Rooms Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Room Number</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Floor</TableHead>
                  <TableHead>Capacity</TableHead>
                  <TableHead>Occupancy</TableHead>
                  <TableHead>Daily Rate</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredRooms.map((room) => (
                  <TableRow key={room.id}>
                    <TableCell className="font-medium">{room.roomNumber}</TableCell>
                    <TableCell>
                      <Badge variant="outline">{room.roomType}</Badge>
                    </TableCell>
                    <TableCell>{room.floor}</TableCell>
                    <TableCell>{room.capacity} beds</TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <span>{room.currentOccupancy}/{room.capacity}</span>
                        <div className="w-16 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full" 
                            style={{ width: `${(room.currentOccupancy / room.capacity) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>₹{room.pricing.dailyRate}</TableCell>
                    <TableCell>{getStatusBadge(room.status)}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => handleViewRoom(room)}>
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleAddBed(room)}>
                            <Plus className="mr-2 h-4 w-4" />
                            Add Beds
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Room
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Settings className="mr-2 h-4 w-4" />
                            Settings
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredRooms.length === 0 && (
            <div className="text-center py-8">
              <Building2 className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No rooms found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm ? 'Try adjusting your search criteria.' : 'Get started by adding your first room.'}
              </p>
              {!searchTerm && (
                <div className="mt-6">
                  <Button onClick={handleAddRoom}>
                    <Plus className="mr-2 h-4 w-4" />
                    Add Room
                  </Button>
                </div>
              )}
            </div>
            )}
            </CardContent>
          </Card>
          </TabsContent>

          {/* Statistics Tab */}
          <TabsContent value="statistics" className="space-y-6">
            <FloorStatistics
              hostelId={selectedHostel.id}
              selectedFloors={selectedFloors}
            />
          </TabsContent>
        </Tabs>
      ) : (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <Building2 className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Select a hostel</h3>
              <p className="mt-1 text-sm text-gray-500">
                Choose a hostel from the dropdown above to start managing rooms and floors.
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Dialogs */}
      {selectedHostel && (
        <>
          <AddRoomDialog
            isOpen={isAddRoomDialogOpen}
            onClose={() => setIsAddRoomDialogOpen(false)}
            onRoomAdded={handleRoomAdded}
            hostelId={selectedHostel.id}
            ownerId={user?.id || ''}
          />

          <AddBedDialog
            isOpen={isAddBedDialogOpen}
            onClose={() => setIsAddBedDialogOpen(false)}
            onBedAdded={handleBedAdded}
            room={selectedRoom}
            ownerId={user?.id || ''}
          />

          <RoomDetailsDialog
            isOpen={isRoomDetailsDialogOpen}
            onClose={() => setIsRoomDetailsDialogOpen(false)}
            room={selectedRoom}
          />
        </>
      )}
    </div>
  );
};
