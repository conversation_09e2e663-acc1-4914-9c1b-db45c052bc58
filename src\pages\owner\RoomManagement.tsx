import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Building2,
  Bed,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Eye,
  Edit,
  Users,
  DollarSign,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  Settings
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/hooks/use-toast';
import { Room, RoomStatistics } from '@/types/roomManagement';
import { getRoomsByHostelId } from '@/data/mockData';
import { getRoomStatistics } from '@/services/roomManagementService';
import { AddRoomDialog } from '@/components/room/AddRoomDialog';
import { AddBedDialog } from '@/components/room/AddBedDialog';
import { RoomDetailsDialog } from '@/components/room/RoomDetailsDialog';

export const RoomManagement: React.FC = () => {
  const { user } = useAuth();
  const [rooms, setRooms] = useState<Room[]>([]);
  const [statistics, setStatistics] = useState<RoomStatistics | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);
  const [isAddRoomDialogOpen, setIsAddRoomDialogOpen] = useState(false);
  const [isAddBedDialogOpen, setIsAddBedDialogOpen] = useState(false);
  const [isRoomDetailsDialogOpen, setIsRoomDetailsDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Mock hostel ID - in real app, this would come from user's hostel data
  const hostelId = '1';

  useEffect(() => {
    loadRoomData();
  }, []);

  const loadRoomData = async () => {
    setIsLoading(true);
    try {
      // Load rooms
      const roomsData = getRoomsByHostelId(hostelId);
      setRooms(roomsData);

      // Load statistics
      const statsResult = await getRoomStatistics(hostelId);
      if (statsResult.success) {
        setStatistics(statsResult.data);
      }
    } catch (error) {
      console.error('Error loading room data:', error);
      toast({
        title: "Error",
        description: "Failed to load room data",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const filteredRooms = rooms.filter(room =>
    room.roomNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
    room.roomType.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusBadge = (status: Room['status']) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case 'maintenance':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Maintenance</Badge>;
      case 'inactive':
        return <Badge variant="destructive">Inactive</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getBedStatusBadge = (status: string) => {
    switch (status) {
      case 'occupied':
        return <Badge className="bg-blue-100 text-blue-800">Occupied</Badge>;
      case 'available':
        return <Badge className="bg-green-100 text-green-800">Available</Badge>;
      case 'maintenance':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700">Maintenance</Badge>;
      case 'reserved':
        return <Badge variant="outline" className="bg-purple-50 text-purple-700">Reserved</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const handleAddRoom = () => {
    setIsAddRoomDialogOpen(true);
  };

  const handleAddBed = (room: Room) => {
    setSelectedRoom(room);
    setIsAddBedDialogOpen(true);
  };

  const handleViewRoom = (room: Room) => {
    setSelectedRoom(room);
    setIsRoomDetailsDialogOpen(true);
  };

  const handleRoomAdded = () => {
    loadRoomData();
    setIsAddRoomDialogOpen(false);
    toast({
      title: "Room Request Submitted",
      description: "Your room addition request has been submitted for admin approval.",
    });
  };

  const handleBedAdded = () => {
    loadRoomData();
    setIsAddBedDialogOpen(false);
    toast({
      title: "Beds Added Successfully",
      description: "New beds have been added to the room.",
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading room data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Room Management</h1>
          <p className="text-muted-foreground">
            Manage your hostel rooms and bed configurations
          </p>
        </div>
        <Button onClick={handleAddRoom}>
          <Plus className="mr-2 h-4 w-4" />
          Add Room
        </Button>
      </div>

      {/* Statistics Cards */}
      {statistics && (
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Rooms</CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.totalRooms}</div>
              <p className="text-xs text-muted-foreground">
                Active rooms in your hostel
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Beds</CardTitle>
              <Bed className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.totalBeds}</div>
              <p className="text-xs text-muted-foreground">
                {statistics.availableBeds} available, {statistics.occupiedBeds} occupied
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Occupancy Rate</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.occupancyRate.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">
                Current occupancy level
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">₹{statistics.revenueStats.monthlyRevenue.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                Estimated monthly income
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Room Management */}
      <Card>
        <CardHeader>
          <CardTitle>Rooms & Beds</CardTitle>
          <CardDescription>
            Manage your room configurations and bed assignments
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Search and Filters */}
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search rooms by number or type..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              Filters
            </Button>
          </div>

          {/* Rooms Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Room Number</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Floor</TableHead>
                  <TableHead>Capacity</TableHead>
                  <TableHead>Occupancy</TableHead>
                  <TableHead>Daily Rate</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredRooms.map((room) => (
                  <TableRow key={room.id}>
                    <TableCell className="font-medium">{room.roomNumber}</TableCell>
                    <TableCell>
                      <Badge variant="outline">{room.roomType}</Badge>
                    </TableCell>
                    <TableCell>{room.floor}</TableCell>
                    <TableCell>{room.capacity} beds</TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <span>{room.currentOccupancy}/{room.capacity}</span>
                        <div className="w-16 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full" 
                            style={{ width: `${(room.currentOccupancy / room.capacity) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>₹{room.pricing.dailyRate}</TableCell>
                    <TableCell>{getStatusBadge(room.status)}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => handleViewRoom(room)}>
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleAddBed(room)}>
                            <Plus className="mr-2 h-4 w-4" />
                            Add Beds
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Room
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Settings className="mr-2 h-4 w-4" />
                            Settings
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredRooms.length === 0 && (
            <div className="text-center py-8">
              <Building2 className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No rooms found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm ? 'Try adjusting your search criteria.' : 'Get started by adding your first room.'}
              </p>
              {!searchTerm && (
                <div className="mt-6">
                  <Button onClick={handleAddRoom}>
                    <Plus className="mr-2 h-4 w-4" />
                    Add Room
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Dialogs */}
      <AddRoomDialog
        isOpen={isAddRoomDialogOpen}
        onClose={() => setIsAddRoomDialogOpen(false)}
        onRoomAdded={handleRoomAdded}
        hostelId={hostelId}
        ownerId={user?.id || ''}
      />

      <AddBedDialog
        isOpen={isAddBedDialogOpen}
        onClose={() => setIsAddBedDialogOpen(false)}
        onBedAdded={handleBedAdded}
        room={selectedRoom}
        ownerId={user?.id || ''}
      />

      <RoomDetailsDialog
        isOpen={isRoomDetailsDialogOpen}
        onClose={() => setIsRoomDetailsDialogOpen(false)}
        room={selectedRoom}
      />
    </div>
  );
};
