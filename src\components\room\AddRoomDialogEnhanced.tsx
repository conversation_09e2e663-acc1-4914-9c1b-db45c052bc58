import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, Plus, X, Layers, CheckCircle, Building2, Home, ArrowRight, AlertCircle } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from '@/hooks/use-toast';
import { RoomType, BedType, BedSize, Floor, HostelInfo } from '@/types/roomManagement';
import { submitAddRoomRequest } from '@/services/roomManagementService';
import { getFloorsByHostelId, getHostelsByOwnerId } from '@/data/mockData';

const addRoomSchema = z.object({
  roomNumber: z.string().min(1, 'Room number is required'),
  roomType: z.enum(['single', 'double', 'triple', 'quad', 'dormitory', 'suite', 'studio', 'family']),
  floorId: z.string().min(1, 'Floor selection is required'),
  floor: z.number().min(0, 'Floor must be 0 or higher'),
  capacity: z.number().min(1, 'Capacity must be at least 1'),
  area: z.number().min(1, 'Area is required'),
  dailyRate: z.number().min(1, 'Daily rate is required'),
  weeklyRate: z.number().min(1, 'Weekly rate is required'),
  monthlyRate: z.number().min(1, 'Monthly rate is required'),
  securityDeposit: z.number().min(0, 'Security deposit must be 0 or higher'),
  cleaningFee: z.number().min(0, 'Cleaning fee must be 0 or higher'),
  description: z.string().optional(),
  reason: z.string().min(10, 'Please provide a reason for adding this room'),
  expectedOccupancyIncrease: z.number().min(1, 'Expected occupancy increase is required')
});

type AddRoomFormData = z.infer<typeof addRoomSchema>;

interface BedConfig {
  bedNumber: string;
  bedType: BedType;
  size: BedSize;
  hasLocker: boolean;
  lockerNumber?: string;
}

interface AddRoomDialogEnhancedProps {
  isOpen: boolean;
  onClose: () => void;
  onRoomAdded: () => void;
  ownerId: string;
  preSelectedHostelId?: string;
  preSelectedFloorId?: string;
}

export const AddRoomDialogEnhanced: React.FC<AddRoomDialogEnhancedProps> = ({
  isOpen,
  onClose,
  onRoomAdded,
  ownerId,
  preSelectedHostelId,
  preSelectedFloorId
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Selection State
  const [availableHostels, setAvailableHostels] = useState<HostelInfo[]>([]);
  const [selectedHostel, setSelectedHostel] = useState<HostelInfo | null>(null);
  const [availableFloors, setAvailableFloors] = useState<Floor[]>([]);
  const [selectedFloor, setSelectedFloor] = useState<Floor | null>(null);
  const [isLoadingHostels, setIsLoadingHostels] = useState(false);
  
  // Form State
  const [selectedAmenities, setSelectedAmenities] = useState<string[]>([]);
  const [bedConfigs, setBedConfigs] = useState<BedConfig[]>([]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    reset
  } = useForm<AddRoomFormData>({
    resolver: zodResolver(addRoomSchema),
    defaultValues: {
      roomNumber: '',
      roomType: 'double',
      floorId: '',
      floor: 1,
      capacity: 2,
      area: 100,
      dailyRate: 300,
      weeklyRate: 2000,
      monthlyRate: 8000,
      securityDeposit: 5000,
      cleaningFee: 200,
      expectedOccupancyIncrease: 2
    }
  });

  const watchedCapacity = watch('capacity');
  const watchedRoomNumber = watch('roomNumber');

  // Load hostels when dialog opens
  useEffect(() => {
    if (isOpen) {
      loadHostels();
      setCurrentStep(1);
      reset();
      setSelectedAmenities([]);
      setBedConfigs([]);
    }
  }, [isOpen, reset]);

  // Auto-select hostel if preSelectedHostelId is provided
  useEffect(() => {
    if (preSelectedHostelId && availableHostels.length > 0) {
      const hostel = availableHostels.find(h => h.id === preSelectedHostelId);
      if (hostel) {
        setSelectedHostel(hostel);
        setCurrentStep(2); // Skip hostel selection
        loadFloorsForHostel(hostel.id);
      }
    }
  }, [preSelectedHostelId, availableHostels]);

  // Auto-select floor if preSelectedFloorId is provided
  useEffect(() => {
    if (preSelectedFloorId && availableFloors.length > 0) {
      const floor = availableFloors.find(f => f.id === preSelectedFloorId);
      if (floor) {
        setSelectedFloor(floor);
        setValue('floorId', floor.id);
        setValue('floor', floor.floorNumber);
        setCurrentStep(3); // Skip floor selection
      }
    }
  }, [preSelectedFloorId, availableFloors, setValue]);

  // Auto-generate bed configurations when capacity changes
  useEffect(() => {
    if (watchedCapacity && watchedRoomNumber) {
      const newBedConfigs: BedConfig[] = [];
      for (let i = 0; i < watchedCapacity; i++) {
        newBedConfigs.push({
          bedNumber: `${watchedRoomNumber}${String.fromCharCode(65 + i)}`,
          bedType: 'single',
          size: 'single',
          hasLocker: true,
          lockerNumber: `${watchedRoomNumber}${String.fromCharCode(65 + i)}`
        });
      }
      setBedConfigs(newBedConfigs);
    }
  }, [watchedCapacity, watchedRoomNumber]);

  const loadHostels = async () => {
    setIsLoadingHostels(true);
    try {
      const hostels = getHostelsByOwnerId(ownerId);
      setAvailableHostels(hostels);
      
      if (hostels.length === 1 && !preSelectedHostelId) {
        // Auto-select if only one hostel
        setSelectedHostel(hostels[0]);
        setCurrentStep(2);
        loadFloorsForHostel(hostels[0].id);
      }
    } catch (error) {
      console.error('Error loading hostels:', error);
      toast({
        title: "Error",
        description: "Failed to load hostels",
        variant: "destructive"
      });
    } finally {
      setIsLoadingHostels(false);
    }
  };

  const loadFloorsForHostel = (hostelId: string) => {
    const floors = getFloorsByHostelId(hostelId);
    setAvailableFloors(floors);
    
    if (floors.length === 1) {
      // Auto-select if only one floor
      setSelectedFloor(floors[0]);
      setValue('floorId', floors[0].id);
      setValue('floor', floors[0].floorNumber);
      setCurrentStep(3);
    }
  };

  const handleHostelSelect = (hostelId: string) => {
    const hostel = availableHostels.find(h => h.id === hostelId);
    if (hostel) {
      setSelectedHostel(hostel);
      setSelectedFloor(null);
      setValue('floorId', '');
      loadFloorsForHostel(hostel.id);
    }
  };

  const handleFloorSelect = (floorId: string) => {
    const floor = availableFloors.find(f => f.id === floorId);
    if (floor) {
      setSelectedFloor(floor);
      setValue('floorId', floor.id);
      setValue('floor', floor.floorNumber);
    }
  };

  const handleAmenityToggle = (amenity: string) => {
    setSelectedAmenities(prev => 
      prev.includes(amenity) 
        ? prev.filter(a => a !== amenity)
        : [...prev, amenity]
    );
  };

  const availableAmenities = [
    'AC', 'WiFi', 'Attached Bathroom', 'Shared Bathroom', 'Study Table', 
    'Wardrobe', 'TV', 'Refrigerator', 'Balcony', 'Window', 'Fan', 'Locker'
  ];

  const validateStep = (step: number): boolean => {
    if (step === 1 && !selectedHostel) {
      toast({
        title: "Selection Required",
        description: "Please select a hostel",
        variant: "destructive"
      });
      return false;
    }
    
    if (step === 2 && !selectedFloor) {
      toast({
        title: "Selection Required", 
        description: "Please select a floor",
        variant: "destructive"
      });
      return false;
    }
    
    return true;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => prev - 1);
  };

  const onSubmit = async (data: AddRoomFormData) => {
    if (!selectedHostel || !selectedFloor) {
      toast({
        title: "Error",
        description: "Please select hostel and floor",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const roomData = {
        roomNumber: data.roomNumber,
        roomType: data.roomType,
        floor: data.floor,
        capacity: data.capacity,
        amenities: selectedAmenities,
        beds: bedConfigs.map(bed => ({
          bedNumber: bed.bedNumber,
          bedType: bed.bedType,
          status: 'available' as const,
          size: bed.size,
          hasLocker: bed.hasLocker,
          lockerNumber: bed.lockerNumber
        })),
        pricing: {
          dailyRate: data.dailyRate,
          weeklyRate: data.weeklyRate,
          monthlyRate: data.monthlyRate,
          securityDeposit: data.securityDeposit,
          cleaningFee: data.cleaningFee
        },
        details: {
          area: data.area,
          hasWindow: true,
          hasBalcony: false,
          hasAttachedBathroom: selectedAmenities.includes('Attached Bathroom'),
          bathroomType: selectedAmenities.includes('Attached Bathroom') ? 'private' : 'shared',
          hasAC: selectedAmenities.includes('AC'),
          hasFan: selectedAmenities.includes('Fan'),
          hasWiFi: selectedAmenities.includes('WiFi'),
          hasTV: selectedAmenities.includes('TV'),
          hasRefrigerator: selectedAmenities.includes('Refrigerator'),
          hasWardrobe: selectedAmenities.includes('Wardrobe'),
          hasStudyTable: selectedAmenities.includes('Study Table'),
          hasChair: true,
          description: data.description || ''
        },
        photos: [],
        reason: data.reason,
        expectedOccupancyIncrease: data.expectedOccupancyIncrease
      };

      const result = await submitAddRoomRequest(selectedHostel.id, ownerId, roomData);

      if (result.success) {
        toast({
          title: "Room Addition Request Submitted",
          description: `Your request to add room ${data.roomNumber} to ${selectedHostel.name} Floor ${selectedFloor.floorNumber} has been submitted for admin approval.`,
        });
        onRoomAdded();
        handleClose();
      } else {
        toast({
          title: "Submission Failed",
          description: result.error || "Failed to submit room addition request",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error submitting room request:', error);
      toast({
        title: "Error",
        description: "Failed to submit room addition request",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    onClose();
    setCurrentStep(1);
    setSelectedHostel(null);
    setSelectedFloor(null);
    setAvailableFloors([]);
    reset();
  };

  // Step 1: Hostel Selection
  const renderHostelSelection = () => (
    <div className="space-y-4">
      <div className="text-center">
        <Building2 className="mx-auto h-12 w-12 text-blue-500" />
        <h3 className="mt-2 text-lg font-medium">Select Hostel</h3>
        <p className="text-sm text-gray-500">Choose which hostel to add the room to</p>
      </div>

      {isLoadingHostels ? (
        <div className="text-center py-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="text-sm text-gray-500 mt-2">Loading hostels...</p>
        </div>
      ) : availableHostels.length === 0 ? (
        <div className="text-center py-6">
          <AlertCircle className="mx-auto h-8 w-8 text-gray-400" />
          <h4 className="mt-2 text-sm font-medium text-gray-900">No Approved Hostels</h4>
          <p className="mt-1 text-sm text-gray-500">
            You don't have any approved hostels yet. Submit a hostel registration request first.
          </p>
        </div>
      ) : (
        <div className="space-y-3">
          <Label>Select Hostel *</Label>
          <Select value={selectedHostel?.id || ''} onValueChange={handleHostelSelect}>
            <SelectTrigger>
              <SelectValue placeholder="Choose a hostel" />
            </SelectTrigger>
            <SelectContent>
              {availableHostels.map((hostel) => (
                <SelectItem key={hostel.id} value={hostel.id}>
                  <div className="flex items-center space-x-2">
                    <Building2 className="h-4 w-4" />
                    <span>{hostel.name}</span>
                    <span className="text-muted-foreground">
                      - {hostel.city} ({hostel.totalFloors} floors)
                    </span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {selectedHostel && (
            <Card className="border-green-200 bg-green-50">
              <CardContent className="pt-4">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <div>
                    <h4 className="font-medium text-green-900">{selectedHostel.name}</h4>
                    <p className="text-sm text-green-700">
                      {selectedHostel.address}, {selectedHostel.city}
                    </p>
                    <p className="text-xs text-green-600 mt-1">
                      ID: {selectedHostel.id} • {selectedHostel.totalFloors} floors • {selectedHostel.totalRooms} rooms
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );

  // Step 2: Floor Selection
  const renderFloorSelection = () => (
    <div className="space-y-4">
      <div className="text-center">
        <Layers className="mx-auto h-12 w-12 text-green-500" />
        <h3 className="mt-2 text-lg font-medium">Select Floor</h3>
        <p className="text-sm text-gray-500">Choose which floor to add the room to</p>
      </div>

      {availableFloors.length === 0 ? (
        <div className="text-center py-6">
          <AlertCircle className="mx-auto h-8 w-8 text-gray-400" />
          <h4 className="mt-2 text-sm font-medium text-gray-900">No Floors Available</h4>
          <p className="mt-1 text-sm text-gray-500">
            This hostel doesn't have any floors yet. Add a floor first before creating rooms.
          </p>
        </div>
      ) : (
        <div className="space-y-3">
          <Label>Select Floor *</Label>
          <Select value={selectedFloor?.id || ''} onValueChange={handleFloorSelect}>
            <SelectTrigger>
              <SelectValue placeholder="Choose a floor" />
            </SelectTrigger>
            <SelectContent>
              {availableFloors.map((floor) => (
                <SelectItem key={floor.id} value={floor.id}>
                  <div className="flex items-center space-x-2">
                    <Layers className="h-4 w-4" />
                    <span>Floor {floor.floorNumber}</span>
                    <span className="text-muted-foreground">
                      ({floor.floorType} • {floor.totalRooms} rooms)
                    </span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {selectedFloor && (
            <Card className="border-purple-200 bg-purple-50">
              <CardContent className="pt-4">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-purple-600" />
                  <div>
                    <h4 className="font-medium text-purple-900">
                      Floor {selectedFloor.floorNumber} ({selectedFloor.floorType})
                    </h4>
                    <p className="text-sm text-purple-700">
                      Current: {selectedFloor.totalRooms} rooms • {selectedFloor.totalBeds} beds
                    </p>
                    <p className="text-xs text-purple-600 mt-1">
                      Floor ID: {selectedFloor.id} • Hostel: {selectedHostel?.name}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );

  // Step 3: Room Details Form
  const renderRoomForm = () => (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="basic">Basic Info</TabsTrigger>
          <TabsTrigger value="pricing">Pricing</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="roomNumber">Room Number *</Label>
              <Input
                id="roomNumber"
                {...register('roomNumber')}
                placeholder="e.g., 201"
              />
              {errors.roomNumber && (
                <p className="text-sm text-red-600 mt-1">{errors.roomNumber.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="roomType">Room Type *</Label>
              <Select onValueChange={(value: RoomType) => setValue('roomType', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select room type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="single">Single</SelectItem>
                  <SelectItem value="double">Double</SelectItem>
                  <SelectItem value="triple">Triple</SelectItem>
                  <SelectItem value="quad">Quad</SelectItem>
                  <SelectItem value="dormitory">Dormitory</SelectItem>
                  <SelectItem value="suite">Suite</SelectItem>
                </SelectContent>
              </Select>
              {errors.roomType && (
                <p className="text-sm text-red-600 mt-1">{errors.roomType.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="capacity">Bed Capacity *</Label>
              <Input
                id="capacity"
                type="number"
                {...register('capacity', { valueAsNumber: true })}
                min="1"
                max="20"
              />
              {errors.capacity && (
                <p className="text-sm text-red-600 mt-1">{errors.capacity.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="area">Area (sq ft) *</Label>
              <Input
                id="area"
                type="number"
                {...register('area', { valueAsNumber: true })}
                min="1"
              />
              {errors.area && (
                <p className="text-sm text-red-600 mt-1">{errors.area.message}</p>
              )}
            </div>
          </div>

          <div>
            <Label>Amenities</Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {availableAmenities.map((amenity) => (
                <Badge
                  key={amenity}
                  variant={selectedAmenities.includes(amenity) ? "default" : "outline"}
                  className="cursor-pointer"
                  onClick={() => handleAmenityToggle(amenity)}
                >
                  {amenity}
                </Badge>
              ))}
            </div>
          </div>

          <div>
            <Label htmlFor="reason">Reason for Adding Room *</Label>
            <Textarea
              id="reason"
              {...register('reason')}
              placeholder="Explain why this room is needed"
              rows={3}
            />
            {errors.reason && (
              <p className="text-sm text-red-600 mt-1">{errors.reason.message}</p>
            )}
          </div>
        </TabsContent>

        <TabsContent value="pricing" className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="dailyRate">Daily Rate (₹) *</Label>
              <Input
                id="dailyRate"
                type="number"
                {...register('dailyRate', { valueAsNumber: true })}
                min="1"
              />
              {errors.dailyRate && (
                <p className="text-sm text-red-600 mt-1">{errors.dailyRate.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="weeklyRate">Weekly Rate (₹) *</Label>
              <Input
                id="weeklyRate"
                type="number"
                {...register('weeklyRate', { valueAsNumber: true })}
                min="1"
              />
              {errors.weeklyRate && (
                <p className="text-sm text-red-600 mt-1">{errors.weeklyRate.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="monthlyRate">Monthly Rate (₹) *</Label>
              <Input
                id="monthlyRate"
                type="number"
                {...register('monthlyRate', { valueAsNumber: true })}
                min="1"
              />
              {errors.monthlyRate && (
                <p className="text-sm text-red-600 mt-1">{errors.monthlyRate.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="securityDeposit">Security Deposit (₹) *</Label>
              <Input
                id="securityDeposit"
                type="number"
                {...register('securityDeposit', { valueAsNumber: true })}
                min="0"
              />
              {errors.securityDeposit && (
                <p className="text-sm text-red-600 mt-1">{errors.securityDeposit.message}</p>
              )}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="features" className="space-y-4">
          <div>
            <Label htmlFor="description">Room Description</Label>
            <Textarea
              id="description"
              {...register('description')}
              placeholder="Describe the room features and layout"
              rows={3}
            />
          </div>

          {/* Bed Configuration Preview */}
          {bedConfigs.length > 0 && (
            <div>
              <Label>Bed Configuration Preview</Label>
              <div className="grid gap-2 mt-2">
                {bedConfigs.map((bed, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <span className="font-medium">Bed {bed.bedNumber}</span>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">{bed.bedType}</Badge>
                      {bed.hasLocker && (
                        <Badge variant="outline">Locker {bed.lockerNumber}</Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Assignment Summary */}
          {selectedHostel && selectedFloor && (
            <Card className="border-blue-200 bg-blue-50">
              <CardHeader>
                <CardTitle className="text-sm">Assignment Summary</CardTitle>
              </CardHeader>
              <CardContent className="text-sm space-y-1">
                <div><strong>Hostel:</strong> {selectedHostel.name} (ID: {selectedHostel.id})</div>
                <div><strong>Floor:</strong> {selectedFloor.floorNumber} ({selectedFloor.floorType}) (ID: {selectedFloor.id})</div>
                <div><strong>Room:</strong> {watchedRoomNumber || 'Not specified'}</div>
                <div><strong>Capacity:</strong> {watchedCapacity} beds</div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </form>
  );

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Home className="h-5 w-5" />
            Add New Room {selectedHostel && selectedFloor && `- ${selectedHostel.name} Floor ${selectedFloor.floorNumber}`}
          </DialogTitle>
          <DialogDescription>
            Submit a request to add a new room to your hostel. This request will be reviewed by the admin.
          </DialogDescription>
        </DialogHeader>

        {/* Progress Steps */}
        <div className="flex items-center justify-center space-x-4 py-4">
          {[1, 2, 3].map((step) => (
            <div key={step} className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                currentStep >= step 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-gray-200 text-gray-600'
              }`}>
                {step}
              </div>
              {step < 3 && (
                <ArrowRight className={`h-4 w-4 mx-2 ${
                  currentStep > step ? 'text-blue-500' : 'text-gray-400'
                }`} />
              )}
            </div>
          ))}
        </div>

        {/* Step Content */}
        <div className="min-h-[400px]">
          {currentStep === 1 && renderHostelSelection()}
          {currentStep === 2 && renderFloorSelection()}
          {currentStep === 3 && renderRoomForm()}
        </div>

        <DialogFooter>
          <div className="flex justify-between w-full">
            <div>
              {currentStep > 1 && (
                <Button variant="outline" onClick={handlePrevious}>
                  Previous
                </Button>
              )}
            </div>
            <div className="space-x-2">
              <Button variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              {currentStep < 3 ? (
                <Button 
                  onClick={handleNext}
                  disabled={
                    (currentStep === 1 && !selectedHostel) ||
                    (currentStep === 2 && !selectedFloor)
                  }
                >
                  Next
                </Button>
              ) : (
                <Button 
                  onClick={handleSubmit(onSubmit)} 
                  disabled={isSubmitting || !selectedHostel || !selectedFloor}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Submitting...
                    </>
                  ) : (
                    'Submit Request'
                  )}
                </Button>
              )}
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
