import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Building2,
  Layers,
  Home,
  Bed,
  ArrowRight,
  CheckCircle,
  Circle,
  Play,
  Users,
  Plus,
  Settings
} from 'lucide-react';

interface WorkflowStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  completed: boolean;
  details: string[];
  codeExample?: string;
}

export const OwnerWorkflowGuide: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());

  const workflowSteps: WorkflowStep[] = [
    {
      id: 'select-hostel',
      title: 'Select Hostel from Portfolio',
      description: 'Choose which hostel property to manage',
      icon: <Building2 className="h-5 w-5" />,
      completed: false,
      details: [
        'Owner logs into the system',
        'Navigates to Room Management section',
        'HostelSelector component loads owner\'s properties',
        'System calls getHostelsByOwnerId(ownerId)',
        'Dropdown shows all hostels owned by this user',
        'Owner selects desired hostel',
        'All subsequent data filters by selected hostelId'
      ],
      codeExample: `
// Data filtering by owner
const ownerHostels = getHostelsByOwnerId(ownerId);
// Result: [hostel1, hostel2, hostel3] for this owner

// When hostel selected
onHostelChange(hostelId, hostelData);
// Triggers data reload for selected hostel
`
    },
    {
      id: 'view-floors',
      title: 'View Available Floors',
      description: 'See all floors in the selected hostel',
      icon: <Layers className="h-5 w-5" />,
      completed: false,
      details: [
        'System automatically loads floors for selected hostel',
        'FloorFilter component calls getFloorsByHostelId(hostelId)',
        'Displays floors with statistics and room counts',
        'Owner can filter rooms by specific floors',
        'Floor-wise occupancy and revenue shown',
        'Option to add new floors (requires admin approval)'
      ],
      codeExample: `
// Load floors for selected hostel
const floors = getFloorsByHostelId(selectedHostel.id);
// Result: [floor1, floor2, floor3] for this hostel

// Floor data structure
{
  id: 'floor_001',
  hostelId: '1',
  floorNumber: 1,
  totalRooms: 4,
  totalBeds: 12,
  currentOccupancy: 9
}
`
    },
    {
      id: 'manage-rooms',
      title: 'Add/Edit Rooms on Floors',
      description: 'Create new rooms or modify existing ones',
      icon: <Home className="h-5 w-5" />,
      completed: false,
      details: [
        'Owner clicks "Add Room" button',
        'AddRoomDialog opens with floor selection',
        'Available floors loaded from getAvailableFloorsForRooms()',
        'Owner selects target floor for new room',
        'Room details entered (type, capacity, amenities)',
        'System assigns room to floor via floorId',
        'Room also linked to hostel via hostelId',
        'Request submitted for admin approval'
      ],
      codeExample: `
// Room creation with floor assignment
const newRoom = {
  hostelId: selectedHostel.id,
  floorId: selectedFloor.id,
  roomNumber: '201',
  roomType: 'double',
  floor: 2,
  capacity: 2
};

// Room filtering by floor
const floorRooms = getRoomsByFloorId(floorId);
// Returns only rooms assigned to this floor
`
    },
    {
      id: 'manage-beds',
      title: 'Add/Manage Beds in Rooms',
      description: 'Configure bed arrangements within rooms',
      icon: <Bed className="h-5 w-5" />,
      completed: false,
      details: [
        'Owner selects specific room to manage',
        'AddBedDialog opens for selected room',
        'System checks room capacity vs current bed count',
        'Owner specifies bed details (type, number, features)',
        'Bed automatically linked to room via roomId',
        'Bed numbers must be unique within room',
        'Occupancy status can be set (available/occupied)',
        'Locker assignments and amenities configured'
      ],
      codeExample: `
// Bed creation within room
const newBed = {
  roomId: selectedRoom.id,
  bedNumber: '201A',
  bedType: 'single',
  status: 'available',
  hasLocker: true,
  lockerNumber: '201A'
};

// Capacity validation
if (room.beds.length >= room.capacity) {
  throw new Error('Room at maximum capacity');
}
`
    },
    {
      id: 'monitor-occupancy',
      title: 'Monitor Occupancy & Statistics',
      description: 'Track bed assignments and revenue',
      icon: <Users className="h-5 w-5" />,
      completed: false,
      details: [
        'Real-time occupancy tracking across hierarchy',
        'Bed-level: individual occupancy status',
        'Room-level: room occupancy percentage',
        'Floor-level: floor occupancy and revenue',
        'Hostel-level: overall property performance',
        'Statistics automatically aggregate upward',
        'Revenue calculations based on occupied beds',
        'Availability tracking for new bookings'
      ],
      codeExample: `
// Hierarchical statistics calculation
const bedStats = room.beds.filter(bed => bed.status === 'occupied');
const roomOccupancy = (bedStats.length / room.capacity) * 100;

const floorStats = floors.map(floor => ({
  floorId: floor.id,
  occupancyRate: calculateFloorOccupancy(floor.id),
  revenue: calculateFloorRevenue(floor.id)
}));
`
    },
    {
      id: 'transfer-management',
      title: 'Transfer & Reassignment',
      description: 'Move rooms between floors or hostels',
      icon: <Settings className="h-5 w-5" />,
      completed: false,
      details: [
        'Room-to-Floor Transfer: Update room.floorId',
        'Room-to-Hostel Transfer: Update room.hostelId',
        'Bed Reassignment: Update bed.roomId',
        'Occupancy Transfer: Update bed.occupiedBy',
        'All transfers require admin approval',
        'System maintains data integrity',
        'Historical records preserved',
        'Notifications sent to affected parties'
      ],
      codeExample: `
// Room transfer between floors
const transferRoom = (roomId, newFloorId) => {
  const room = getRoomById(roomId);
  room.floorId = newFloorId;
  room.floor = getFloorById(newFloorId).floorNumber;
  // Update floor statistics
  updateFloorStatistics(room.hostelId);
};

// Bed occupancy assignment
const assignBed = (bedId, memberId) => {
  const bed = getBedById(bedId);
  bed.status = 'occupied';
  bed.occupiedBy = memberId;
  bed.updatedAt = new Date().toISOString();
};
`
    }
  ];

  const markStepComplete = (stepId: string) => {
    setCompletedSteps(prev => new Set([...prev, stepId]));
  };

  const isStepCompleted = (stepId: string) => completedSteps.has(stepId);

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h1 className="text-2xl font-bold">Owner Panel Workflow Guide</h1>
        <p className="text-gray-600 mt-2">
          Step-by-step process for managing hostels, floors, rooms, and beds
        </p>
      </div>

      {/* Progress Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Workflow Progress</CardTitle>
          <CardDescription>
            Complete workflow for room and bed assignment management
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <span className="text-sm font-medium">
              {completedSteps.size} of {workflowSteps.length} steps completed
            </span>
            <span className="text-sm text-gray-500">
              {Math.round((completedSteps.size / workflowSteps.length) * 100)}% complete
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(completedSteps.size / workflowSteps.length) * 100}%` }}
            />
          </div>
        </CardContent>
      </Card>

      {/* Workflow Steps */}
      <div className="space-y-4">
        {workflowSteps.map((step, index) => (
          <Card key={step.id} className={`${currentStep === index ? 'ring-2 ring-blue-500' : ''}`}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-full ${
                    isStepCompleted(step.id) 
                      ? 'bg-green-100 text-green-600' 
                      : currentStep === index 
                        ? 'bg-blue-100 text-blue-600'
                        : 'bg-gray-100 text-gray-600'
                  }`}>
                    {isStepCompleted(step.id) ? <CheckCircle className="h-5 w-5" /> : step.icon}
                  </div>
                  <div>
                    <CardTitle className="text-lg">
                      Step {index + 1}: {step.title}
                    </CardTitle>
                    <CardDescription>{step.description}</CardDescription>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {isStepCompleted(step.id) && (
                    <Badge variant="default">Completed</Badge>
                  )}
                  {currentStep === index && (
                    <Badge variant="outline">Current</Badge>
                  )}
                  <Button
                    size="sm"
                    variant={currentStep === index ? "default" : "outline"}
                    onClick={() => setCurrentStep(index)}
                  >
                    {currentStep === index ? <Play className="h-3 w-3" /> : <Circle className="h-3 w-3" />}
                  </Button>
                </div>
              </div>
            </CardHeader>
            
            {currentStep === index && (
              <CardContent className="space-y-4">
                {/* Step Details */}
                <div>
                  <h4 className="font-medium mb-2">Process Details:</h4>
                  <ul className="space-y-1">
                    {step.details.map((detail, idx) => (
                      <li key={idx} className="text-sm flex items-start gap-2">
                        <ArrowRight className="h-3 w-3 mt-0.5 text-gray-400 flex-shrink-0" />
                        {detail}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Code Example */}
                {step.codeExample && (
                  <div>
                    <h4 className="font-medium mb-2">Technical Implementation:</h4>
                    <pre className="bg-gray-900 text-gray-100 p-3 rounded-lg text-xs overflow-x-auto">
                      <code>{step.codeExample.trim()}</code>
                    </pre>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex items-center gap-2 pt-2">
                  <Button
                    size="sm"
                    onClick={() => markStepComplete(step.id)}
                    disabled={isStepCompleted(step.id)}
                  >
                    {isStepCompleted(step.id) ? (
                      <>
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Completed
                      </>
                    ) : (
                      <>
                        <Circle className="h-3 w-3 mr-1" />
                        Mark Complete
                      </>
                    )}
                  </Button>
                  {index < workflowSteps.length - 1 && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setCurrentStep(index + 1)}
                    >
                      Next Step
                      <ArrowRight className="h-3 w-3 ml-1" />
                    </Button>
                  )}
                </div>
              </CardContent>
            )}
          </Card>
        ))}
      </div>

      {/* Summary */}
      {completedSteps.size === workflowSteps.length && (
        <Card className="border-green-200 bg-green-50">
          <CardHeader>
            <CardTitle className="text-green-800 flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Workflow Complete!
            </CardTitle>
            <CardDescription className="text-green-700">
              You've successfully learned the complete room and bed assignment workflow.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-sm text-green-700">
              <p className="mb-2">Key takeaways:</p>
              <ul className="space-y-1">
                <li>• Hierarchical data structure: Hostel → Floor → Room → Bed</li>
                <li>• Foreign key relationships maintain data integrity</li>
                <li>• Dynamic filtering based on selections</li>
                <li>• Approval workflows for structural changes</li>
                <li>• Real-time statistics and occupancy tracking</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
