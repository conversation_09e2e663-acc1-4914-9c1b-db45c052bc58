import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  Clock, 
  Utensils,
  Shield,
  Wifi,
  Car,
  Dumbbell,
  Waves,
  Coffee,
  Tv,
  Wind,
  Zap,
  Phone,
  MapPin,
  Mail,
  Calendar,
  Users,
  AlertTriangle,
  Info,
  CheckCircle,
  Download,
  FileText
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { mockHostels, getHostelById } from '@/data/mockData';

// Mock hostel information data
interface HostelInfo {
  id: string;
  name: string;
  address: string;
  contact: {
    phone: string;
    email: string;
    emergencyContact: string;
  };
  foodTimings: {
    breakfast: { start: string; end: string };
    lunch: { start: string; end: string };
    dinner: { start: string; end: string };
  };
  weeklyMenu: {
    [key: string]: {
      breakfast: string[];
      lunch: string[];
      dinner: string[];
    };
  };
  rules: {
    category: string;
    rules: string[];
  }[];
  amenities: {
    name: string;
    description: string;
    available: boolean;
    timings?: string;
  }[];
  emergencyProcedures: {
    type: string;
    procedure: string[];
  }[];
}

const mockHostelInfo: HostelInfo = {
  id: '1',
  name: 'Urban Stay Hostel',
  address: '123 College Road, Andheri, Mumbai, Maharashtra 400001',
  contact: {
    phone: '+91 9876543210',
    email: '<EMAIL>',
    emergencyContact: '+91 9876543211'
  },
  foodTimings: {
    breakfast: { start: '07:00', end: '10:00' },
    lunch: { start: '12:00', end: '15:00' },
    dinner: { start: '19:00', end: '22:00' }
  },
  weeklyMenu: {
    Monday: {
      breakfast: ['Poha', 'Bread & Butter', 'Tea/Coffee'],
      lunch: ['Dal Rice', 'Roti', 'Vegetable Curry', 'Pickle'],
      dinner: ['Rajma Rice', 'Roti', 'Salad', 'Curd']
    },
    Tuesday: {
      breakfast: ['Upma', 'Banana', 'Tea/Coffee'],
      lunch: ['Chole Rice', 'Roti', 'Aloo Sabzi', 'Pickle'],
      dinner: ['Dal Rice', 'Roti', 'Paneer Curry', 'Salad']
    },
    Wednesday: {
      breakfast: ['Paratha', 'Curd', 'Tea/Coffee'],
      lunch: ['Sambar Rice', 'Roti', 'Bhindi Sabzi', 'Pickle'],
      dinner: ['Biryani', 'Raita', 'Boiled Egg', 'Salad']
    },
    Thursday: {
      breakfast: ['Idli Sambhar', 'Coconut Chutney', 'Tea/Coffee'],
      lunch: ['Dal Rice', 'Roti', 'Cauliflower Curry', 'Pickle'],
      dinner: ['Pulao', 'Dal', 'Mixed Vegetables', 'Curd']
    },
    Friday: {
      breakfast: ['Dosa', 'Sambhar', 'Tea/Coffee'],
      lunch: ['Rajma Rice', 'Roti', 'Cabbage Sabzi', 'Pickle'],
      dinner: ['Fried Rice', 'Manchurian', 'Soup', 'Salad']
    },
    Saturday: {
      breakfast: ['Puri Bhaji', 'Tea/Coffee'],
      lunch: ['Curd Rice', 'Roti', 'Dal', 'Vegetable'],
      dinner: ['Chicken Curry', 'Rice', 'Roti', 'Salad']
    },
    Sunday: {
      breakfast: ['Pancakes', 'Fruits', 'Tea/Coffee'],
      lunch: ['Special Thali', 'Sweet Dish'],
      dinner: ['Pizza', 'Garlic Bread', 'Cold Drink']
    }
  },
  rules: [
    {
      category: 'General Rules',
      rules: [
        'Maintain cleanliness in your room and common areas',
        'No smoking or alcohol consumption inside the hostel premises',
        'Visitors are allowed only between 10 AM to 8 PM',
        'All visitors must register at the reception',
        'No loud music or noise after 10 PM (Quiet Hours)',
        'Respect other residents and their privacy'
      ]
    },
    {
      category: 'Room Rules',
      rules: [
        'Keep your room locked when not present',
        'Do not share room keys with unauthorized persons',
        'Report any maintenance issues immediately',
        'No cooking or heating appliances in rooms',
        'Maximum 2 guests allowed in room at a time',
        'Bed sheets and pillows to be changed weekly'
      ]
    },
    {
      category: 'Kitchen & Dining Rules',
      rules: [
        'Clean utensils after use',
        'No outside food in dining area during meal times',
        'Inform kitchen staff about dietary restrictions in advance',
        'Food wastage is strictly prohibited',
        'Kitchen closes at 11 PM',
        'Personal utensils must be labeled'
      ]
    },
    {
      category: 'Safety & Security',
      rules: [
        'Always carry your ID card',
        'Report suspicious activities immediately',
        'Do not prop open security doors',
        'Emergency exits must be kept clear',
        'No unauthorized entry to restricted areas',
        'CCTV cameras are installed for security purposes'
      ]
    }
  ],
  amenities: [
    { name: 'WiFi', description: 'High-speed internet throughout the hostel', available: true, timings: '24/7' },
    { name: 'Air Conditioning', description: 'AC in all rooms and common areas', available: true, timings: '24/7' },
    { name: 'Laundry', description: 'Washing machine and dryer facilities', available: true, timings: '6 AM - 10 PM' },
    { name: 'Gym', description: 'Fully equipped fitness center', available: true, timings: '5 AM - 11 PM' },
    { name: 'Common Kitchen', description: 'Shared kitchen with basic appliances', available: true, timings: '6 AM - 11 PM' },
    { name: 'Study Room', description: 'Quiet study area with desks and chairs', available: true, timings: '24/7' },
    { name: 'Recreation Room', description: 'TV, games, and entertainment area', available: true, timings: '6 AM - 11 PM' },
    { name: 'Parking', description: 'Secure parking for bikes and cars', available: true, timings: '24/7' },
    { name: 'Swimming Pool', description: 'Outdoor swimming pool', available: false },
    { name: 'Cafeteria', description: 'In-house cafeteria with snacks', available: true, timings: '7 AM - 11 PM' }
  ],
  emergencyProcedures: [
    {
      type: 'Fire Emergency',
      procedure: [
        'Immediately evacuate the building using nearest exit',
        'Do not use elevators during fire emergency',
        'Gather at the designated assembly point in the parking area',
        'Call fire department: 101',
        'Inform hostel management immediately'
      ]
    },
    {
      type: 'Medical Emergency',
      procedure: [
        'Call emergency services: 108',
        'Inform hostel management immediately',
        'Provide first aid if trained to do so',
        'Do not move seriously injured persons',
        'Keep emergency contact numbers handy'
      ]
    },
    {
      type: 'Security Emergency',
      procedure: [
        'Call police: 100',
        'Inform hostel security immediately',
        'Stay in a safe location',
        'Do not confront intruders',
        'Report incident details to management'
      ]
    }
  ]
};

export const HostelInformation: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');

  const hostelInfo = mockHostelInfo; // In real app, fetch based on user's hostel

  const getMealIcon = (meal: string) => {
    switch (meal) {
      case 'breakfast': return <Coffee className="h-4 w-4" />;
      case 'lunch': return <Utensils className="h-4 w-4" />;
      case 'dinner': return <Utensils className="h-4 w-4" />;
      default: return <Utensils className="h-4 w-4" />;
    }
  };

  const getAmenityIcon = (amenity: string) => {
    switch (amenity.toLowerCase()) {
      case 'wifi': return <Wifi className="h-5 w-5" />;
      case 'air conditioning': return <Wind className="h-5 w-5" />;
      case 'laundry': return <Zap className="h-5 w-5" />;
      case 'gym': return <Dumbbell className="h-5 w-5" />;
      case 'parking': return <Car className="h-5 w-5" />;
      case 'swimming pool': return <Waves className="h-5 w-5" />;
      case 'recreation room': return <Tv className="h-5 w-5" />;
      default: return <CheckCircle className="h-5 w-5" />;
    }
  };

  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Hostel Information</h1>
          <p className="text-muted-foreground">
            Complete information about your hostel facilities and services
          </p>
        </div>
        <Button variant="outline">
          <Download className="mr-2 h-4 w-4" />
          Download Handbook
        </Button>
      </div>

      {/* Hostel Overview Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MapPin className="h-5 w-5" />
            <span>{hostelInfo.name}</span>
          </CardTitle>
          <CardDescription>{hostelInfo.address}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center space-x-2">
              <Phone className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{hostelInfo.contact.phone}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Mail className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{hostelInfo.contact.email}</span>
            </div>
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-4 w-4 text-red-500" />
              <span className="text-sm">Emergency: {hostelInfo.contact.emergencyContact}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="food">Food & Menu</TabsTrigger>
          <TabsTrigger value="rules">Rules & Guidelines</TabsTrigger>
          <TabsTrigger value="emergency">Emergency Info</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Food Timings */}
          <Card>
            <CardHeader>
              <CardTitle>Meal Timings</CardTitle>
              <CardDescription>Daily meal service hours</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {Object.entries(hostelInfo.foodTimings).map(([meal, timing]) => (
                  <div key={meal} className="flex items-center space-x-3 p-3 border rounded-lg">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      {getMealIcon(meal)}
                    </div>
                    <div>
                      <h4 className="font-medium capitalize">{meal}</h4>
                      <p className="text-sm text-muted-foreground">
                        {formatTime(timing.start)} - {formatTime(timing.end)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Amenities */}
          <Card>
            <CardHeader>
              <CardTitle>Amenities & Facilities</CardTitle>
              <CardDescription>Available facilities and their operating hours</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {hostelInfo.amenities.map((amenity, index) => (
                  <div key={index} className="flex items-start space-x-3 p-3 border rounded-lg">
                    <div className={`p-2 rounded-lg ${amenity.available ? 'bg-green-100' : 'bg-gray-100'}`}>
                      {getAmenityIcon(amenity.name)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h4 className="font-medium">{amenity.name}</h4>
                        <Badge variant={amenity.available ? 'default' : 'secondary'}>
                          {amenity.available ? 'Available' : 'Not Available'}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">{amenity.description}</p>
                      {amenity.timings && (
                        <p className="text-xs text-blue-600 mt-1">
                          <Clock className="h-3 w-3 inline mr-1" />
                          {amenity.timings}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Food & Menu Tab */}
        <TabsContent value="food" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Weekly Menu</CardTitle>
              <CardDescription>Complete weekly meal plan</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {Object.entries(hostelInfo.weeklyMenu).map(([day, meals]) => (
                  <div key={day} className="border rounded-lg p-4">
                    <h3 className="font-semibold text-lg mb-3 flex items-center">
                      <Calendar className="h-4 w-4 mr-2" />
                      {day}
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {Object.entries(meals).map(([mealType, items]) => (
                        <div key={mealType} className="space-y-2">
                          <h4 className="font-medium capitalize flex items-center">
                            {getMealIcon(mealType)}
                            <span className="ml-2">{mealType}</span>
                          </h4>
                          <ul className="text-sm space-y-1">
                            {items.map((item, index) => (
                              <li key={index} className="flex items-center">
                                <CheckCircle className="h-3 w-3 text-green-500 mr-2" />
                                {item}
                              </li>
                            ))}
                          </ul>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Rules Tab */}
        <TabsContent value="rules" className="space-y-6">
          {hostelInfo.rules.map((ruleCategory, index) => (
            <Card key={index}>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Shield className="h-5 w-5 mr-2" />
                  {ruleCategory.category}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {ruleCategory.rules.map((rule, ruleIndex) => (
                    <li key={ruleIndex} className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span className="text-sm">{rule}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        {/* Emergency Tab */}
        <TabsContent value="emergency" className="space-y-6">
          {hostelInfo.emergencyProcedures.map((emergency, index) => (
            <Card key={index}>
              <CardHeader>
                <CardTitle className="flex items-center text-red-600">
                  <AlertTriangle className="h-5 w-5 mr-2" />
                  {emergency.type}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ol className="space-y-2">
                  {emergency.procedure.map((step, stepIndex) => (
                    <li key={stepIndex} className="flex items-start">
                      <span className="bg-red-100 text-red-800 rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium mr-3 mt-0.5 flex-shrink-0">
                        {stepIndex + 1}
                      </span>
                      <span className="text-sm">{step}</span>
                    </li>
                  ))}
                </ol>
              </CardContent>
            </Card>
          ))}

          {/* Emergency Contacts */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-red-600">
                <Phone className="h-5 w-5 mr-2" />
                Emergency Contact Numbers
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <span className="font-medium">Police</span>
                  <span className="text-red-600 font-bold">100</span>
                </div>
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <span className="font-medium">Fire Department</span>
                  <span className="text-red-600 font-bold">101</span>
                </div>
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <span className="font-medium">Ambulance</span>
                  <span className="text-red-600 font-bold">108</span>
                </div>
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <span className="font-medium">Hostel Emergency</span>
                  <span className="text-red-600 font-bold">{hostelInfo.contact.emergencyContact}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
