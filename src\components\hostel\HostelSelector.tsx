import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Building2,
  MapPin,
  Users,
  Bed,
  TrendingUp,
  Info,
  Phone,
  Mail,
  Globe
} from 'lucide-react';
import { HostelInfo } from '@/types/roomManagement';
import { getOwnerHostels } from '@/services/floorManagementService';
import { toast } from '@/hooks/use-toast';

interface HostelSelectorProps {
  ownerId: string;
  selectedHostelId?: string;
  onHostelChange: (hostelId: string, hostel: HostelInfo) => void;
  showDetails?: boolean;
  className?: string;
}

export const HostelSelector: React.FC<HostelSelectorProps> = ({
  ownerId,
  selectedHostelId,
  onHostelChange,
  showDetails = true,
  className = ''
}) => {
  const [hostels, setHostels] = useState<HostelInfo[]>([]);
  const [selectedHostel, setSelectedHostel] = useState<HostelInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);

  useEffect(() => {
    loadHostels();
  }, [ownerId]);

  useEffect(() => {
    if (selectedHostelId && hostels.length > 0) {
      const hostel = hostels.find(h => h.id === selectedHostelId);
      if (hostel) {
        setSelectedHostel(hostel);
      }
    }
  }, [selectedHostelId, hostels]);

  const loadHostels = async () => {
    setIsLoading(true);
    try {
      const result = await getOwnerHostels(ownerId);
      if (result.success) {
        setHostels(result.data);
        
        // Auto-select first hostel if none selected
        if (!selectedHostelId && result.data.length > 0) {
          const firstHostel = result.data[0];
          setSelectedHostel(firstHostel);
          onHostelChange(firstHostel.id, firstHostel);
        }
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to load hostels",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error loading hostels:', error);
      toast({
        title: "Error",
        description: "Failed to load hostels",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleHostelSelect = (hostelId: string) => {
    const hostel = hostels.find(h => h.id === hostelId);
    if (hostel) {
      setSelectedHostel(hostel);
      onHostelChange(hostelId, hostel);
    }
  };

  const getStatusBadge = (status: HostelInfo['status']) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case 'inactive':
        return <Badge variant="destructive">Inactive</Badge>;
      case 'under_construction':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700">Under Construction</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center h-20">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            <span className="ml-2 text-sm text-muted-foreground">Loading hostels...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (hostels.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <Building2 className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No hostels found</h3>
            <p className="mt-1 text-sm text-gray-500">
              You don't have any hostels registered yet.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building2 className="h-5 w-5" />
          Hostel Selection
        </CardTitle>
        <CardDescription>
          Select a hostel to manage rooms and floors
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Hostel Selector */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Select Hostel</label>
          <Select value={selectedHostel?.id || ''} onValueChange={handleHostelSelect}>
            <SelectTrigger>
              <SelectValue placeholder="Choose a hostel" />
            </SelectTrigger>
            <SelectContent>
              {hostels.map((hostel) => (
                <SelectItem key={hostel.id} value={hostel.id}>
                  <div className="flex items-center space-x-2">
                    <Building2 className="h-4 w-4" />
                    <span>{hostel.name}</span>
                    <span className="text-muted-foreground">- {hostel.city}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Selected Hostel Details */}
        {selectedHostel && showDetails && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">{selectedHostel.name}</h3>
              {getStatusBadge(selectedHostel.status)}
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{selectedHostel.totalFloors}</div>
                <div className="text-xs text-muted-foreground">Floors</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{selectedHostel.totalRooms}</div>
                <div className="text-xs text-muted-foreground">Rooms</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{selectedHostel.totalBeds}</div>
                <div className="text-xs text-muted-foreground">Beds</div>
              </div>
            </div>

            {/* Location */}
            <div className="flex items-start space-x-2">
              <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
              <div className="text-sm">
                <div>{selectedHostel.address}</div>
                <div className="text-muted-foreground">
                  {selectedHostel.city}, {selectedHostel.state} - {selectedHostel.pincode}
                </div>
              </div>
            </div>

            {/* Facilities */}
            <div>
              <div className="text-sm font-medium mb-2">Facilities</div>
              <div className="flex flex-wrap gap-1">
                {selectedHostel.facilities.slice(0, 4).map((facility) => (
                  <Badge key={facility} variant="outline" className="text-xs">
                    {facility}
                  </Badge>
                ))}
                {selectedHostel.facilities.length > 4 && (
                  <Badge variant="outline" className="text-xs">
                    +{selectedHostel.facilities.length - 4} more
                  </Badge>
                )}
              </div>
            </div>

            {/* View Details Button */}
            <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="w-full">
                  <Info className="mr-2 h-4 w-4" />
                  View Full Details
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle className="flex items-center gap-2">
                    <Building2 className="h-5 w-5" />
                    {selectedHostel.name}
                  </DialogTitle>
                  <DialogDescription>
                    Complete hostel information and statistics
                  </DialogDescription>
                </DialogHeader>

                <div className="space-y-6">
                  {/* Contact Information */}
                  <div>
                    <h4 className="font-semibold mb-3">Contact Information</h4>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{selectedHostel.contactInfo.phone}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{selectedHostel.contactInfo.email}</span>
                      </div>
                      {selectedHostel.contactInfo.website && (
                        <div className="flex items-center space-x-2">
                          <Globe className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{selectedHostel.contactInfo.website}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Statistics */}
                  <div>
                    <h4 className="font-semibold mb-3">Statistics</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-3 bg-gray-50 rounded-lg">
                        <div className="text-xl font-bold">{selectedHostel.totalFloors}</div>
                        <div className="text-sm text-muted-foreground">Total Floors</div>
                      </div>
                      <div className="text-center p-3 bg-gray-50 rounded-lg">
                        <div className="text-xl font-bold">{selectedHostel.totalRooms}</div>
                        <div className="text-sm text-muted-foreground">Total Rooms</div>
                      </div>
                      <div className="text-center p-3 bg-gray-50 rounded-lg">
                        <div className="text-xl font-bold">{selectedHostel.totalBeds}</div>
                        <div className="text-sm text-muted-foreground">Total Beds</div>
                      </div>
                      <div className="text-center p-3 bg-gray-50 rounded-lg">
                        <div className="text-xl font-bold">{getStatusBadge(selectedHostel.status)}</div>
                        <div className="text-sm text-muted-foreground">Status</div>
                      </div>
                    </div>
                  </div>

                  {/* All Facilities */}
                  <div>
                    <h4 className="font-semibold mb-3">All Facilities</h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedHostel.facilities.map((facility) => (
                        <Badge key={facility} variant="outline">
                          {facility}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Dates */}
                  <div>
                    <h4 className="font-semibold mb-3">Timeline</h4>
                    <div className="space-y-2 text-sm">
                      <div>
                        <span className="font-medium">Created:</span>{' '}
                        {new Date(selectedHostel.createdAt).toLocaleDateString()}
                      </div>
                      <div>
                        <span className="font-medium">Last Updated:</span>{' '}
                        {new Date(selectedHostel.updatedAt).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
