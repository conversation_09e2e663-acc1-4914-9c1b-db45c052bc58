import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Building2,
  Search,
  Filter,
  MoreHorizontal,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  User,
  UserCheck,
  FileText,
  MapPin,
  Phone,
  Mail,
  Calendar,
  AlertTriangle,
  Download,
  ExternalLink
} from 'lucide-react';
import {
  mockHostelRegistrationRequests,
  mockAccountCreationRequests,
  mockHostelOwnerAccessRequests,
  mockRoomModificationRequests,
  getUserById
} from '@/data/mockData';
import { HostelRegistrationRequest, AccountCreationRequest, HostelOwnerAccessRequest } from '@/types/admin';
import { RoomModificationRequest } from '@/types/roomManagement';
import { toast } from '@/hooks/use-toast';
import { processHostelRegistrationApproval, processAccountCreationApproval } from '@/services/approvalService';
import { approveOwnerAccessRequest, rejectOwnerAccessRequest } from '@/services/ownerAccessService';
import { useAuth } from '@/contexts/AuthContext';

export const RegistrationApprovals: React.FC = () => {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'approved' | 'rejected' | 'under_review'>('all');
  const [selectedRequest, setSelectedRequest] = useState<HostelRegistrationRequest | AccountCreationRequest | HostelOwnerAccessRequest | RoomModificationRequest | null>(null);
  const [isApprovalDialogOpen, setIsApprovalDialogOpen] = useState(false);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [approvalAction, setApprovalAction] = useState<'approve' | 'reject' | null>(null);
  const [approvalNotes, setApprovalNotes] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  // Filter requests based on search and status
  const filteredHostelRequests = mockHostelRegistrationRequests.filter(request => {
    const matchesSearch = request.hostelDetails.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         request.ownerDetails.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         request.ownerDetails.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || request.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const filteredAccountRequests = mockAccountCreationRequests.filter(request => {
    const matchesSearch = request.requestedUserDetails.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         request.requestedUserDetails.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || request.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const filteredOwnerAccessRequests = mockHostelOwnerAccessRequests.filter(request => {
    const matchesSearch = request.applicantDetails.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         request.applicantDetails.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         request.businessDetails.businessName?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || request.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const filteredRoomModificationRequests = mockRoomModificationRequests.filter(request => {
    const matchesSearch = request.requestType.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         request.hostelId.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || request.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Pending</Badge>;
      case 'approved':
        return <Badge className="bg-green-100 text-green-800">Approved</Badge>;
      case 'rejected':
        return <Badge variant="destructive">Rejected</Badge>;
      case 'under_review':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Under Review</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const handleApprovalAction = async (request: HostelRegistrationRequest | AccountCreationRequest | HostelOwnerAccessRequest | RoomModificationRequest, action: 'approve' | 'reject') => {
    setSelectedRequest(request);
    setApprovalAction(action);
    setIsApprovalDialogOpen(true);
  };

  const confirmApprovalAction = async () => {
    if (!selectedRequest || !approvalAction || !user) return;

    setIsProcessing(true);

    try {
      let result;

      if (selectedRequest.requestType === 'hostel_registration') {
        result = await processHostelRegistrationApproval({
          requestId: selectedRequest.id,
          action: approvalAction,
          notes: approvalNotes,
          adminId: user.id
        });
      } else if (selectedRequest.requestType === 'account_creation') {
        result = await processAccountCreationApproval({
          requestId: selectedRequest.id,
          action: approvalAction,
          notes: approvalNotes,
          adminId: user.id
        });
      } else if (selectedRequest.requestType === 'owner_access') {
        if (approvalAction === 'approve') {
          result = await approveOwnerAccessRequest(
            selectedRequest.id,
            user.id,
            approvalNotes
          );
        } else {
          result = await rejectOwnerAccessRequest(
            selectedRequest.id,
            user.id,
            approvalNotes
          );
        }
      }

      if (result.success) {
        toast({
          title: `Request ${approvalAction === 'approve' ? 'Approved' : 'Rejected'}`,
          description: `The ${selectedRequest.requestType.replace('_', ' ')} request has been ${approvalAction}d successfully.`,
        });

        setIsApprovalDialogOpen(false);
        setSelectedRequest(null);
        setApprovalAction(null);
        setApprovalNotes('');
      } else {
        toast({
          title: "Action Failed",
          description: result.error || "There was an error processing the request. Please try again.",
          variant: "destructive"
        });
      }

    } catch (error) {
      console.error('Approval action error:', error);
      toast({
        title: "Action Failed",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const pendingHostelCount = mockHostelRegistrationRequests.filter(r => r.status === 'pending').length;
  const pendingAccountCount = mockAccountCreationRequests.filter(r => r.status === 'pending').length;
  const pendingOwnerAccessCount = mockHostelOwnerAccessRequests.filter(r => r.status === 'pending').length;
  const pendingRoomModificationCount = mockRoomModificationRequests.filter(r => r.status === 'pending').length;

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Registration Approvals</h1>
          <p className="text-muted-foreground">
            Review and manage hostel registration and account creation requests
          </p>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Hostel Registrations</CardTitle>
            <Building2 className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{pendingHostelCount}</div>
            <p className="text-xs text-muted-foreground">
              Awaiting review
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Account Requests</CardTitle>
            <User className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{pendingAccountCount}</div>
            <p className="text-xs text-muted-foreground">
              Awaiting approval
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Owner Access</CardTitle>
            <UserCheck className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{pendingOwnerAccessCount}</div>
            <p className="text-xs text-muted-foreground">
              Awaiting review
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Approved</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {[...mockHostelRegistrationRequests, ...mockAccountCreationRequests, ...mockHostelOwnerAccessRequests]
                .filter(r => r.status === 'approved').length}
            </div>
            <p className="text-xs text-muted-foreground">
              All time
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Under Review</CardTitle>
            <Clock className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {mockHostelRegistrationRequests.filter(r => r.status === 'under_review').length}
            </div>
            <p className="text-xs text-muted-foreground">
              Being processed
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Registration Requests</CardTitle>
          <CardDescription>
            Review and approve registration requests from hostel owners and employees
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by name, email, or hostel name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Filter className="mr-2 h-4 w-4" />
                  Status: {statusFilter === 'all' ? 'All' : statusFilter}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setStatusFilter('all')}>
                  All Requests
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('pending')}>
                  Pending
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('under_review')}>
                  Under Review
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('approved')}>
                  Approved
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('rejected')}>
                  Rejected
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <Tabs defaultValue="hostel-registrations" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="hostel-registrations">
                Hostel Registrations ({filteredHostelRequests.length})
              </TabsTrigger>
              <TabsTrigger value="account-requests">
                Account Requests ({filteredAccountRequests.length})
              </TabsTrigger>
              <TabsTrigger value="owner-access-requests">
                Owner Access ({filteredOwnerAccessRequests.length})
              </TabsTrigger>
              <TabsTrigger value="room-modifications">
                Room Changes ({filteredRoomModificationRequests.length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="hostel-registrations" className="space-y-4">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Hostel Name</TableHead>
                      <TableHead>Owner</TableHead>
                      <TableHead>Location</TableHead>
                      <TableHead>Submitted</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredHostelRequests.map((request) => (
                      <TableRow key={request.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center space-x-2">
                            <Building2 className="h-4 w-4 text-muted-foreground" />
                            <span>{request.hostelDetails.name}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{request.ownerDetails.name}</div>
                            <div className="text-sm text-muted-foreground">{request.ownerDetails.email}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-1">
                            <MapPin className="h-3 w-3 text-muted-foreground" />
                            <span className="text-sm">{request.hostelDetails.city}, {request.hostelDetails.state}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">{formatDate(request.submittedAt)}</div>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(request.status)}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => {
                                setSelectedRequest(request);
                                setIsDetailsDialogOpen(true);
                              }}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              {request.status === 'pending' && (
                                <>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem 
                                    onClick={() => handleApprovalAction(request, 'approve')}
                                    className="text-green-600"
                                  >
                                    <CheckCircle className="mr-2 h-4 w-4" />
                                    Approve
                                  </DropdownMenuItem>
                                  <DropdownMenuItem 
                                    onClick={() => handleApprovalAction(request, 'reject')}
                                    className="text-red-600"
                                  >
                                    <XCircle className="mr-2 h-4 w-4" />
                                    Reject
                                  </DropdownMenuItem>
                                </>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {filteredHostelRequests.length === 0 && (
                <div className="text-center py-8">
                  <Building2 className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No hostel registrations found</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Try adjusting your search criteria.
                  </p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="account-requests" className="space-y-4">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Submitted</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredAccountRequests.map((request) => (
                      <TableRow key={request.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center space-x-2">
                            <User className="h-4 w-4 text-muted-foreground" />
                            <span>{request.requestedUserDetails.name}</span>
                          </div>
                        </TableCell>
                        <TableCell>{request.requestedUserDetails.email}</TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {request.requestedUserDetails.role}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">{formatDate(request.submittedAt)}</div>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(request.status)}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => {
                                setSelectedRequest(request);
                                setIsDetailsDialogOpen(true);
                              }}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              {request.status === 'pending' && (
                                <>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem 
                                    onClick={() => handleApprovalAction(request, 'approve')}
                                    className="text-green-600"
                                  >
                                    <CheckCircle className="mr-2 h-4 w-4" />
                                    Approve
                                  </DropdownMenuItem>
                                  <DropdownMenuItem 
                                    onClick={() => handleApprovalAction(request, 'reject')}
                                    className="text-red-600"
                                  >
                                    <XCircle className="mr-2 h-4 w-4" />
                                    Reject
                                  </DropdownMenuItem>
                                </>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {filteredAccountRequests.length === 0 && (
                <div className="text-center py-8">
                  <User className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No account requests found</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Try adjusting your search criteria.
                  </p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="owner-access-requests" className="space-y-4">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Applicant Name</TableHead>
                      <TableHead>Business</TableHead>
                      <TableHead>Location</TableHead>
                      <TableHead>Experience</TableHead>
                      <TableHead>Submitted</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredOwnerAccessRequests.map((request) => (
                      <TableRow key={request.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center space-x-2">
                            <UserCheck className="h-4 w-4 text-muted-foreground" />
                            <div>
                              <div className="font-medium">{request.applicantDetails.name}</div>
                              <div className="text-sm text-muted-foreground">{request.applicantDetails.email}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{request.businessDetails.businessName || 'Individual'}</div>
                            <div className="text-sm text-muted-foreground">{request.businessDetails.businessType}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-1">
                            <MapPin className="h-3 w-3 text-muted-foreground" />
                            <span className="text-sm">{request.businessDetails.targetLocation}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {request.businessDetails.experienceYears} years
                            {request.businessDetails.previousHostelExperience && (
                              <Badge variant="outline" className="ml-2 text-xs">
                                Hostel Exp.
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">{formatDate(request.submittedAt)}</div>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(request.status)}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => {
                                setSelectedRequest(request);
                                setIsDetailsDialogOpen(true);
                              }}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              {request.status === 'pending' && (
                                <>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    onClick={() => handleApprovalAction(request, 'approve')}
                                    className="text-green-600"
                                  >
                                    <CheckCircle className="mr-2 h-4 w-4" />
                                    Approve
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => handleApprovalAction(request, 'reject')}
                                    className="text-red-600"
                                  >
                                    <XCircle className="mr-2 h-4 w-4" />
                                    Reject
                                  </DropdownMenuItem>
                                </>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {filteredOwnerAccessRequests.length === 0 && (
                <div className="text-center py-8">
                  <UserCheck className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No owner access requests found</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Try adjusting your search criteria.
                  </p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="room-modifications" className="space-y-4">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Request Type</TableHead>
                      <TableHead>Hostel</TableHead>
                      <TableHead>Details</TableHead>
                      <TableHead>Submitted</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredRoomModificationRequests.map((request) => (
                      <TableRow key={request.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center space-x-2">
                            <Building2 className="h-4 w-4 text-muted-foreground" />
                            <div>
                              <div className="font-medium capitalize">{request.requestType.replace('_', ' ')}</div>
                              <div className="text-sm text-muted-foreground">ID: {request.id}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">Hostel {request.hostelId}</div>
                            <div className="text-sm text-muted-foreground">Owner: {request.ownerId}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {request.requestType === 'add_room' && (
                            <div className="text-sm">
                              <div>Room: {(request.requestDetails as any).roomNumber}</div>
                              <div className="text-muted-foreground">Type: {(request.requestDetails as any).roomType}</div>
                            </div>
                          )}
                          {request.requestType === 'add_beds' && (
                            <div className="text-sm">
                              <div>+{(request.requestDetails as any).newBeds?.length || 0} beds</div>
                              <div className="text-muted-foreground">Room: {(request.requestDetails as any).roomId}</div>
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">{formatDate(request.submittedAt)}</div>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(request.status)}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => {
                                setSelectedRequest(request);
                                setIsDetailsDialogOpen(true);
                              }}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              {request.status === 'pending' && (
                                <>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    onClick={() => handleApprovalAction(request, 'approve')}
                                    className="text-green-600"
                                  >
                                    <CheckCircle className="mr-2 h-4 w-4" />
                                    Approve
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => handleApprovalAction(request, 'reject')}
                                    className="text-red-600"
                                  >
                                    <XCircle className="mr-2 h-4 w-4" />
                                    Reject
                                  </DropdownMenuItem>
                                </>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {filteredRoomModificationRequests.length === 0 && (
                <div className="text-center py-8">
                  <Building2 className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No room modification requests found</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Try adjusting your search criteria.
                  </p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Approval/Rejection Dialog */}
      <Dialog open={isApprovalDialogOpen} onOpenChange={setIsApprovalDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>
              {approvalAction === 'approve' ? 'Approve' : 'Reject'} Request
            </DialogTitle>
            <DialogDescription>
              {approvalAction === 'approve' 
                ? 'Are you sure you want to approve this request?' 
                : 'Please provide a reason for rejecting this request.'}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <label htmlFor="notes" className="text-sm font-medium">
                {approvalAction === 'approve' ? 'Approval Notes (Optional)' : 'Rejection Reason *'}
              </label>
              <Textarea
                id="notes"
                placeholder={approvalAction === 'approve' 
                  ? 'Add any notes for this approval...' 
                  : 'Please explain why this request is being rejected...'}
                value={approvalNotes}
                onChange={(e) => setApprovalNotes(e.target.value)}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsApprovalDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={confirmApprovalAction}
              variant={approvalAction === 'approve' ? 'default' : 'destructive'}
              disabled={(approvalAction === 'reject' && !approvalNotes.trim()) || isProcessing}
            >
              {isProcessing ? 'Processing...' : (approvalAction === 'approve' ? 'Approve' : 'Reject')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Request Details Dialog */}
      <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
        <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {selectedRequest?.requestType === 'hostel_registration' ? 'Hostel Registration' :
               selectedRequest?.requestType === 'account_creation' ? 'Account Creation' : 'Owner Access'} Request Details
            </DialogTitle>
            <DialogDescription>
              Review all submitted information and documents
            </DialogDescription>
          </DialogHeader>

          {selectedRequest && (
            <div className="space-y-6">
              {/* Request Info */}
              <div className="grid grid-cols-2 gap-4 p-4 bg-muted rounded-lg">
                <div>
                  <p className="text-sm font-medium">Request ID</p>
                  <p className="text-sm text-muted-foreground">{selectedRequest.id}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Status</p>
                  <div className="mt-1">{getStatusBadge(selectedRequest.status)}</div>
                </div>
                <div>
                  <p className="text-sm font-medium">Submitted</p>
                  <p className="text-sm text-muted-foreground">{formatDate(selectedRequest.submittedAt)}</p>
                </div>
                {selectedRequest.reviewedAt && (
                  <div>
                    <p className="text-sm font-medium">Reviewed</p>
                    <p className="text-sm text-muted-foreground">{formatDate(selectedRequest.reviewedAt)}</p>
                  </div>
                )}
              </div>

              {selectedRequest.requestType === 'hostel_registration' ? (
                // Hostel Registration Details
                <div className="space-y-6">
                  {/* Owner Details */}
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Owner Information</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium">Name</p>
                        <p className="text-sm text-muted-foreground">{(selectedRequest as HostelRegistrationRequest).ownerDetails.name}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Email</p>
                        <p className="text-sm text-muted-foreground">{(selectedRequest as HostelRegistrationRequest).ownerDetails.email}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Phone</p>
                        <p className="text-sm text-muted-foreground">{(selectedRequest as HostelRegistrationRequest).ownerDetails.phone}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Business Experience</p>
                        <p className="text-sm text-muted-foreground">{(selectedRequest as HostelRegistrationRequest).ownerDetails.businessExperience} years</p>
                      </div>
                    </div>
                  </div>

                  {/* Hostel Details */}
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Hostel Information</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium">Hostel Name</p>
                        <p className="text-sm text-muted-foreground">{(selectedRequest as HostelRegistrationRequest).hostelDetails.name}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Total Beds</p>
                        <p className="text-sm text-muted-foreground">{(selectedRequest as HostelRegistrationRequest).hostelDetails.totalBeds}</p>
                      </div>
                      <div className="col-span-2">
                        <p className="text-sm font-medium">Address</p>
                        <p className="text-sm text-muted-foreground">
                          {(selectedRequest as HostelRegistrationRequest).hostelDetails.address}, {(selectedRequest as HostelRegistrationRequest).hostelDetails.city}, {(selectedRequest as HostelRegistrationRequest).hostelDetails.state} - {(selectedRequest as HostelRegistrationRequest).hostelDetails.pincode}
                        </p>
                      </div>
                      <div className="col-span-2">
                        <p className="text-sm font-medium">Amenities</p>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {(selectedRequest as HostelRegistrationRequest).hostelDetails.amenities.map((amenity) => (
                            <Badge key={amenity} variant="outline" className="text-xs">
                              {amenity}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Documents */}
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Documents</h3>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <FileText className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="text-sm font-medium">Owner ID Proof ({(selectedRequest as HostelRegistrationRequest).documents.ownerIdProof.type})</p>
                            <p className="text-xs text-muted-foreground">{(selectedRequest as HostelRegistrationRequest).documents.ownerIdProof.number}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {(selectedRequest as HostelRegistrationRequest).documents.ownerIdProof.verified ? (
                            <Badge className="bg-green-100 text-green-800">Verified</Badge>
                          ) : (
                            <Badge variant="outline">Pending</Badge>
                          )}
                          <Button size="sm" variant="outline">
                            <Download className="h-3 w-3 mr-1" />
                            View
                          </Button>
                        </div>
                      </div>

                      {(selectedRequest as HostelRegistrationRequest).documents.businessRegistration && (
                        <div className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center space-x-3">
                            <FileText className="h-4 w-4 text-muted-foreground" />
                            <div>
                              <p className="text-sm font-medium">Business Registration ({(selectedRequest as HostelRegistrationRequest).documents.businessRegistration!.type})</p>
                              <p className="text-xs text-muted-foreground">{(selectedRequest as HostelRegistrationRequest).documents.businessRegistration!.number}</p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            {(selectedRequest as HostelRegistrationRequest).documents.businessRegistration!.verified ? (
                              <Badge className="bg-green-100 text-green-800">Verified</Badge>
                            ) : (
                              <Badge variant="outline">Pending</Badge>
                            )}
                            <Button size="sm" variant="outline">
                              <Download className="h-3 w-3 mr-1" />
                              View
                            </Button>
                          </div>
                        </div>
                      )}

                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <FileText className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="text-sm font-medium">Property Documents ({(selectedRequest as HostelRegistrationRequest).documents.propertyDocuments.type})</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {(selectedRequest as HostelRegistrationRequest).documents.propertyDocuments.verified ? (
                            <Badge className="bg-green-100 text-green-800">Verified</Badge>
                          ) : (
                            <Badge variant="outline">Pending</Badge>
                          )}
                          <Button size="sm" variant="outline">
                            <Download className="h-3 w-3 mr-1" />
                            View
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : selectedRequest.requestType === 'account_creation' ? (
                // Account Creation Details
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Requested User Information</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium">Name</p>
                        <p className="text-sm text-muted-foreground">{(selectedRequest as AccountCreationRequest).requestedUserDetails.name}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Email</p>
                        <p className="text-sm text-muted-foreground">{(selectedRequest as AccountCreationRequest).requestedUserDetails.email}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Phone</p>
                        <p className="text-sm text-muted-foreground">{(selectedRequest as AccountCreationRequest).requestedUserDetails.phone}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Role</p>
                        <Badge variant="outline">{(selectedRequest as AccountCreationRequest).requestedUserDetails.role}</Badge>
                      </div>
                      {(selectedRequest as AccountCreationRequest).requestedUserDetails.businessType && (
                        <div>
                          <p className="text-sm font-medium">Business Type</p>
                          <p className="text-sm text-muted-foreground">{(selectedRequest as AccountCreationRequest).requestedUserDetails.businessType}</p>
                        </div>
                      )}
                      {(selectedRequest as AccountCreationRequest).requestedUserDetails.experienceYears && (
                        <div>
                          <p className="text-sm font-medium">Experience</p>
                          <p className="text-sm text-muted-foreground">{(selectedRequest as AccountCreationRequest).requestedUserDetails.experienceYears} years</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Documents */}
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Documents</h3>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <FileText className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="text-sm font-medium">ID Proof ({(selectedRequest as AccountCreationRequest).documents.idProof.type})</p>
                            <p className="text-xs text-muted-foreground">{(selectedRequest as AccountCreationRequest).documents.idProof.number}</p>
                          </div>
                        </div>
                        <Button size="sm" variant="outline">
                          <Download className="h-3 w-3 mr-1" />
                          View
                        </Button>
                      </div>

                      {(selectedRequest as AccountCreationRequest).documents.businessProof && (
                        <div className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center space-x-3">
                            <FileText className="h-4 w-4 text-muted-foreground" />
                            <div>
                              <p className="text-sm font-medium">Business Proof ({(selectedRequest as AccountCreationRequest).documents.businessProof!.type})</p>
                              <p className="text-xs text-muted-foreground">{(selectedRequest as AccountCreationRequest).documents.businessProof!.number}</p>
                            </div>
                          </div>
                          <Button size="sm" variant="outline">
                            <Download className="h-3 w-3 mr-1" />
                            View
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                // Owner Access Request Details
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Applicant Information</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium">Name</p>
                        <p className="text-sm text-muted-foreground">{(selectedRequest as HostelOwnerAccessRequest).applicantDetails.name}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Email</p>
                        <p className="text-sm text-muted-foreground">{(selectedRequest as HostelOwnerAccessRequest).applicantDetails.email}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Phone</p>
                        <p className="text-sm text-muted-foreground">{(selectedRequest as HostelOwnerAccessRequest).applicantDetails.phone}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Location</p>
                        <p className="text-sm text-muted-foreground">
                          {(selectedRequest as HostelOwnerAccessRequest).applicantDetails.city}, {(selectedRequest as HostelOwnerAccessRequest).applicantDetails.state}
                        </p>
                      </div>
                      <div className="col-span-2">
                        <p className="text-sm font-medium">Address</p>
                        <p className="text-sm text-muted-foreground">{(selectedRequest as HostelOwnerAccessRequest).applicantDetails.address}</p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-3">Business Information</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium">Business Name</p>
                        <p className="text-sm text-muted-foreground">
                          {(selectedRequest as HostelOwnerAccessRequest).businessDetails.businessName || 'Individual'}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Business Type</p>
                        <p className="text-sm text-muted-foreground">{(selectedRequest as HostelOwnerAccessRequest).businessDetails.businessType}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Experience</p>
                        <p className="text-sm text-muted-foreground">{(selectedRequest as HostelOwnerAccessRequest).businessDetails.experienceYears} years</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Previous Hostel Experience</p>
                        <Badge variant={
                          (selectedRequest as HostelOwnerAccessRequest).businessDetails.previousHostelExperience
                            ? "default" : "outline"
                        }>
                          {(selectedRequest as HostelOwnerAccessRequest).businessDetails.previousHostelExperience ? 'Yes' : 'No'}
                        </Badge>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Target Location</p>
                        <p className="text-sm text-muted-foreground">{(selectedRequest as HostelOwnerAccessRequest).businessDetails.targetLocation}</p>
                      </div>
                      {(selectedRequest as HostelOwnerAccessRequest).businessDetails.estimatedInvestment && (
                        <div>
                          <p className="text-sm font-medium">Estimated Investment</p>
                          <p className="text-sm text-muted-foreground">
                            ₹{(selectedRequest as HostelOwnerAccessRequest).businessDetails.estimatedInvestment?.toLocaleString()}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-3">Documents</h3>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <FileText className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="text-sm font-medium">ID Proof ({(selectedRequest as HostelOwnerAccessRequest).documents.idProof.type})</p>
                            <p className="text-xs text-muted-foreground">{(selectedRequest as HostelOwnerAccessRequest).documents.idProof.number}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {(selectedRequest as HostelOwnerAccessRequest).documents.idProof.verified ? (
                            <Badge className="bg-green-100 text-green-800">Verified</Badge>
                          ) : (
                            <Badge variant="outline">Pending</Badge>
                          )}
                          <Button size="sm" variant="outline">
                            <Download className="h-3 w-3 mr-1" />
                            View
                          </Button>
                        </div>
                      </div>

                      {(selectedRequest as HostelOwnerAccessRequest).documents.businessProof && (
                        <div className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center space-x-3">
                            <FileText className="h-4 w-4 text-muted-foreground" />
                            <div>
                              <p className="text-sm font-medium">Business Proof</p>
                              <p className="text-xs text-muted-foreground">{(selectedRequest as HostelOwnerAccessRequest).documents.businessProof!.type}</p>
                            </div>
                          </div>
                          <Button size="sm" variant="outline">
                            <Download className="h-3 w-3 mr-1" />
                            View
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>

                  {(selectedRequest as HostelOwnerAccessRequest).additionalInfo && (
                    <div>
                      <h3 className="text-lg font-semibold mb-3">Additional Information</h3>
                      <div className="space-y-4">
                        <div>
                          <p className="text-sm font-medium">Motivation</p>
                          <p className="text-sm text-muted-foreground mt-1">
                            {(selectedRequest as HostelOwnerAccessRequest).additionalInfo!.motivation}
                          </p>
                        </div>
                        {(selectedRequest as HostelOwnerAccessRequest).additionalInfo!.businessPlan && (
                          <div>
                            <p className="text-sm font-medium">Business Plan</p>
                            <p className="text-sm text-muted-foreground mt-1">
                              {(selectedRequest as HostelOwnerAccessRequest).additionalInfo!.businessPlan}
                            </p>
                          </div>
                        )}
                        {(selectedRequest as HostelOwnerAccessRequest).additionalInfo!.references &&
                         (selectedRequest as HostelOwnerAccessRequest).additionalInfo!.references!.length > 0 && (
                          <div>
                            <p className="text-sm font-medium mb-2">References</p>
                            <div className="space-y-2">
                              {(selectedRequest as HostelOwnerAccessRequest).additionalInfo!.references!.map((ref, index) => (
                                <div key={index} className="p-2 bg-muted rounded text-sm">
                                  <p className="font-medium">{ref.name} ({ref.relationship})</p>
                                  <p className="text-muted-foreground">{ref.phone} {ref.email && `• ${ref.email}`}</p>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Review Notes */}
              {selectedRequest.reviewNotes && (
                <div>
                  <h3 className="text-lg font-semibold mb-3">Review Notes</h3>
                  <div className="p-3 bg-muted rounded-lg">
                    <p className="text-sm">{selectedRequest.reviewNotes}</p>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              {selectedRequest.status === 'pending' && (
                <div className="flex justify-end space-x-2 pt-4 border-t">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setIsDetailsDialogOpen(false);
                      handleApprovalAction(selectedRequest, 'reject');
                    }}
                  >
                    <XCircle className="mr-2 h-4 w-4" />
                    Reject
                  </Button>
                  <Button
                    onClick={() => {
                      setIsDetailsDialogOpen(false);
                      handleApprovalAction(selectedRequest, 'approve');
                    }}
                  >
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Approve
                  </Button>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};
