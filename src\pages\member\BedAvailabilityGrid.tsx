import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Bed, 
  Star, 
  Wifi,
  Coffee,
  Car,
  Shield,
  CheckCircle,
  XCircle,
  Clock,
  Users,
  Utensils,
  RefreshCw
} from 'lucide-react';
import { type Hostel } from '@/data/mockData';

interface BedAvailabilityGridProps {
  hostel: Hostel;
  membershipType: 'regular' | 'guest';
  onBedSelect: (selection: {
    bedType: 'standard' | 'premium';
    foodPackage: 'with_meals' | 'without_meals';
    pricing: {
      bedRate: number;
      foodRate: number;
      total: number;
    };
  }) => void;
  selectedBedType?: 'standard' | 'premium';
  selectedFoodPackage?: 'with_meals' | 'without_meals';
  className?: string;
}

export const BedAvailabilityGrid: React.FC<BedAvailabilityGridProps> = ({
  hostel,
  membershipType,
  onBedSelect,
  selectedBedType,
  selectedFoodPackage,
  className
}) => {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(new Date());

  const refreshAvailability = async () => {
    setIsRefreshing(true);
    // Simulate API call to refresh bed availability
    await new Promise(resolve => setTimeout(resolve, 1000));
    setLastUpdated(new Date());
    setIsRefreshing(false);
  };

  const handleBedTypeSelect = (bedType: 'standard' | 'premium') => {
    const bedRate = membershipType === 'regular' 
      ? hostel.bedTypes[bedType].monthlyRate 
      : hostel.bedTypes[bedType].dailyRate;
    
    const foodRate = selectedFoodPackage === 'with_meals' 
      ? hostel.foodPackages.withMeals.dailyRate 
      : 0;

    onBedSelect({
      bedType,
      foodPackage: selectedFoodPackage || 'without_meals',
      pricing: {
        bedRate,
        foodRate,
        total: bedRate + (membershipType === 'guest' ? foodRate : foodRate * 30)
      }
    });
  };

  const handleFoodPackageSelect = (foodPackage: 'with_meals' | 'without_meals') => {
    if (!selectedBedType) return;

    const bedRate = membershipType === 'regular' 
      ? hostel.bedTypes[selectedBedType].monthlyRate 
      : hostel.bedTypes[selectedBedType].dailyRate;
    
    const foodRate = foodPackage === 'with_meals' 
      ? hostel.foodPackages.withMeals.dailyRate 
      : 0;

    onBedSelect({
      bedType: selectedBedType,
      foodPackage,
      pricing: {
        bedRate,
        foodRate,
        total: bedRate + (membershipType === 'guest' ? foodRate : foodRate * 30)
      }
    });
  };

  const getBedTypeStatus = (bedType: 'standard' | 'premium') => {
    const available = hostel.bedTypes[bedType].available;
    const total = hostel.bedTypes[bedType].count;
    
    if (available === 0) return 'unavailable';
    if (available <= total * 0.2) return 'limited';
    return 'available';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'bg-green-100 text-green-800';
      case 'limited': return 'bg-yellow-100 text-yellow-800';
      case 'unavailable': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'available': return <CheckCircle className="h-4 w-4" />;
      case 'limited': return <Clock className="h-4 w-4" />;
      case 'unavailable': return <XCircle className="h-4 w-4" />;
      default: return <Bed className="h-4 w-4" />;
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Bed className="h-5 w-5" />
              <span>Bed Availability & Pricing</span>
            </CardTitle>
            <CardDescription>
              Select your preferred bed type and food package
            </CardDescription>
          </div>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={refreshAvailability}
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
        <div className="text-xs text-muted-foreground">
          Last updated: {lastUpdated.toLocaleTimeString()}
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Bed Types */}
        <div className="space-y-4">
          <h4 className="font-medium">Bed Types</h4>
          <div className="grid gap-4 md:grid-cols-2">
            {/* Standard Bed */}
            <Card 
              className={`cursor-pointer transition-all hover:shadow-md ${
                selectedBedType === 'standard' ? 'ring-2 ring-primary' : ''
              }`}
              onClick={() => handleBedTypeSelect('standard')}
            >
              <CardContent className="p-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h5 className="font-medium">Standard Bed</h5>
                    <Badge className={getStatusColor(getBedTypeStatus('standard'))}>
                      {getStatusIcon(getBedTypeStatus('standard'))}
                      <span className="ml-1 capitalize">{getBedTypeStatus('standard')}</span>
                    </Badge>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Available:</span>
                      <span className="font-medium">
                        {hostel.bedTypes.standard.available}/{hostel.bedTypes.standard.count}
                      </span>
                    </div>
                    
                    <div className="space-y-1">
                      <div className="flex items-center justify-between text-sm">
                        <span>Monthly Rate:</span>
                        <span className="font-medium">₹{hostel.bedTypes.standard.monthlyRate}</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>Daily Rate:</span>
                        <span className="font-medium">₹{hostel.bedTypes.standard.dailyRate}</span>
                      </div>
                    </div>
                  </div>

                  {membershipType === 'regular' && (
                    <div className="text-xs text-muted-foreground">
                      Best for long-term stays
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Premium Bed */}
            <Card 
              className={`cursor-pointer transition-all hover:shadow-md ${
                selectedBedType === 'premium' ? 'ring-2 ring-primary' : ''
              }`}
              onClick={() => handleBedTypeSelect('premium')}
            >
              <CardContent className="p-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h5 className="font-medium">Premium Bed</h5>
                    <Badge className={getStatusColor(getBedTypeStatus('premium'))}>
                      {getStatusIcon(getBedTypeStatus('premium'))}
                      <span className="ml-1 capitalize">{getBedTypeStatus('premium')}</span>
                    </Badge>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Available:</span>
                      <span className="font-medium">
                        {hostel.bedTypes.premium.available}/{hostel.bedTypes.premium.count}
                      </span>
                    </div>
                    
                    <div className="space-y-1">
                      <div className="flex items-center justify-between text-sm">
                        <span>Monthly Rate:</span>
                        <span className="font-medium">₹{hostel.bedTypes.premium.monthlyRate}</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>Daily Rate:</span>
                        <span className="font-medium">₹{hostel.bedTypes.premium.dailyRate}</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                    <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                    <span>Enhanced comfort & amenities</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Food Packages */}
        {selectedBedType && (
          <div className="space-y-4">
            <h4 className="font-medium">Food Packages</h4>
            <div className="grid gap-4 md:grid-cols-2">
              {/* With Meals */}
              <Card 
                className={`cursor-pointer transition-all hover:shadow-md ${
                  selectedFoodPackage === 'with_meals' ? 'ring-2 ring-primary' : ''
                }`}
                onClick={() => handleFoodPackageSelect('with_meals')}
              >
                <CardContent className="p-4">
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Utensils className="h-4 w-4" />
                      <h5 className="font-medium">With Meals</h5>
                    </div>
                    
                    <div className="space-y-2">
                      <p className="text-sm text-muted-foreground">
                        {hostel.foodPackages.withMeals.description}
                      </p>
                      <div className="flex items-center justify-between text-sm">
                        <span>Daily Rate:</span>
                        <span className="font-medium">₹{hostel.foodPackages.withMeals.dailyRate}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Without Meals */}
              <Card 
                className={`cursor-pointer transition-all hover:shadow-md ${
                  selectedFoodPackage === 'without_meals' ? 'ring-2 ring-primary' : ''
                }`}
                onClick={() => handleFoodPackageSelect('without_meals')}
              >
                <CardContent className="p-4">
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Coffee className="h-4 w-4" />
                      <h5 className="font-medium">Without Meals</h5>
                    </div>
                    
                    <div className="space-y-2">
                      <p className="text-sm text-muted-foreground">
                        {hostel.foodPackages.withoutMeals.description}
                      </p>
                      <div className="flex items-center justify-between text-sm">
                        <span>Daily Rate:</span>
                        <span className="font-medium">₹{hostel.foodPackages.withoutMeals.dailyRate}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {/* Hostel Amenities */}
        <div className="space-y-3">
          <h4 className="font-medium">Amenities</h4>
          <div className="flex flex-wrap gap-2">
            {hostel.amenities.map((amenity) => (
              <Badge key={amenity} variant="secondary" className="flex items-center space-x-1">
                {amenity === 'WiFi' && <Wifi className="h-3 w-3" />}
                {amenity === 'Parking' && <Car className="h-3 w-3" />}
                {amenity === 'Security' && <Shield className="h-3 w-3" />}
                {amenity === 'Mess' && <Utensils className="h-3 w-3" />}
                <span>{amenity}</span>
              </Badge>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
