// Owner Registration and Management System Types
// Comprehensive interfaces for hostel owner operations

export interface OwnerRegistration {
  // Personal Information
  personalInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    alternatePhone?: string;
    dateOfBirth: string;
    gender: 'male' | 'female' | 'other';
    address: {
      line1: string;
      line2?: string;
      city: string;
      state: string;
      pincode: string;
      country: string;
    };
  };

  // Business Information
  businessInfo: {
    businessName: string;
    businessType: 'individual' | 'partnership' | 'company' | 'llp';
    gstNumber?: string;
    panNumber: string;
    businessAddress: {
      line1: string;
      line2?: string;
      city: string;
      state: string;
      pincode: string;
      country: string;
    };
    yearsOfExperience: number;
    previousHostelExperience: boolean;
  };

  // Hostel Information
  hostelInfo: {
    name: string;
    description: string;
    address: {
      line1: string;
      line2?: string;
      city: string;
      state: string;
      pincode: string;
      country: string;
    };
    areaId: string;
    propertyType: 'owned' | 'rented' | 'leased';
    totalFloors: number;
    totalRooms: number;
    totalBeds: number;
    amenities: string[];
    nearbyLandmarks: string[];
    transportConnectivity: string[];
  };

  // Room Configuration with Sharing Types and AC/Non-AC
  roomConfiguration: {
    [key: string]: {
      sharing1: {
        ac: {
          available: boolean;
          count: number;
          monthlyRate: number;
          dailyRate: number;
        };
        nonAc: {
          available: boolean;
          count: number;
          monthlyRate: number;
          dailyRate: number;
        };
      };
      sharing2: {
        ac: {
          available: boolean;
          count: number;
          monthlyRate: number;
          dailyRate: number;
        };
        nonAc: {
          available: boolean;
          count: number;
          monthlyRate: number;
          dailyRate: number;
        };
      };
      sharing3: {
        ac: {
          available: boolean;
          count: number;
          monthlyRate: number;
          dailyRate: number;
        };
        nonAc: {
          available: boolean;
          count: number;
          monthlyRate: number;
          dailyRate: number;
        };
      };
      sharing4: {
        ac: {
          available: boolean;
          count: number;
          monthlyRate: number;
          dailyRate: number;
        };
        nonAc: {
          available: boolean;
          count: number;
          monthlyRate: number;
          dailyRate: number;
        };
      };
      sharing5: {
        ac: {
          available: boolean;
          count: number;
          monthlyRate: number;
          dailyRate: number;
        };
        nonAc: {
          available: boolean;
          count: number;
          monthlyRate: number;
          dailyRate: number;
        };
      };
      sharing6: {
        ac: {
          available: boolean;
          count: number;
          monthlyRate: number;
          dailyRate: number;
        };
        nonAc: {
          available: boolean;
          count: number;
          monthlyRate: number;
          dailyRate: number;
        };
      };
    };
  };

  // Food Services
  foodServices: {
    isProvided: boolean;
    withMeals: {
      dailyRate: number;
      description: string;
      mealTypes: ('breakfast' | 'lunch' | 'dinner')[];
    };
    withoutMeals: {
      dailyRate: number;
      description: string;
    };
    kitchenFacilities: boolean;
    outsideFoodAllowed: boolean;
  };

  // Documents
  documents: {
    ownerIdProof: {
      type: 'aadhar' | 'pan' | 'passport' | 'driving_license';
      number: string;
      fileUrl?: string;
      verified: boolean;
    };
    businessRegistration?: {
      type: 'gst' | 'shop_act' | 'trade_license';
      number: string;
      fileUrl?: string;
      verified: boolean;
    };
    propertyDocuments: {
      type: 'ownership' | 'rental_agreement' | 'lease_deed';
      fileUrl?: string;
      verified: boolean;
    };
    bankDetails: {
      accountNumber: string;
      ifscCode: string;
      bankName: string;
      branchName: string;
      accountHolderName: string;
      accountType: 'savings' | 'current';
    };
  };

  // Photos
  photos: {
    exterior: string[];
    rooms: string[];
    commonAreas: string[];
    amenities: string[];
    kitchen?: string[];
  };

  // Registration Status
  status: 'draft' | 'submitted' | 'under_review' | 'approved' | 'rejected';
  submittedDate?: string;
  reviewedDate?: string;
  reviewedBy?: string;
  rejectionReason?: string;
  completionPercentage: number;
}

export interface MemberApplication {
  id: string;
  userId: string;
  hostelId: string;
  applicationDate: string;
  status: 'pending' | 'approved' | 'rejected' | 'waitlisted';
  priority: 'high' | 'medium' | 'low';
  
  // Member Details
  memberDetails: {
    name: string;
    email: string;
    phone: string;
    age: number;
    gender: 'male' | 'female' | 'other';
    occupation: string;
    company?: string;
    emergencyContact: {
      name: string;
      phone: string;
      relationship: string;
    };
  };

  // Booking Preferences
  preferences: {
    sharingType: SharingType; // 'sharing1' | 'sharing2' | 'sharing3' | 'sharing4' | 'sharing5' | 'sharing6'
    roomType: RoomType; // 'ac' | 'nonAc'
    foodPackage: 'with_meals' | 'without_meals';
    duration: number; // in days
    checkInDate: string;
    specialRequests?: string;
  };

  // Verification Status
  verification: {
    idProof: boolean;
    backgroundCheck: boolean;
    employmentVerification: boolean;
    previousHostelReference: boolean;
  };

  // Application Score
  score: {
    total: number;
    breakdown: {
      profileCompleteness: number;
      verification: number;
      references: number;
      paymentHistory: number;
    };
  };

  // Review Notes
  reviewNotes?: string;
  reviewedBy?: string;
  reviewedDate?: string;
}

export interface RoomAllocation {
  id: string;
  hostelId: string;
  roomNumber: string;
  bedNumber: string;
  sharingType: SharingType; // 'sharing1' | 'sharing2' | 'sharing3' | 'sharing4' | 'sharing5' | 'sharing6'
  roomType: RoomType; // 'ac' | 'nonAc'
  status: 'available' | 'occupied' | 'maintenance' | 'reserved';
  
  // Current Occupant
  currentOccupant?: {
    userId: string;
    name: string;
    checkInDate: string;
    expectedCheckOut: string;
    bookingId: string;
  };

  // Allocation History
  allocationHistory: Array<{
    userId: string;
    userName: string;
    checkInDate: string;
    checkOutDate?: string;
    bookingId: string;
    rating?: number;
  }>;

  // Maintenance
  lastCleaned: string;
  lastMaintenance: string;
  maintenanceNotes?: string;
}

export interface OwnerDashboardMetrics {
  occupancy: {
    current: number;
    target: number;
    trend: 'up' | 'down' | 'stable';
    lastMonth: number;
  };
  
  revenue: {
    thisMonth: number;
    lastMonth: number;
    projected: number;
    trend: 'up' | 'down' | 'stable';
  };

  applications: {
    pending: number;
    thisWeek: number;
    conversionRate: number;
    averageProcessingTime: number;
  };

  memberSatisfaction: {
    averageRating: number;
    totalReviews: number;
    responseRate: number;
    issueResolutionTime: number;
  };

  payments: {
    collected: number;
    pending: number;
    overdue: number;
    successRate: number;
  };
}

export interface BulkAction {
  type: 'approve' | 'reject' | 'waitlist' | 'priority_change';
  applicationIds: string[];
  reason?: string;
  notes?: string;
  newPriority?: 'high' | 'medium' | 'low';
}

export interface AllocationConflict {
  id: string;
  type: 'double_booking' | 'maintenance_overlap' | 'checkout_delay';
  bedNumber: string;
  conflictingBookings: string[];
  suggestedResolution: string;
  severity: 'low' | 'medium' | 'high';
  autoResolvable: boolean;
}

// Form validation schemas
export interface OwnerRegistrationFormData {
  // Step 1: Personal Info
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  alternatePhone: string;
  dateOfBirth: string;
  gender: string;

  // Step 2: Business Info
  businessName: string;
  businessType: string;
  gstNumber: string;
  panNumber: string;
  yearsOfExperience: string;
  previousHostelExperience: boolean;

  // Step 3: Hostel Info
  hostelName: string;
  hostelDescription: string;
  selectedCity: string;
  selectedArea: string;
  propertyType: string;
  totalFloors: string;
  totalRooms: string;
  totalBeds: string;

  // Step 4: Room Configuration & Pricing
  // 1-sharing (Single occupancy)
  sharing1AcAvailable: boolean;
  sharing1AcCount: string;
  sharing1AcMonthlyRate: string;
  sharing1AcDailyRate: string;
  sharing1NonAcAvailable: boolean;
  sharing1NonAcCount: string;
  sharing1NonAcMonthlyRate: string;
  sharing1NonAcDailyRate: string;

  // 2-sharing (Double occupancy)
  sharing2AcAvailable: boolean;
  sharing2AcCount: string;
  sharing2AcMonthlyRate: string;
  sharing2AcDailyRate: string;
  sharing2NonAcAvailable: boolean;
  sharing2NonAcCount: string;
  sharing2NonAcMonthlyRate: string;
  sharing2NonAcDailyRate: string;

  // 3-sharing (Triple occupancy)
  sharing3AcAvailable: boolean;
  sharing3AcCount: string;
  sharing3AcMonthlyRate: string;
  sharing3AcDailyRate: string;
  sharing3NonAcAvailable: boolean;
  sharing3NonAcCount: string;
  sharing3NonAcMonthlyRate: string;
  sharing3NonAcDailyRate: string;

  // 4-sharing (Quad occupancy)
  sharing4AcAvailable: boolean;
  sharing4AcCount: string;
  sharing4AcMonthlyRate: string;
  sharing4AcDailyRate: string;
  sharing4NonAcAvailable: boolean;
  sharing4NonAcCount: string;
  sharing4NonAcMonthlyRate: string;
  sharing4NonAcDailyRate: string;

  // 5-sharing (Five-bed occupancy)
  sharing5AcAvailable: boolean;
  sharing5AcCount: string;
  sharing5AcMonthlyRate: string;
  sharing5AcDailyRate: string;
  sharing5NonAcAvailable: boolean;
  sharing5NonAcCount: string;
  sharing5NonAcMonthlyRate: string;
  sharing5NonAcDailyRate: string;

  // 6-sharing (Six-bed occupancy)
  sharing6AcAvailable: boolean;
  sharing6AcCount: string;
  sharing6AcMonthlyRate: string;
  sharing6AcDailyRate: string;
  sharing6NonAcAvailable: boolean;
  sharing6NonAcCount: string;
  sharing6NonAcMonthlyRate: string;
  sharing6NonAcDailyRate: string;

  // Food Services
  foodServiceProvided: boolean;
  withMealsDailyRate: string;
  withoutMealsDailyRate: string;

  // Step 5: Documents
  ownerIdType: string;
  ownerIdNumber: string;
  businessRegNumber: string;
  propertyDocType: string;
  bankAccountNumber: string;
  bankIfscCode: string;
  bankName: string;
  accountHolderName: string;
}

export type RegistrationStep = 1 | 2 | 3 | 4 | 5 | 6;
export type ApplicationStatus = MemberApplication['status'];
export type ApplicationPriority = MemberApplication['priority'];
export type AllocationStatus = RoomAllocation['status'];

// Room sharing types
export type SharingType = 'sharing1' | 'sharing2' | 'sharing3' | 'sharing4' | 'sharing5' | 'sharing6';
export type RoomType = 'ac' | 'nonAc';

export interface RoomTypeConfig {
  available: boolean;
  count: number;
  monthlyRate: number;
  dailyRate: number;
}

export interface SharingConfig {
  ac: RoomTypeConfig;
  nonAc: RoomTypeConfig;
}

export interface RoomPricingMatrix {
  sharing1: SharingConfig;
  sharing2: SharingConfig;
  sharing3: SharingConfig;
  sharing4: SharingConfig;
  sharing5: SharingConfig;
  sharing6: SharingConfig;
}
