import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { 
  Plus,
  Building2,
  Layers,
  Users,
  DollarSign,
  FileText,
  AlertCircle,
  CheckCircle,
  ArrowRight
} from 'lucide-react';
import { FloorType, AddFloorRequest, HostelInfo } from '@/types/roomManagement';
import { submitAddFloorRequest } from '@/services/floorManagementService';
import { getHostelsByOwnerId } from '@/data/mockData';
import { toast } from '@/hooks/use-toast';

interface AddFloorDialogEnhancedProps {
  ownerId: string;
  onFloorAdded?: () => void;
  trigger?: React.ReactNode;
  preSelectedHostelId?: string;
}

export const AddFloorDialogEnhanced: React.FC<AddFloorDialogEnhancedProps> = ({
  ownerId,
  onFloorAdded,
  trigger,
  preSelectedHostelId
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Hostel Selection State
  const [availableHostels, setAvailableHostels] = useState<HostelInfo[]>([]);
  const [selectedHostel, setSelectedHostel] = useState<HostelInfo | null>(null);
  const [isLoadingHostels, setIsLoadingHostels] = useState(false);
  
  // Form State
  const [formData, setFormData] = useState<AddFloorRequest>({
    floorNumber: 1,
    floorType: 'residential',
    plannedRooms: 4,
    plannedBeds: 12,
    amenities: ['WiFi', 'CCTV', 'Power Backup', 'Water Supply'],
    details: {
      area: 1000,
      ceilingHeight: 9,
      hasElevatorAccess: false,
      hasStairAccess: true,
      hasFireExit: true,
      hasCommonArea: false,
      hasWiFi: true,
      hasCCTV: true,
      hasIntercom: true,
      hasPowerBackup: true,
      hasWaterSupply: true,
      description: ''
    },
    constructionTimeline: '3 months',
    estimatedCost: 500000,
    reason: '',
    expectedOccupancyIncrease: 12,
    businessJustification: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Load hostels when dialog opens
  useEffect(() => {
    if (isOpen) {
      loadHostels();
      setCurrentStep(1);
      setErrors({});
    }
  }, [isOpen]);

  // Auto-select hostel if preSelectedHostelId is provided
  useEffect(() => {
    if (preSelectedHostelId && availableHostels.length > 0) {
      const hostel = availableHostels.find(h => h.id === preSelectedHostelId);
      if (hostel) {
        setSelectedHostel(hostel);
        setCurrentStep(2); // Skip hostel selection step
        updateFormData('floorNumber', hostel.totalFloors + 1);
      }
    }
  }, [preSelectedHostelId, availableHostels]);

  const loadHostels = async () => {
    setIsLoadingHostels(true);
    try {
      const hostels = getHostelsByOwnerId(ownerId);
      setAvailableHostels(hostels);
      
      if (hostels.length === 1 && !preSelectedHostelId) {
        // Auto-select if only one hostel
        setSelectedHostel(hostels[0]);
        setCurrentStep(2);
        updateFormData('floorNumber', hostels[0].totalFloors + 1);
      }
    } catch (error) {
      console.error('Error loading hostels:', error);
      toast({
        title: "Error",
        description: "Failed to load hostels",
        variant: "destructive"
      });
    } finally {
      setIsLoadingHostels(false);
    }
  };

  const handleHostelSelect = (hostelId: string) => {
    const hostel = availableHostels.find(h => h.id === hostelId);
    if (hostel) {
      setSelectedHostel(hostel);
      updateFormData('floorNumber', hostel.totalFloors + 1);
      updateFormData('expectedOccupancyIncrease', formData.plannedBeds);
    }
  };

  const updateFormData = (field: keyof AddFloorRequest, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when field is updated
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const updateFormDetails = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      details: { ...prev.details, [field]: value }
    }));
  };

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    if (step === 1) {
      if (!selectedHostel) {
        newErrors.hostel = 'Please select a hostel';
      }
    }

    if (step === 2) {
      if (!formData.floorNumber || formData.floorNumber < 0) {
        newErrors.floorNumber = 'Floor number is required';
      }
      if (!formData.plannedRooms || formData.plannedRooms < 1) {
        newErrors.plannedRooms = 'Planned rooms must be at least 1';
      }
      if (!formData.plannedBeds || formData.plannedBeds < 1) {
        newErrors.plannedBeds = 'Planned beds must be at least 1';
      }
      if (!formData.details.area || formData.details.area < 1) {
        newErrors.area = 'Area is required';
      }
    }

    if (step === 3) {
      if (!formData.reason || formData.reason.length < 10) {
        newErrors.reason = 'Please provide a detailed reason (minimum 10 characters)';
      }
      if (!formData.businessJustification || formData.businessJustification.length < 20) {
        newErrors.businessJustification = 'Please provide business justification (minimum 20 characters)';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => prev - 1);
  };

  const handleSubmit = async () => {
    if (!selectedHostel || !validateStep(3)) return;

    setIsSubmitting(true);
    try {
      const result = await submitAddFloorRequest(selectedHostel.id, ownerId, formData);
      
      if (result.success) {
        toast({
          title: "Floor Addition Request Submitted",
          description: `Your request to add Floor ${formData.floorNumber} to ${selectedHostel.name} has been submitted for admin approval.`,
        });
        
        setIsOpen(false);
        if (onFloorAdded) {
          onFloorAdded();
        }
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to submit floor addition request",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error submitting floor request:', error);
      toast({
        title: "Error",
        description: "Failed to submit floor addition request",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setIsOpen(false);
    setCurrentStep(1);
    setSelectedHostel(null);
    setErrors({});
  };

  // Step 1: Hostel Selection
  const renderHostelSelection = () => (
    <div className="space-y-4">
      <div className="text-center">
        <Building2 className="mx-auto h-12 w-12 text-blue-500" />
        <h3 className="mt-2 text-lg font-medium">Select Hostel</h3>
        <p className="text-sm text-gray-500">Choose which hostel to add the floor to</p>
      </div>

      {isLoadingHostels ? (
        <div className="text-center py-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="text-sm text-gray-500 mt-2">Loading hostels...</p>
        </div>
      ) : availableHostels.length === 0 ? (
        <div className="text-center py-6">
          <AlertCircle className="mx-auto h-8 w-8 text-gray-400" />
          <h4 className="mt-2 text-sm font-medium text-gray-900">No Approved Hostels</h4>
          <p className="mt-1 text-sm text-gray-500">
            You don't have any approved hostels yet. Submit a hostel registration request first.
          </p>
        </div>
      ) : (
        <div className="space-y-3">
          <Label>Select Hostel *</Label>
          <Select value={selectedHostel?.id || ''} onValueChange={handleHostelSelect}>
            <SelectTrigger>
              <SelectValue placeholder="Choose a hostel" />
            </SelectTrigger>
            <SelectContent>
              {availableHostels.map((hostel) => (
                <SelectItem key={hostel.id} value={hostel.id}>
                  <div className="flex items-center space-x-2">
                    <Building2 className="h-4 w-4" />
                    <span>{hostel.name}</span>
                    <span className="text-muted-foreground">
                      - {hostel.city} ({hostel.totalFloors} floors)
                    </span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.hostel && (
            <p className="text-sm text-red-600">{errors.hostel}</p>
          )}

          {selectedHostel && (
            <Card className="border-green-200 bg-green-50">
              <CardContent className="pt-4">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <div>
                    <h4 className="font-medium text-green-900">{selectedHostel.name}</h4>
                    <p className="text-sm text-green-700">
                      {selectedHostel.address}, {selectedHostel.city}
                    </p>
                    <p className="text-xs text-green-600 mt-1">
                      Current: {selectedHostel.totalFloors} floors • {selectedHostel.totalRooms} rooms
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );

  // Step 2: Floor Details
  const renderFloorDetails = () => (
    <div className="space-y-4">
      <div className="text-center">
        <Layers className="mx-auto h-12 w-12 text-green-500" />
        <h3 className="mt-2 text-lg font-medium">Floor Details</h3>
        <p className="text-sm text-gray-500">Configure the new floor specifications</p>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="floorNumber">Floor Number *</Label>
          <Input
            id="floorNumber"
            type="number"
            value={formData.floorNumber}
            onChange={(e) => updateFormData('floorNumber', parseInt(e.target.value))}
            min="0"
          />
          {errors.floorNumber && (
            <p className="text-sm text-red-600 mt-1">{errors.floorNumber}</p>
          )}
        </div>

        <div>
          <Label htmlFor="floorType">Floor Type *</Label>
          <Select value={formData.floorType} onValueChange={(value: FloorType) => updateFormData('floorType', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="residential">Residential</SelectItem>
              <SelectItem value="commercial">Commercial</SelectItem>
              <SelectItem value="mixed">Mixed Use</SelectItem>
              <SelectItem value="common">Common Area</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="plannedRooms">Planned Rooms *</Label>
          <Input
            id="plannedRooms"
            type="number"
            value={formData.plannedRooms}
            onChange={(e) => updateFormData('plannedRooms', parseInt(e.target.value))}
            min="1"
          />
          {errors.plannedRooms && (
            <p className="text-sm text-red-600 mt-1">{errors.plannedRooms}</p>
          )}
        </div>

        <div>
          <Label htmlFor="plannedBeds">Planned Beds *</Label>
          <Input
            id="plannedBeds"
            type="number"
            value={formData.plannedBeds}
            onChange={(e) => updateFormData('plannedBeds', parseInt(e.target.value))}
            min="1"
          />
          {errors.plannedBeds && (
            <p className="text-sm text-red-600 mt-1">{errors.plannedBeds}</p>
          )}
        </div>

        <div>
          <Label htmlFor="area">Floor Area (sq ft) *</Label>
          <Input
            id="area"
            type="number"
            value={formData.details.area}
            onChange={(e) => updateFormDetails('area', parseInt(e.target.value))}
            min="1"
          />
          {errors.area && (
            <p className="text-sm text-red-600 mt-1">{errors.area}</p>
          )}
        </div>

        <div>
          <Label htmlFor="estimatedCost">Estimated Cost (₹) *</Label>
          <Input
            id="estimatedCost"
            type="number"
            value={formData.estimatedCost}
            onChange={(e) => updateFormData('estimatedCost', parseInt(e.target.value))}
            min="0"
          />
        </div>
      </div>
    </div>
  );

  // Step 3: Business Justification
  const renderBusinessJustification = () => (
    <div className="space-y-4">
      <div className="text-center">
        <FileText className="mx-auto h-12 w-12 text-purple-500" />
        <h3 className="mt-2 text-lg font-medium">Business Justification</h3>
        <p className="text-sm text-gray-500">Provide reasoning for this floor addition</p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="reason">Reason for Addition *</Label>
          <Textarea
            id="reason"
            value={formData.reason}
            onChange={(e) => updateFormData('reason', e.target.value)}
            placeholder="Explain why this floor is needed"
            rows={3}
          />
          {errors.reason && (
            <p className="text-sm text-red-600 mt-1">{errors.reason}</p>
          )}
        </div>

        <div>
          <Label htmlFor="businessJustification">Business Justification *</Label>
          <Textarea
            id="businessJustification"
            value={formData.businessJustification}
            onChange={(e) => updateFormData('businessJustification', e.target.value)}
            placeholder="Explain the business benefits and ROI of this addition"
            rows={3}
          />
          {errors.businessJustification && (
            <p className="text-sm text-red-600 mt-1">{errors.businessJustification}</p>
          )}
        </div>

        <div>
          <Label htmlFor="constructionTimeline">Construction Timeline</Label>
          <Input
            id="constructionTimeline"
            value={formData.constructionTimeline}
            onChange={(e) => updateFormData('constructionTimeline', e.target.value)}
            placeholder="e.g., 3 months"
          />
        </div>
      </div>

      {/* Summary */}
      {selectedHostel && (
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="text-sm">Request Summary</CardTitle>
          </CardHeader>
          <CardContent className="text-sm space-y-1">
            <div><strong>Hostel:</strong> {selectedHostel.name}</div>
            <div><strong>Floor:</strong> {formData.floorNumber} ({formData.floorType})</div>
            <div><strong>Capacity:</strong> {formData.plannedRooms} rooms, {formData.plannedBeds} beds</div>
            <div><strong>Area:</strong> {formData.details.area} sq ft</div>
            <div><strong>Cost:</strong> ₹{formData.estimatedCost.toLocaleString()}</div>
          </CardContent>
        </Card>
      )}
    </div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Floor
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Layers className="h-5 w-5" />
            Add New Floor {selectedHostel && `- ${selectedHostel.name}`}
          </DialogTitle>
          <DialogDescription>
            Submit a request to add a new floor to your hostel. This request will be reviewed by administrators.
          </DialogDescription>
        </DialogHeader>

        {/* Progress Steps */}
        <div className="flex items-center justify-center space-x-4 py-4">
          {[1, 2, 3].map((step) => (
            <div key={step} className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                currentStep >= step 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-gray-200 text-gray-600'
              }`}>
                {step}
              </div>
              {step < 3 && (
                <ArrowRight className={`h-4 w-4 mx-2 ${
                  currentStep > step ? 'text-blue-500' : 'text-gray-400'
                }`} />
              )}
            </div>
          ))}
        </div>

        {/* Step Content */}
        <div className="min-h-[400px]">
          {currentStep === 1 && renderHostelSelection()}
          {currentStep === 2 && renderFloorDetails()}
          {currentStep === 3 && renderBusinessJustification()}
        </div>

        <DialogFooter>
          <div className="flex justify-between w-full">
            <div>
              {currentStep > 1 && (
                <Button variant="outline" onClick={handlePrevious}>
                  Previous
                </Button>
              )}
            </div>
            <div className="space-x-2">
              <Button variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              {currentStep < 3 ? (
                <Button 
                  onClick={handleNext}
                  disabled={currentStep === 1 && !selectedHostel}
                >
                  Next
                </Button>
              ) : (
                <Button 
                  onClick={handleSubmit} 
                  disabled={isSubmitting || !selectedHostel}
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Submitting...
                    </>
                  ) : (
                    'Submit Request'
                  )}
                </Button>
              )}
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
