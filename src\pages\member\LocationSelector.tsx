import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  MapPin, 
  Building2, 
  Loader2,
  ChevronRight,
  Star,
  Bed
} from 'lucide-react';
import { mockCities, mockHostels, getCityById, getHostelsByArea, type City, type Area, type Hostel } from '@/data/mockData';

interface LocationSelectorProps {
  onSelectionChange: (selection: {
    city?: City;
    area?: Area;
    hostel?: Hostel;
  }) => void;
  selectedCity?: string;
  selectedArea?: string;
  selectedHostel?: string;
  className?: string;
}

export const LocationSelector: React.FC<LocationSelectorProps> = ({
  onSelectionChange,
  selectedCity,
  selectedArea,
  selectedHostel,
  className
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [loadingStep, setLoadingStep] = useState<'city' | 'area' | 'hostel' | null>(null);
  
  const currentCity = selectedCity ? getCityById(selectedCity) : undefined;
  const currentArea = currentCity && selectedArea ? 
    currentCity.areas.find(area => area.id === selectedArea) : undefined;
  const availableHostels = selectedArea ? getHostelsByArea(selectedArea) : [];
  const currentHostel = selectedHostel ? 
    availableHostels.find(hostel => hostel.id === selectedHostel) : undefined;

  const handleCityChange = async (cityId: string) => {
    setIsLoading(true);
    setLoadingStep('city');
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const city = getCityById(cityId);
    onSelectionChange({ city });
    
    setIsLoading(false);
    setLoadingStep(null);
  };

  const handleAreaChange = async (areaId: string) => {
    if (!currentCity) return;
    
    setIsLoading(true);
    setLoadingStep('area');
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const area = currentCity.areas.find(a => a.id === areaId);
    onSelectionChange({ 
      city: currentCity, 
      area 
    });
    
    setIsLoading(false);
    setLoadingStep(null);
  };

  const handleHostelChange = async (hostelId: string) => {
    if (!currentCity || !currentArea) return;
    
    setIsLoading(true);
    setLoadingStep('hostel');
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 200));
    
    const hostel = availableHostels.find(h => h.id === hostelId);
    onSelectionChange({ 
      city: currentCity, 
      area: currentArea, 
      hostel 
    });
    
    setIsLoading(false);
    setLoadingStep(null);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <MapPin className="h-5 w-5" />
          <span>Select Location</span>
        </CardTitle>
        <CardDescription>
          Choose your preferred city, area, and hostel
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Breadcrumb */}
        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
          <span>Location:</span>
          {currentCity && (
            <>
              <ChevronRight className="h-3 w-3" />
              <span className="font-medium">{currentCity.name}</span>
            </>
          )}
          {currentArea && (
            <>
              <ChevronRight className="h-3 w-3" />
              <span className="font-medium">{currentArea.name}</span>
            </>
          )}
          {currentHostel && (
            <>
              <ChevronRight className="h-3 w-3" />
              <span className="font-medium">{currentHostel.name}</span>
            </>
          )}
        </div>

        {/* City Selection */}
        <div className="space-y-2">
          <label className="text-sm font-medium">City</label>
          <Select value={selectedCity} onValueChange={handleCityChange} disabled={isLoading}>
            <SelectTrigger>
              <SelectValue placeholder="Select a city" />
              {loadingStep === 'city' && <Loader2 className="h-4 w-4 animate-spin" />}
            </SelectTrigger>
            <SelectContent>
              {mockCities.map((city) => (
                <SelectItem key={city.id} value={city.id}>
                  <div className="flex items-center justify-between w-full">
                    <span>{city.name}</span>
                    <Badge variant="secondary" className="ml-2">
                      {city.state}
                    </Badge>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Area Selection */}
        {currentCity && (
          <div className="space-y-2">
            <label className="text-sm font-medium">Area</label>
            <Select value={selectedArea} onValueChange={handleAreaChange} disabled={isLoading}>
              <SelectTrigger>
                <SelectValue placeholder="Select an area" />
                {loadingStep === 'area' && <Loader2 className="h-4 w-4 animate-spin" />}
              </SelectTrigger>
              <SelectContent>
                {currentCity.areas.map((area) => (
                  <SelectItem key={area.id} value={area.id}>
                    {area.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Hostel Selection */}
        {currentArea && availableHostels.length > 0 && (
          <div className="space-y-2">
            <label className="text-sm font-medium">Hostel</label>
            <Select value={selectedHostel} onValueChange={handleHostelChange} disabled={isLoading}>
              <SelectTrigger>
                <SelectValue placeholder="Select a hostel" />
                {loadingStep === 'hostel' && <Loader2 className="h-4 w-4 animate-spin" />}
              </SelectTrigger>
              <SelectContent>
                {availableHostels.map((hostel) => (
                  <SelectItem key={hostel.id} value={hostel.id}>
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center space-x-2">
                        <Building2 className="h-4 w-4" />
                        <span>{hostel.name}</span>
                      </div>
                      <div className="flex items-center space-x-2 ml-2">
                        <div className="flex items-center space-x-1">
                          <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                          <span className="text-xs">{hostel.rating}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Bed className="h-3 w-3" />
                          <span className="text-xs">{hostel.availableBeds} available</span>
                        </div>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* No Hostels Message */}
        {currentArea && availableHostels.length === 0 && (
          <div className="text-center py-4 text-muted-foreground">
            <Building2 className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No hostels available in this area</p>
            <p className="text-sm">Please select a different area</p>
          </div>
        )}

        {/* Selected Hostel Details */}
        {currentHostel && (
          <Card className="bg-muted/50">
            <CardContent className="pt-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">{currentHostel.name}</h4>
                  <Badge className={currentHostel.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                    {currentHostel.status}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">{currentHostel.address}</p>
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span>{currentHostel.rating}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Bed className="h-4 w-4" />
                      <span>{currentHostel.availableBeds}/{currentHostel.totalBeds} beds</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">From ₹{currentHostel.bedTypes.standard.dailyRate}/day</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </CardContent>
    </Card>
  );
};
