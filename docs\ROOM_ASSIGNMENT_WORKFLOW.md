# Complete Room and Bed Assignment Workflow

## Overview

This document explains the complete workflow for hostel owners to manage multiple properties, floors, rooms, and beds within the HostelHub system. The system follows a hierarchical structure: **Hostel → Floor → Room → Bed**.

## 1. Multi-Hostel Management

### Data Structure
```typescript
interface HostelInfo {
  id: string;
  name: string;
  ownerId: string;
  totalFloors: number;
  totalRooms: number;
  totalBeds: number;
  floors: string[]; // Array of floor IDs
}
```

### Implementation
- **Data Loading**: `getHostelsByOwnerId(ownerId)` filters hostels by owner
- **Dynamic Filtering**: All subsequent data filters by selected `hostelId`
- **UI Component**: `HostelSelector` provides dropdown with hostel details

### Workflow
1. Owner logs in and navigates to Room Management
2. System loads all hostels owned by the user
3. Owner selects hostel from dropdown
4. All floor, room, and bed data filters automatically
5. Statistics and management options update for selected hostel

## 2. Room-to-Floor Assignment

### Data Structure
```typescript
interface Room {
  id: string;
  hostelId: string;  // Links to parent hostel
  floorId: string;   // Links to specific floor
  roomNumber: string;
  floor: number;     // Floor number for display
  capacity: number;
  beds: Bed[];
}

interface Floor {
  id: string;
  hostelId: string;
  floorNumber: number;
  totalRooms: number;
  rooms: string[];   // Array of room IDs
}
```

### Assignment Process
1. **Room Creation**:
   - Owner clicks "Add Room"
   - System loads available floors: `getAvailableFloorsForRooms(hostelId)`
   - Owner selects target floor
   - Room assigned via `floorId` and `hostelId`

2. **Room Filtering**:
   - By hostel: `getRoomsByHostelId(hostelId)`
   - By floor: `getRoomsByFloorId(floorId)`
   - Combined: `getRoomsByHostelAndFloor(hostelId, floorNumber)`

3. **Room Transfer**:
   - Update `room.floorId` to new floor
   - Update `room.floor` number
   - Recalculate floor statistics
   - Requires admin approval for structural changes

## 3. Room-to-Hostel Assignment

### Data Relationships
```typescript
// Foreign key relationships
Room.hostelId → Hostel.id
Room.floorId → Floor.id
Floor.hostelId → Hostel.id
```

### Implementation
- **Hostel Filtering**: All rooms filtered by `hostelId`
- **Data Integrity**: Rooms cannot exist without valid `hostelId`
- **Transfer Process**: Update `room.hostelId` for hostel transfers
- **Validation**: Ensure target hostel has compatible floors

### Transfer Workflow
1. Select room to transfer
2. Choose destination hostel
3. Select compatible floor in destination hostel
4. Update room's `hostelId` and `floorId`
5. Submit for admin approval
6. Update statistics for both hostels

## 4. Bed-to-Room Assignment

### Data Structure
```typescript
interface Bed {
  id: string;
  roomId: string;    // Links to parent room
  bedNumber: string; // Unique within room
  bedType: BedType;
  status: 'available' | 'occupied' | 'maintenance' | 'reserved';
  occupiedBy?: string; // Member ID if occupied
  hasLocker: boolean;
  lockerNumber?: string;
}
```

### Assignment Rules
- **Capacity Limit**: Total beds cannot exceed `room.capacity`
- **Unique Numbers**: Bed numbers must be unique within room
- **Room Link**: `bed.roomId` must match parent `room.id`
- **Status Tracking**: Real-time occupancy management

### Bed Management Workflow
1. **Adding Beds**:
   - Select room to manage
   - Check available capacity: `room.capacity - room.beds.length`
   - Enter bed details (number, type, features)
   - Assign unique bed number within room
   - Set initial status (usually 'available')

2. **Occupancy Management**:
   - Assign member to bed: `bed.occupiedBy = memberId`
   - Update status: `bed.status = 'occupied'`
   - Track check-in/check-out dates
   - Calculate revenue based on occupancy

## 5. Data Relationships & Filtering

### Hierarchical Structure
```
Hostel (1) → Floor (N) → Room (N) → Bed (N)
```

### Key Relationships
```typescript
// One-to-Many relationships
Hostel.id ← Floor.hostelId (multiple floors per hostel)
Hostel.id ← Room.hostelId (multiple rooms per hostel)
Floor.id ← Room.floorId (multiple rooms per floor)
Room.id ← Bed.roomId (multiple beds per room)
```

### Filtering Functions
```typescript
// Hostel level
getHostelsByOwnerId(ownerId): HostelInfo[]

// Floor level
getFloorsByHostelId(hostelId): Floor[]
getFloorById(floorId): Floor

// Room level
getRoomsByHostelId(hostelId): Room[]
getRoomsByFloorId(floorId): Room[]
getRoomById(roomId): Room

// Bed level
getBedsByRoomId(roomId): Bed[]
getBedById(bedId): Bed
```

## 6. Owner Panel Workflow

### Step-by-Step Process

#### Step 1: Hostel Selection
```typescript
// Load owner's hostels
const hostels = await getOwnerHostels(ownerId);

// Handle selection
const handleHostelChange = (hostelId: string, hostel: HostelInfo) => {
  setSelectedHostel(hostel);
  setSelectedFloors([]); // Reset floor selection
  loadRoomData(); // Reload data for new hostel
};
```

#### Step 2: Floor Management
```typescript
// Load floors for selected hostel
const floors = await getHostelFloors(selectedHostel.id);

// Filter rooms by selected floors
const filteredRooms = selectedFloors.length > 0 
  ? rooms.filter(room => selectedFloors.includes(room.floorId))
  : rooms;
```

#### Step 3: Room Operations
```typescript
// Add new room
const addRoom = async (roomData: AddRoomRequest) => {
  const newRoom = {
    ...roomData,
    hostelId: selectedHostel.id,
    floorId: selectedFloor.id,
    id: generateRoomId()
  };
  
  return await submitAddRoomRequest(newRoom);
};
```

#### Step 4: Bed Management
```typescript
// Add bed to room
const addBed = async (roomId: string, bedData: AddBedRequest) => {
  const room = getRoomById(roomId);
  
  // Validate capacity
  if (room.beds.length >= room.capacity) {
    throw new Error('Room at maximum capacity');
  }
  
  const newBed = {
    ...bedData,
    roomId: roomId,
    id: generateBedId()
  };
  
  return await submitAddBedRequest(newBed);
};
```

## 7. Statistics & Analytics

### Hierarchical Aggregation
```typescript
// Bed level statistics
const bedOccupancy = bed.status === 'occupied' ? 1 : 0;

// Room level statistics
const roomOccupancy = room.beds.filter(bed => 
  bed.status === 'occupied').length / room.capacity;

// Floor level statistics
const floorOccupancy = floorRooms.reduce((sum, room) => 
  sum + room.currentOccupancy, 0) / floorRooms.reduce((sum, room) => 
  sum + room.capacity, 0);

// Hostel level statistics
const hostelOccupancy = hostelRooms.reduce((sum, room) => 
  sum + room.currentOccupancy, 0) / hostelRooms.reduce((sum, room) => 
  sum + room.capacity, 0);
```

### Revenue Calculation
```typescript
// Calculate revenue at each level
const calculateRevenue = (level: 'bed' | 'room' | 'floor' | 'hostel', id: string) => {
  switch (level) {
    case 'bed':
      const bed = getBedById(id);
      return bed.status === 'occupied' ? bed.dailyRate : 0;
      
    case 'room':
      const room = getRoomById(id);
      return room.beds.reduce((sum, bed) => 
        sum + (bed.status === 'occupied' ? room.pricing.dailyRate / room.capacity : 0), 0);
        
    case 'floor':
      const floorRooms = getRoomsByFloorId(id);
      return floorRooms.reduce((sum, room) => 
        sum + calculateRevenue('room', room.id), 0);
        
    case 'hostel':
      const hostelRooms = getRoomsByHostelId(id);
      return hostelRooms.reduce((sum, room) => 
        sum + calculateRevenue('room', room.id), 0);
  }
};
```

## 8. Approval Workflows

### Room Addition
1. Owner submits room request with floor assignment
2. Admin reviews request in approval panel
3. Admin can approve/reject with comments
4. Approved rooms added to system
5. Floor statistics updated automatically

### Floor Addition
1. Owner submits floor request with specifications
2. Admin reviews structural plans and permits
3. Admin approves/rejects with detailed review
4. Approved floors available for room assignment
5. Hostel capacity updated

### Bed Addition
- **Immediate Approval**: If within room capacity
- **Admin Approval**: If exceeding original room design
- **Automatic Updates**: Statistics and availability

## 9. UI Components & Integration

### Key Components
- `HostelSelector`: Multi-hostel dropdown with details
- `FloorFilter`: Floor selection and filtering
- `FloorStatistics`: Floor-wise analytics
- `AddFloorDialog`: Floor addition workflow
- `AddRoomDialog`: Room creation with floor selection
- `AddBedDialog`: Bed management within rooms

### Data Flow
```
User Action → Component State → Service Call → Data Update → UI Refresh
```

### Real-time Updates
- Statistics recalculated on data changes
- Occupancy tracking updates immediately
- Revenue calculations refresh automatically
- Availability status synchronized across components

This comprehensive workflow ensures efficient management of complex hostel operations while maintaining data integrity and providing real-time insights for hostel owners.
