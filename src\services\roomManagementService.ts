// Room Management Service for hostel owners

import { 
  Room, 
  Bed, 
  RoomModificationRequest, 
  AddRoomRequest, 
  AddBedsRequest, 
  ModifyRoomRequest,
  RoomStatistics,
  RoomSearchFilters
} from '@/types/roomManagement';
import { 
  mockRooms, 
  mockRoomModificationRequests,
  getRoomsByHostelId,
  getRoomById,
  getBedsByRoomId,
  getRoomModificationRequestsByHostelId
} from '@/data/mockData';

export interface RoomOperationResult {
  success: boolean;
  data?: any;
  error?: string;
  requestId?: string;
}

// Generate unique IDs
const generateRoomId = (): string => `room_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
const generateBedId = (): string => `bed_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
const generateRequestId = (): string => `rmr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

// Add new room request (requires admin approval)
export const submitAddRoomRequest = async (
  hostelId: string,
  ownerId: string,
  roomData: AddRoomRequest
): Promise<RoomOperationResult> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Validate room number uniqueness
    const existingRooms = getRoomsByHostelId(hostelId);
    const roomExists = existingRooms.some(room => room.roomNumber === roomData.roomNumber);
    
    if (roomExists) {
      return {
        success: false,
        error: `Room number ${roomData.roomNumber} already exists`
      };
    }

    // Create room modification request
    const requestId = generateRequestId();
    const newRequest: RoomModificationRequest = {
      id: requestId,
      hostelId,
      ownerId,
      requestType: 'add_room',
      status: 'pending',
      submittedAt: new Date().toISOString(),
      requestDetails: roomData
    };

    // Add to mock data
    mockRoomModificationRequests.push(newRequest);

    return {
      success: true,
      requestId,
      data: newRequest
    };

  } catch (error) {
    console.error('Error submitting add room request:', error);
    return {
      success: false,
      error: 'Failed to submit room addition request'
    };
  }
};

// Add beds to existing room (immediate for small additions, approval required for major changes)
export const addBedsToRoom = async (
  roomId: string,
  ownerId: string,
  bedsData: Omit<Bed, 'id' | 'roomId' | 'createdAt' | 'updatedAt'>[]
): Promise<RoomOperationResult> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    const room = getRoomById(roomId);
    if (!room) {
      return {
        success: false,
        error: 'Room not found'
      };
    }

    // Check if adding beds exceeds reasonable capacity
    const newBedCount = bedsData.length;
    const currentBedCount = room.beds.length;
    const totalAfterAddition = currentBedCount + newBedCount;

    // If adding more than 2 beds or total exceeds 6, require approval
    if (newBedCount > 2 || totalAfterAddition > 6) {
      const requestId = generateRequestId();
      const addBedsRequest: AddBedsRequest = {
        roomId,
        newBeds: bedsData,
        reason: 'Adding additional beds to increase capacity',
        capacityIncrease: newBedCount
      };

      const newRequest: RoomModificationRequest = {
        id: requestId,
        hostelId: room.hostelId,
        ownerId,
        requestType: 'add_beds',
        status: 'pending',
        submittedAt: new Date().toISOString(),
        requestDetails: addBedsRequest
      };

      mockRoomModificationRequests.push(newRequest);

      return {
        success: true,
        requestId,
        data: { message: 'Request submitted for admin approval', request: newRequest }
      };
    }

    // For small additions, add beds immediately
    const newBeds: Bed[] = bedsData.map(bedData => ({
      ...bedData,
      id: generateBedId(),
      roomId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }));

    // Update room in mock data
    const roomIndex = mockRooms.findIndex(r => r.id === roomId);
    if (roomIndex !== -1) {
      mockRooms[roomIndex].beds.push(...newBeds);
      mockRooms[roomIndex].capacity += newBedCount;
      mockRooms[roomIndex].updatedAt = new Date().toISOString();
    }

    return {
      success: true,
      data: { addedBeds: newBeds, room: mockRooms[roomIndex] }
    };

  } catch (error) {
    console.error('Error adding beds to room:', error);
    return {
      success: false,
      error: 'Failed to add beds to room'
    };
  }
};

// Update room details (immediate for minor changes, approval required for major changes)
export const updateRoomDetails = async (
  roomId: string,
  ownerId: string,
  updates: Partial<Room>
): Promise<RoomOperationResult> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800));

    const room = getRoomById(roomId);
    if (!room) {
      return {
        success: false,
        error: 'Room not found'
      };
    }

    // Check if updates require approval
    const requiresApproval = !!(
      updates.roomType || 
      updates.capacity || 
      (updates.pricing && (
        Math.abs((updates.pricing.dailyRate || room.pricing.dailyRate) - room.pricing.dailyRate) > room.pricing.dailyRate * 0.2 ||
        Math.abs((updates.pricing.monthlyRate || room.pricing.monthlyRate) - room.pricing.monthlyRate) > room.pricing.monthlyRate * 0.2
      ))
    );

    if (requiresApproval) {
      const requestId = generateRequestId();
      const modifyRequest: ModifyRoomRequest = {
        roomId,
        changes: updates,
        reason: 'Updating room configuration and pricing'
      };

      const newRequest: RoomModificationRequest = {
        id: requestId,
        hostelId: room.hostelId,
        ownerId,
        requestType: 'modify_room',
        status: 'pending',
        submittedAt: new Date().toISOString(),
        requestDetails: modifyRequest
      };

      mockRoomModificationRequests.push(newRequest);

      return {
        success: true,
        requestId,
        data: { message: 'Request submitted for admin approval', request: newRequest }
      };
    }

    // For minor updates, apply immediately
    const roomIndex = mockRooms.findIndex(r => r.id === roomId);
    if (roomIndex !== -1) {
      mockRooms[roomIndex] = {
        ...mockRooms[roomIndex],
        ...updates,
        updatedAt: new Date().toISOString()
      };
    }

    return {
      success: true,
      data: { room: mockRooms[roomIndex] }
    };

  } catch (error) {
    console.error('Error updating room details:', error);
    return {
      success: false,
      error: 'Failed to update room details'
    };
  }
};

// Update bed status (immediate)
export const updateBedStatus = async (
  bedId: string,
  newStatus: Bed['status'],
  occupiedBy?: string,
  reservedBy?: string,
  reservedUntil?: string
): Promise<RoomOperationResult> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Find and update bed
    let bedFound = false;
    for (const room of mockRooms) {
      const bedIndex = room.beds.findIndex(bed => bed.id === bedId);
      if (bedIndex !== -1) {
        room.beds[bedIndex] = {
          ...room.beds[bedIndex],
          status: newStatus,
          occupiedBy: newStatus === 'occupied' ? occupiedBy : undefined,
          reservedBy: newStatus === 'reserved' ? reservedBy : undefined,
          reservedUntil: newStatus === 'reserved' ? reservedUntil : undefined,
          updatedAt: new Date().toISOString()
        };

        // Update room occupancy
        room.currentOccupancy = room.beds.filter(bed => bed.status === 'occupied').length;
        room.updatedAt = new Date().toISOString();

        bedFound = true;
        break;
      }
    }

    if (!bedFound) {
      return {
        success: false,
        error: 'Bed not found'
      };
    }

    return {
      success: true,
      data: { message: 'Bed status updated successfully' }
    };

  } catch (error) {
    console.error('Error updating bed status:', error);
    return {
      success: false,
      error: 'Failed to update bed status'
    };
  }
};

// Get room statistics for a hostel
export const getRoomStatistics = async (hostelId: string): Promise<RoomOperationResult> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    const rooms = getRoomsByHostelId(hostelId);
    const totalRooms = rooms.length;
    const totalBeds = rooms.reduce((sum, room) => sum + room.beds.length, 0);
    const occupiedBeds = rooms.reduce((sum, room) => 
      sum + room.beds.filter(bed => bed.status === 'occupied').length, 0);
    const availableBeds = rooms.reduce((sum, room) => 
      sum + room.beds.filter(bed => bed.status === 'available').length, 0);
    const maintenanceBeds = rooms.reduce((sum, room) => 
      sum + room.beds.filter(bed => bed.status === 'maintenance').length, 0);
    const reservedBeds = rooms.reduce((sum, room) => 
      sum + room.beds.filter(bed => bed.status === 'reserved').length, 0);

    const occupancyRate = totalBeds > 0 ? (occupiedBeds / totalBeds) * 100 : 0;

    // Calculate revenue
    const dailyRevenue = rooms.reduce((sum, room) => {
      const occupiedBedsInRoom = room.beds.filter(bed => bed.status === 'occupied').length;
      return sum + (occupiedBedsInRoom * room.pricing.dailyRate / room.capacity);
    }, 0);

    const monthlyRevenue = dailyRevenue * 30;
    const averageRatePerBed = rooms.length > 0 ? 
      rooms.reduce((sum, room) => sum + room.pricing.dailyRate, 0) / rooms.length : 0;

    const statistics: RoomStatistics = {
      totalRooms,
      totalBeds,
      occupiedBeds,
      availableBeds,
      maintenanceBeds,
      reservedBeds,
      occupancyRate,
      roomTypeBreakdown: {},
      revenueStats: {
        dailyRevenue,
        monthlyRevenue,
        averageRatePerBed,
        totalSecurityDeposits: occupiedBeds * 5500 // Average security deposit
      }
    };

    return {
      success: true,
      data: statistics
    };

  } catch (error) {
    console.error('Error getting room statistics:', error);
    return {
      success: false,
      error: 'Failed to get room statistics'
    };
  }
};

// Search and filter rooms
export const searchRooms = async (
  hostelId: string,
  filters: RoomSearchFilters
): Promise<RoomOperationResult> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 400));

    let rooms = getRoomsByHostelId(hostelId);

    // Apply filters
    if (filters.roomType && filters.roomType.length > 0) {
      rooms = rooms.filter(room => filters.roomType!.includes(room.roomType));
    }

    if (filters.status && filters.status.length > 0) {
      rooms = rooms.filter(room => filters.status!.includes(room.status));
    }

    if (filters.floor && filters.floor.length > 0) {
      rooms = rooms.filter(room => filters.floor!.includes(room.floor));
    }

    if (filters.hasAC !== undefined) {
      rooms = rooms.filter(room => room.details.hasAC === filters.hasAC);
    }

    if (filters.hasAttachedBathroom !== undefined) {
      rooms = rooms.filter(room => room.details.hasAttachedBathroom === filters.hasAttachedBathroom);
    }

    if (filters.priceRange) {
      const { min, max, period } = filters.priceRange;
      rooms = rooms.filter(room => {
        const price = period === 'daily' ? room.pricing.dailyRate :
                     period === 'weekly' ? room.pricing.weeklyRate :
                     room.pricing.monthlyRate;
        return price >= min && price <= max;
      });
    }

    if (filters.capacity) {
      const { min, max } = filters.capacity;
      rooms = rooms.filter(room => room.capacity >= min && room.capacity <= max);
    }

    return {
      success: true,
      data: rooms
    };

  } catch (error) {
    console.error('Error searching rooms:', error);
    return {
      success: false,
      error: 'Failed to search rooms'
    };
  }
};
