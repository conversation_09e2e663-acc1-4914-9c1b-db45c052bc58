import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Building2,
  User,
  FileText,
  Camera,
  DollarSign,
  CreditCard,
  ArrowRight,
  ArrowLeft,
  CheckCircle,
  Upload,
  MapPin,
  Phone,
  Mail,
  Calendar,
  Briefcase,
  Home,
  Bed,
  Utensils,
  Shield,
  AlertTriangle,
  Info,
  Send
} from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from '@/hooks/use-toast';
import { LocationSelector } from '@/pages/member/LocationSelector';
import { OwnerRegistrationFormData, RegistrationStep } from '@/types/owner';
import { submitHostelRegistration } from '@/services/registrationService';
import { useAuth } from '@/contexts/AuthContext';

// Validation schema
const ownerRegistrationSchema = z.object({
  // Personal Info
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().min(10, 'Phone number must be at least 10 digits'),
  alternatePhone: z.string().optional(),
  dateOfBirth: z.string().min(1, 'Date of birth is required'),
  gender: z.string().min(1, 'Please select gender'),

  // Business Info
  businessName: z.string().min(2, 'Business name is required'),
  businessType: z.string().min(1, 'Please select business type'),
  gstNumber: z.string().optional(),
  panNumber: z.string().min(10, 'PAN number is required'),
  yearsOfExperience: z.string().min(1, 'Years of experience is required'),
  previousHostelExperience: z.boolean(),

  // Hostel Info
  hostelName: z.string().min(2, 'Hostel name is required'),
  hostelDescription: z.string().min(50, 'Description must be at least 50 characters'),
  selectedCity: z.string().min(1, 'Please select a city'),
  selectedArea: z.string().min(1, 'Please select an area'),
  propertyType: z.string().min(1, 'Please select property type'),
  totalFloors: z.string().min(1, 'Number of floors is required'),
  totalRooms: z.string().min(1, 'Number of rooms is required'),
  totalBeds: z.string().min(1, 'Number of beds is required'),

  // Room Configuration & Pricing
  // 1-sharing (Single occupancy)
  sharing1AcAvailable: z.boolean(),
  sharing1AcCount: z.string().optional(),
  sharing1AcMonthlyRate: z.string().optional(),
  sharing1AcDailyRate: z.string().optional(),
  sharing1NonAcAvailable: z.boolean(),
  sharing1NonAcCount: z.string().optional(),
  sharing1NonAcMonthlyRate: z.string().optional(),
  sharing1NonAcDailyRate: z.string().optional(),

  // 2-sharing (Double occupancy)
  sharing2AcAvailable: z.boolean(),
  sharing2AcCount: z.string().optional(),
  sharing2AcMonthlyRate: z.string().optional(),
  sharing2AcDailyRate: z.string().optional(),
  sharing2NonAcAvailable: z.boolean(),
  sharing2NonAcCount: z.string().optional(),
  sharing2NonAcMonthlyRate: z.string().optional(),
  sharing2NonAcDailyRate: z.string().optional(),

  // 3-sharing (Triple occupancy)
  sharing3AcAvailable: z.boolean(),
  sharing3AcCount: z.string().optional(),
  sharing3AcMonthlyRate: z.string().optional(),
  sharing3AcDailyRate: z.string().optional(),
  sharing3NonAcAvailable: z.boolean(),
  sharing3NonAcCount: z.string().optional(),
  sharing3NonAcMonthlyRate: z.string().optional(),
  sharing3NonAcDailyRate: z.string().optional(),

  // 4-sharing (Quad occupancy)
  sharing4AcAvailable: z.boolean(),
  sharing4AcCount: z.string().optional(),
  sharing4AcMonthlyRate: z.string().optional(),
  sharing4AcDailyRate: z.string().optional(),
  sharing4NonAcAvailable: z.boolean(),
  sharing4NonAcCount: z.string().optional(),
  sharing4NonAcMonthlyRate: z.string().optional(),
  sharing4NonAcDailyRate: z.string().optional(),

  // 5-sharing (Five-bed occupancy)
  sharing5AcAvailable: z.boolean(),
  sharing5AcCount: z.string().optional(),
  sharing5AcMonthlyRate: z.string().optional(),
  sharing5AcDailyRate: z.string().optional(),
  sharing5NonAcAvailable: z.boolean(),
  sharing5NonAcCount: z.string().optional(),
  sharing5NonAcMonthlyRate: z.string().optional(),
  sharing5NonAcDailyRate: z.string().optional(),

  // 6-sharing (Six-bed occupancy)
  sharing6AcAvailable: z.boolean(),
  sharing6AcCount: z.string().optional(),
  sharing6AcMonthlyRate: z.string().optional(),
  sharing6AcDailyRate: z.string().optional(),
  sharing6NonAcAvailable: z.boolean(),
  sharing6NonAcCount: z.string().optional(),
  sharing6NonAcMonthlyRate: z.string().optional(),
  sharing6NonAcDailyRate: z.string().optional(),

  // Food Services
  foodServiceProvided: z.boolean(),
  withMealsDailyRate: z.string().optional(),
  withoutMealsDailyRate: z.string().optional(),

  // Documents & Banking
  ownerIdType: z.string().min(1, 'Please select ID type'),
  ownerIdNumber: z.string().min(1, 'ID number is required'),
  businessRegNumber: z.string().optional(),
  propertyDocType: z.string().min(1, 'Please select property document type'),
  bankAccountNumber: z.string().min(1, 'Bank account number is required'),
  bankIfscCode: z.string().min(1, 'IFSC code is required'),
  bankName: z.string().min(1, 'Bank name is required'),
  accountHolderName: z.string().min(1, 'Account holder name is required'),
});

export const OwnerRegistration: React.FC = () => {
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState<RegistrationStep>(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<Record<string, File[]>>({});
  const [selectedAmenities, setSelectedAmenities] = useState<string[]>([]);
  const [submissionResult, setSubmissionResult] = useState<{ requestId?: string; success: boolean } | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    trigger,
    getValues
  } = useForm<OwnerRegistrationFormData>({
    resolver: zodResolver(ownerRegistrationSchema),
    mode: 'onChange',
    defaultValues: {
      previousHostelExperience: false,
      foodServiceProvided: false,
      selectedCity: '',
      selectedArea: '',
      alternatePhone: '',
      gstNumber: '',
      businessRegNumber: '',
      withMealsDailyRate: '',
      withoutMealsDailyRate: '',

      // Room configuration defaults
      sharing1AcAvailable: false,
      sharing1AcCount: '',
      sharing1AcMonthlyRate: '',
      sharing1AcDailyRate: '',
      sharing1NonAcAvailable: false,
      sharing1NonAcCount: '',
      sharing1NonAcMonthlyRate: '',
      sharing1NonAcDailyRate: '',

      sharing2AcAvailable: false,
      sharing2AcCount: '',
      sharing2AcMonthlyRate: '',
      sharing2AcDailyRate: '',
      sharing2NonAcAvailable: false,
      sharing2NonAcCount: '',
      sharing2NonAcMonthlyRate: '',
      sharing2NonAcDailyRate: '',

      sharing3AcAvailable: false,
      sharing3AcCount: '',
      sharing3AcMonthlyRate: '',
      sharing3AcDailyRate: '',
      sharing3NonAcAvailable: false,
      sharing3NonAcCount: '',
      sharing3NonAcMonthlyRate: '',
      sharing3NonAcDailyRate: '',

      sharing4AcAvailable: false,
      sharing4AcCount: '',
      sharing4AcMonthlyRate: '',
      sharing4AcDailyRate: '',
      sharing4NonAcAvailable: false,
      sharing4NonAcCount: '',
      sharing4NonAcMonthlyRate: '',
      sharing4NonAcDailyRate: '',

      sharing5AcAvailable: false,
      sharing5AcCount: '',
      sharing5AcMonthlyRate: '',
      sharing5AcDailyRate: '',
      sharing5NonAcAvailable: false,
      sharing5NonAcCount: '',
      sharing5NonAcMonthlyRate: '',
      sharing5NonAcDailyRate: '',

      sharing6AcAvailable: false,
      sharing6AcCount: '',
      sharing6AcMonthlyRate: '',
      sharing6AcDailyRate: '',
      sharing6NonAcAvailable: false,
      sharing6NonAcCount: '',
      sharing6NonAcMonthlyRate: '',
      sharing6NonAcDailyRate: ''
    }
  });

  const watchedValues = watch();
  const steps = [
    { number: 1, title: 'Personal Information', icon: User },
    { number: 2, title: 'Business Details', icon: Briefcase },
    { number: 3, title: 'Hostel Information', icon: Building2 },
    { number: 4, title: 'Pricing & Services', icon: DollarSign },
    { number: 5, title: 'Documents & Banking', icon: FileText },
    { number: 6, title: 'Photos & Review', icon: Camera },
  ];

  const calculateProgress = (): number => {
    const totalFields = Object.keys(ownerRegistrationSchema.shape).length;
    const filledFields = Object.values(watchedValues).filter(value => 
      value !== undefined && value !== '' && value !== false
    ).length;
    return Math.round((filledFields / totalFields) * 100);
  };

  const validateRoomConfiguration = (): boolean => {
    const values = getValues();

    // Check if at least one room type is configured
    const hasAnyRoomType = [
      values.sharing1AcAvailable,
      values.sharing1NonAcAvailable,
      values.sharing2AcAvailable,
      values.sharing2NonAcAvailable,
      values.sharing3AcAvailable,
      values.sharing3NonAcAvailable,
      values.sharing4AcAvailable,
      values.sharing4NonAcAvailable,
      values.sharing5AcAvailable,
      values.sharing5NonAcAvailable,
      values.sharing6AcAvailable,
      values.sharing6NonAcAvailable
    ].some(available => available);

    if (!hasAnyRoomType) {
      toast({
        title: "Room Configuration Required",
        description: "Please configure at least one room type with pricing.",
        variant: "destructive"
      });
      return false;
    }

    // Validate that enabled room types have required fields
    const roomTypes = [
      { prefix: 'sharing1Ac', available: values.sharing1AcAvailable },
      { prefix: 'sharing1NonAc', available: values.sharing1NonAcAvailable },
      { prefix: 'sharing2Ac', available: values.sharing2AcAvailable },
      { prefix: 'sharing2NonAc', available: values.sharing2NonAcAvailable },
      { prefix: 'sharing3Ac', available: values.sharing3AcAvailable },
      { prefix: 'sharing3NonAc', available: values.sharing3NonAcAvailable },
      { prefix: 'sharing4Ac', available: values.sharing4AcAvailable },
      { prefix: 'sharing4NonAc', available: values.sharing4NonAcAvailable },
      { prefix: 'sharing5Ac', available: values.sharing5AcAvailable },
      { prefix: 'sharing5NonAc', available: values.sharing5NonAcAvailable },
      { prefix: 'sharing6Ac', available: values.sharing6AcAvailable },
      { prefix: 'sharing6NonAc', available: values.sharing6NonAcAvailable }
    ];

    for (const roomType of roomTypes) {
      if (roomType.available) {
        const countKey = `${roomType.prefix}Count` as keyof OwnerRegistrationFormData;
        const monthlyKey = `${roomType.prefix}MonthlyRate` as keyof OwnerRegistrationFormData;
        const dailyKey = `${roomType.prefix}DailyRate` as keyof OwnerRegistrationFormData;

        if (!values[countKey] || !values[monthlyKey] || !values[dailyKey]) {
          toast({
            title: "Incomplete Room Configuration",
            description: `Please fill all fields for enabled room types.`,
            variant: "destructive"
          });
          return false;
        }
      }
    }

    return true;
  };

  const validateCurrentStep = async (): Promise<boolean> => {
    const stepFields: Record<RegistrationStep, (keyof OwnerRegistrationFormData)[]> = {
      1: ['firstName', 'lastName', 'email', 'phone', 'dateOfBirth', 'gender'],
      2: ['businessName', 'businessType', 'panNumber', 'yearsOfExperience'],
      3: ['hostelName', 'hostelDescription', 'selectedCity', 'selectedArea', 'propertyType', 'totalFloors', 'totalRooms', 'totalBeds'],
      4: [], // Room configuration validation will be custom
      5: ['ownerIdType', 'ownerIdNumber', 'propertyDocType', 'bankAccountNumber', 'bankIfscCode', 'bankName', 'accountHolderName'],
      6: []
    };

    if (currentStep === 4) {
      return validateRoomConfiguration();
    }

    return await trigger(stepFields[currentStep]);
  };

  const handleNext = async () => {
    const isValid = await validateCurrentStep();
    if (isValid && currentStep < 6) {
      setCurrentStep((prev) => (prev + 1) as RegistrationStep);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep((prev) => (prev - 1) as RegistrationStep);
    }
  };

  const handleFileUpload = (category: string, files: FileList | null) => {
    if (files) {
      setUploadedFiles(prev => ({
        ...prev,
        [category]: Array.from(files)
      }));
    }
  };

  const handleAmenityToggle = (amenity: string) => {
    setSelectedAmenities(prev => 
      prev.includes(amenity) 
        ? prev.filter(a => a !== amenity)
        : [...prev, amenity]
    );
  };

  const onSubmit = async (data: OwnerRegistrationFormData) => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "Please log in to submit your registration.",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const result = await submitHostelRegistration(
        data,
        uploadedFiles,
        selectedAmenities,
        user.id
      );

      if (result.success) {
        setSubmissionResult({ requestId: result.requestId, success: true });
        toast({
          title: "Registration Submitted Successfully!",
          description: `Your hostel registration has been submitted for review. Request ID: ${result.requestId}. You'll receive an email confirmation shortly.`,
        });

        // Move to a success step or reset
        setCurrentStep(1);
      } else {
        toast({
          title: "Submission Failed",
          description: result.error || "There was an error submitting your registration. Please try again.",
          variant: "destructive"
        });
      }

    } catch (error) {
      console.error('Registration submission error:', error);
      toast({
        title: "Submission Failed",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const availableAmenities = [
    'WiFi', 'AC', 'Parking', 'Laundry', 'Kitchen', 'Security', 'CCTV', 
    'Power Backup', 'Water Purifier', 'Gym', 'Common Area', 'Study Room',
    'Balcony', 'Elevator', 'Housekeeping', 'Medical Facility'
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold tracking-tight">Hostel Owner Registration</h1>
          <p className="text-muted-foreground">
            Join our platform and start managing your hostel business
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="text-sm text-muted-foreground">
            Progress: {calculateProgress()}%
          </div>
          <Progress value={calculateProgress()} className="w-32" />
        </div>
      </div>

      {/* Step Indicator */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.number} className="flex items-center">
                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  currentStep >= step.number 
                    ? 'bg-primary border-primary text-primary-foreground' 
                    : 'border-muted-foreground text-muted-foreground'
                }`}>
                  {currentStep > step.number ? (
                    <CheckCircle className="h-5 w-5" />
                  ) : (
                    <step.icon className="h-5 w-5" />
                  )}
                </div>
                <div className="ml-3 hidden md:block">
                  <div className={`text-sm font-medium ${
                    currentStep >= step.number ? 'text-primary' : 'text-muted-foreground'
                  }`}>
                    Step {step.number}
                  </div>
                  <div className="text-xs text-muted-foreground">{step.title}</div>
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-12 h-0.5 mx-4 ${
                    currentStep > step.number ? 'bg-primary' : 'bg-muted'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <form onSubmit={handleSubmit(onSubmit)}>
        {/* Step 1: Personal Information */}
        {currentStep === 1 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="h-5 w-5" />
                <span>Personal Information</span>
              </CardTitle>
              <CardDescription>
                Please provide your personal details for account verification
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName">First Name *</Label>
                  <Input
                    id="firstName"
                    {...register('firstName')}
                    placeholder="Enter your first name"
                  />
                  {errors.firstName && (
                    <p className="text-sm text-destructive">{errors.firstName.message}</p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="lastName">Last Name *</Label>
                  <Input
                    id="lastName"
                    {...register('lastName')}
                    placeholder="Enter your last name"
                  />
                  {errors.lastName && (
                    <p className="text-sm text-destructive">{errors.lastName.message}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    type="email"
                    {...register('email')}
                    placeholder="Enter your email address"
                  />
                  {errors.email && (
                    <p className="text-sm text-destructive">{errors.email.message}</p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number *</Label>
                  <Input
                    id="phone"
                    {...register('phone')}
                    placeholder="Enter your phone number"
                  />
                  {errors.phone && (
                    <p className="text-sm text-destructive">{errors.phone.message}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="alternatePhone">Alternate Phone</Label>
                  <Input
                    id="alternatePhone"
                    {...register('alternatePhone')}
                    placeholder="Enter alternate phone number"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="dateOfBirth">Date of Birth *</Label>
                  <Input
                    id="dateOfBirth"
                    type="date"
                    {...register('dateOfBirth')}
                  />
                  {errors.dateOfBirth && (
                    <p className="text-sm text-destructive">{errors.dateOfBirth.message}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="gender">Gender *</Label>
                <Select onValueChange={(value) => setValue('gender', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select gender" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="male">Male</SelectItem>
                    <SelectItem value="female">Female</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
                {errors.gender && (
                  <p className="text-sm text-destructive">{errors.gender.message}</p>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 2: Business Information */}
        {currentStep === 2 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Briefcase className="h-5 w-5" />
                <span>Business Information</span>
              </CardTitle>
              <CardDescription>
                Provide details about your business for verification and compliance
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="businessName">Business Name *</Label>
                  <Input
                    id="businessName"
                    {...register('businessName')}
                    placeholder="Enter your business name"
                  />
                  {errors.businessName && (
                    <p className="text-sm text-destructive">{errors.businessName.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="businessType">Business Type *</Label>
                  <Select onValueChange={(value) => setValue('businessType', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select business type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="individual">Individual</SelectItem>
                      <SelectItem value="partnership">Partnership</SelectItem>
                      <SelectItem value="company">Private Limited Company</SelectItem>
                      <SelectItem value="llp">Limited Liability Partnership</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.businessType && (
                    <p className="text-sm text-destructive">{errors.businessType.message}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="panNumber">PAN Number *</Label>
                  <Input
                    id="panNumber"
                    {...register('panNumber')}
                    placeholder="Enter PAN number"
                    style={{ textTransform: 'uppercase' }}
                  />
                  {errors.panNumber && (
                    <p className="text-sm text-destructive">{errors.panNumber.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="gstNumber">GST Number (Optional)</Label>
                  <Input
                    id="gstNumber"
                    {...register('gstNumber')}
                    placeholder="Enter GST number"
                    style={{ textTransform: 'uppercase' }}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="yearsOfExperience">Years of Experience *</Label>
                  <Select onValueChange={(value) => setValue('yearsOfExperience', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select experience" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0-1">0-1 years</SelectItem>
                      <SelectItem value="2-5">2-5 years</SelectItem>
                      <SelectItem value="6-10">6-10 years</SelectItem>
                      <SelectItem value="10+">10+ years</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.yearsOfExperience && (
                    <p className="text-sm text-destructive">{errors.yearsOfExperience.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label>Previous Hostel Experience</Label>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="previousHostelExperience"
                      checked={watchedValues.previousHostelExperience}
                      onCheckedChange={(checked) => setValue('previousHostelExperience', !!checked)}
                    />
                    <Label htmlFor="previousHostelExperience" className="text-sm">
                      I have previous experience managing hostels
                    </Label>
                  </div>
                </div>
              </div>

              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  Business information will be verified during the review process. Please ensure all details are accurate.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        )}

        {/* Step 3: Hostel Information */}
        {currentStep === 3 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Building2 className="h-5 w-5" />
                <span>Hostel Information</span>
              </CardTitle>
              <CardDescription>
                Provide detailed information about your hostel property
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="hostelName">Hostel Name *</Label>
                  <Input
                    id="hostelName"
                    {...register('hostelName')}
                    placeholder="Enter hostel name"
                  />
                  {errors.hostelName && (
                    <p className="text-sm text-destructive">{errors.hostelName.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="propertyType">Property Type *</Label>
                  <Select onValueChange={(value) => setValue('propertyType', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select property type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="owned">Owned</SelectItem>
                      <SelectItem value="rented">Rented</SelectItem>
                      <SelectItem value="leased">Leased</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.propertyType && (
                    <p className="text-sm text-destructive">{errors.propertyType.message}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="hostelDescription">Hostel Description *</Label>
                <Textarea
                  id="hostelDescription"
                  {...register('hostelDescription')}
                  placeholder="Describe your hostel, its unique features, and what makes it special..."
                  rows={4}
                />
                {errors.hostelDescription && (
                  <p className="text-sm text-destructive">{errors.hostelDescription.message}</p>
                )}
              </div>

              {/* Location Selection - Simplified for now */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="selectedCity">City *</Label>
                  <Select onValueChange={(value) => setValue('selectedCity', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select city" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="mumbai">Mumbai</SelectItem>
                      <SelectItem value="delhi">Delhi</SelectItem>
                      <SelectItem value="bangalore">Bangalore</SelectItem>
                      <SelectItem value="pune">Pune</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.selectedCity && (
                    <p className="text-sm text-destructive">{errors.selectedCity.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="selectedArea">Area *</Label>
                  <Select onValueChange={(value) => setValue('selectedArea', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select area" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="andheri">Andheri</SelectItem>
                      <SelectItem value="bandra">Bandra</SelectItem>
                      <SelectItem value="powai">Powai</SelectItem>
                      <SelectItem value="thane">Thane</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.selectedArea && (
                    <p className="text-sm text-destructive">{errors.selectedArea.message}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="totalFloors">Total Floors *</Label>
                  <Input
                    id="totalFloors"
                    type="number"
                    {...register('totalFloors')}
                    placeholder="Number of floors"
                    min="1"
                  />
                  {errors.totalFloors && (
                    <p className="text-sm text-destructive">{errors.totalFloors.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="totalRooms">Total Rooms *</Label>
                  <Input
                    id="totalRooms"
                    type="number"
                    {...register('totalRooms')}
                    placeholder="Number of rooms"
                    min="1"
                  />
                  {errors.totalRooms && (
                    <p className="text-sm text-destructive">{errors.totalRooms.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="totalBeds">Total Beds *</Label>
                  <Input
                    id="totalBeds"
                    type="number"
                    {...register('totalBeds')}
                    placeholder="Number of beds"
                    min="1"
                  />
                  {errors.totalBeds && (
                    <p className="text-sm text-destructive">{errors.totalBeds.message}</p>
                  )}
                </div>
              </div>

              {/* Amenities Selection */}
              <div className="space-y-2">
                <Label>Amenities</Label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  {availableAmenities.map((amenity) => (
                    <div key={amenity} className="flex items-center space-x-2">
                      <Checkbox
                        id={amenity}
                        checked={selectedAmenities.includes(amenity)}
                        onCheckedChange={() => handleAmenityToggle(amenity)}
                      />
                      <Label htmlFor={amenity} className="text-sm">
                        {amenity}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 4: Room Configuration & Pricing */}
        {currentStep === 4 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <DollarSign className="h-5 w-5" />
                <span>Room Configuration & Pricing</span>
              </CardTitle>
              <CardDescription>
                Configure your room sharing types with AC/Non-AC pricing options
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  Configure at least one room type. Enable the room types you offer and set their pricing.
                </AlertDescription>
              </Alert>

              {/* Room Configuration */}
              <div className="space-y-6">
                {[1, 2, 3, 4, 5, 6].map((sharing) => (
                  <div key={sharing} className="border rounded-lg p-4">
                    <h3 className="text-lg font-medium mb-4 flex items-center space-x-2">
                      <Bed className="h-5 w-5" />
                      <span>{sharing}-Sharing ({sharing === 1 ? 'Single' : sharing === 2 ? 'Double' : sharing === 3 ? 'Triple' : sharing === 4 ? 'Quad' : sharing === 5 ? 'Five-bed' : 'Six-bed'} Occupancy)</span>
                    </h3>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      {/* AC Rooms */}
                      <div className="border rounded-lg p-4 bg-blue-50">
                        <div className="flex items-center space-x-2 mb-3">
                          <Checkbox
                            id={`sharing${sharing}AcAvailable`}
                            checked={watchedValues[`sharing${sharing}AcAvailable` as keyof OwnerRegistrationFormData] as boolean}
                            onCheckedChange={(checked) => setValue(`sharing${sharing}AcAvailable` as keyof OwnerRegistrationFormData, !!checked)}
                          />
                          <Label htmlFor={`sharing${sharing}AcAvailable`} className="font-medium">
                            AC Rooms Available
                          </Label>
                        </div>

                        {watchedValues[`sharing${sharing}AcAvailable` as keyof OwnerRegistrationFormData] && (
                          <div className="space-y-3">
                            <div className="space-y-2">
                              <Label htmlFor={`sharing${sharing}AcCount`}>Number of Rooms</Label>
                              <Input
                                id={`sharing${sharing}AcCount`}
                                type="number"
                                {...register(`sharing${sharing}AcCount` as keyof OwnerRegistrationFormData)}
                                placeholder="0"
                                min="0"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor={`sharing${sharing}AcMonthlyRate`}>Monthly Rate (₹)</Label>
                              <Input
                                id={`sharing${sharing}AcMonthlyRate`}
                                type="number"
                                {...register(`sharing${sharing}AcMonthlyRate` as keyof OwnerRegistrationFormData)}
                                placeholder="0"
                                min="0"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor={`sharing${sharing}AcDailyRate`}>Daily Rate (₹)</Label>
                              <Input
                                id={`sharing${sharing}AcDailyRate`}
                                type="number"
                                {...register(`sharing${sharing}AcDailyRate` as keyof OwnerRegistrationFormData)}
                                placeholder="0"
                                min="0"
                              />
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Non-AC Rooms */}
                      <div className="border rounded-lg p-4 bg-green-50">
                        <div className="flex items-center space-x-2 mb-3">
                          <Checkbox
                            id={`sharing${sharing}NonAcAvailable`}
                            checked={watchedValues[`sharing${sharing}NonAcAvailable` as keyof OwnerRegistrationFormData] as boolean}
                            onCheckedChange={(checked) => setValue(`sharing${sharing}NonAcAvailable` as keyof OwnerRegistrationFormData, !!checked)}
                          />
                          <Label htmlFor={`sharing${sharing}NonAcAvailable`} className="font-medium">
                            Non-AC Rooms Available
                          </Label>
                        </div>

                        {watchedValues[`sharing${sharing}NonAcAvailable` as keyof OwnerRegistrationFormData] && (
                          <div className="space-y-3">
                            <div className="space-y-2">
                              <Label htmlFor={`sharing${sharing}NonAcCount`}>Number of Rooms</Label>
                              <Input
                                id={`sharing${sharing}NonAcCount`}
                                type="number"
                                {...register(`sharing${sharing}NonAcCount` as keyof OwnerRegistrationFormData)}
                                placeholder="0"
                                min="0"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor={`sharing${sharing}NonAcMonthlyRate`}>Monthly Rate (₹)</Label>
                              <Input
                                id={`sharing${sharing}NonAcMonthlyRate`}
                                type="number"
                                {...register(`sharing${sharing}NonAcMonthlyRate` as keyof OwnerRegistrationFormData)}
                                placeholder="0"
                                min="0"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor={`sharing${sharing}NonAcDailyRate`}>Daily Rate (₹)</Label>
                              <Input
                                id={`sharing${sharing}NonAcDailyRate`}
                                type="number"
                                {...register(`sharing${sharing}NonAcDailyRate` as keyof OwnerRegistrationFormData)}
                                placeholder="0"
                                min="0"
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Food Services */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Food Services</h3>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="foodServiceProvided"
                    checked={watchedValues.foodServiceProvided}
                    onCheckedChange={(checked) => setValue('foodServiceProvided', !!checked)}
                  />
                  <Label htmlFor="foodServiceProvided">
                    We provide food services
                  </Label>
                </div>

                {watchedValues.foodServiceProvided && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="withMealsDailyRate">With Meals - Daily Rate (₹)</Label>
                      <Input
                        id="withMealsDailyRate"
                        type="number"
                        {...register('withMealsDailyRate')}
                        placeholder="0"
                        min="0"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="withoutMealsDailyRate">Without Meals - Daily Rate (₹)</Label>
                      <Input
                        id="withoutMealsDailyRate"
                        type="number"
                        {...register('withoutMealsDailyRate')}
                        placeholder="0"
                        min="0"
                      />
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 5: Documents & Banking */}
        {currentStep === 5 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="h-5 w-5" />
                <span>Documents & Banking</span>
              </CardTitle>
              <CardDescription>
                Upload required documents and provide banking details for payments
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Identity Documents */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Identity Verification</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="ownerIdType">ID Proof Type *</Label>
                    <Select onValueChange={(value) => setValue('ownerIdType', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select ID type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="aadhar">Aadhar Card</SelectItem>
                        <SelectItem value="pan">PAN Card</SelectItem>
                        <SelectItem value="passport">Passport</SelectItem>
                        <SelectItem value="driving_license">Driving License</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.ownerIdType && (
                      <p className="text-sm text-destructive">{errors.ownerIdType.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="ownerIdNumber">ID Number *</Label>
                    <Input
                      id="ownerIdNumber"
                      {...register('ownerIdNumber')}
                      placeholder="Enter ID number"
                    />
                    {errors.ownerIdNumber && (
                      <p className="text-sm text-destructive">{errors.ownerIdNumber.message}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Property Documents */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Property Documents</h3>

                <div className="space-y-2">
                  <Label htmlFor="propertyDocType">Property Document Type *</Label>
                  <Select onValueChange={(value) => setValue('propertyDocType', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select document type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ownership">Ownership Documents</SelectItem>
                      <SelectItem value="rental_agreement">Rental Agreement</SelectItem>
                      <SelectItem value="lease_deed">Lease Deed</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.propertyDocType && (
                    <p className="text-sm text-destructive">{errors.propertyDocType.message}</p>
                  )}
                </div>
              </div>

              {/* Banking Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Banking Information</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="bankAccountNumber">Account Number *</Label>
                    <Input
                      id="bankAccountNumber"
                      {...register('bankAccountNumber')}
                      placeholder="Enter account number"
                    />
                    {errors.bankAccountNumber && (
                      <p className="text-sm text-destructive">{errors.bankAccountNumber.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="bankIfscCode">IFSC Code *</Label>
                    <Input
                      id="bankIfscCode"
                      {...register('bankIfscCode')}
                      placeholder="Enter IFSC code"
                      style={{ textTransform: 'uppercase' }}
                    />
                    {errors.bankIfscCode && (
                      <p className="text-sm text-destructive">{errors.bankIfscCode.message}</p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="bankName">Bank Name *</Label>
                    <Input
                      id="bankName"
                      {...register('bankName')}
                      placeholder="Enter bank name"
                    />
                    {errors.bankName && (
                      <p className="text-sm text-destructive">{errors.bankName.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="accountHolderName">Account Holder Name *</Label>
                    <Input
                      id="accountHolderName"
                      {...register('accountHolderName')}
                      placeholder="Enter account holder name"
                    />
                    {errors.accountHolderName && (
                      <p className="text-sm text-destructive">{errors.accountHolderName.message}</p>
                    )}
                  </div>
                </div>
              </div>

              <Alert>
                <Shield className="h-4 w-4" />
                <AlertDescription>
                  All documents will be securely stored and used only for verification purposes.
                  Banking details are required for payment processing.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        )}

        {/* Step 6: Photos & Review */}
        {currentStep === 6 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Camera className="h-5 w-5" />
                <span>Photos & Final Review</span>
              </CardTitle>
              <CardDescription>
                Upload photos of your hostel and review your registration details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Photo Upload */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Hostel Photos</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Exterior Photos</Label>
                    <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
                      <Upload className="mx-auto h-12 w-12 text-muted-foreground" />
                      <div className="mt-2">
                        <Button variant="outline" size="sm">
                          Upload Photos
                        </Button>
                      </div>
                      <p className="text-xs text-muted-foreground mt-2">
                        Upload exterior photos of your hostel
                      </p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Room Photos</Label>
                    <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
                      <Upload className="mx-auto h-12 w-12 text-muted-foreground" />
                      <div className="mt-2">
                        <Button variant="outline" size="sm">
                          Upload Photos
                        </Button>
                      </div>
                      <p className="text-xs text-muted-foreground mt-2">
                        Upload photos of rooms and beds
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Registration Summary */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Registration Summary</h3>

                <div className="p-4 bg-muted rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Owner Name:</span> {watchedValues.firstName} {watchedValues.lastName}
                    </div>
                    <div>
                      <span className="font-medium">Email:</span> {watchedValues.email}
                    </div>
                    <div>
                      <span className="font-medium">Hostel Name:</span> {watchedValues.hostelName}
                    </div>
                    <div>
                      <span className="font-medium">Total Beds:</span> {watchedValues.totalBeds}
                    </div>
                    <div>
                      <span className="font-medium">Business Type:</span> {watchedValues.businessType}
                    </div>
                    <div>
                      <span className="font-medium">Completion:</span> {calculateProgress()}%
                    </div>
                  </div>
                </div>
              </div>

              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  Please review all information carefully before submitting.
                  Your registration will be reviewed within 2-3 business days.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        )}

        {/* Navigation Buttons */}
        <div className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStep === 1}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>

          {currentStep < 6 ? (
            <Button type="button" onClick={handleNext}>
              Next
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          ) : (
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Submitting...' : 'Submit Registration'}
              <Send className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>
      </form>
    </div>
  );
};
