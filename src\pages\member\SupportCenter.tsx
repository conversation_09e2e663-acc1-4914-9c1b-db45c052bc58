import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  HelpCircle, 
  MessageSquare,
  Phone,
  Mail,
  Search,
  Plus,
  MoreHorizontal,
  Eye,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  BookOpen,
  Video,
  FileText,
  Headphones,
  Send,
  Star,
  ThumbsUp,
  ThumbsDown,
  Filter,
  Calendar
} from 'lucide-react';

// Mock FAQ data
interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: 'booking' | 'payment' | 'account' | 'general';
  helpful: number;
  notHelpful: number;
}

const mockFAQs: FAQ[] = [
  {
    id: '1',
    question: 'How do I cancel my booking?',
    answer: 'You can cancel your booking by going to "My Bookings" section and clicking on the "Cancel" button. Please note that cancellation policies may apply depending on the hostel and timing.',
    category: 'booking',
    helpful: 45,
    notHelpful: 3
  },
  {
    id: '2',
    question: 'What payment methods are accepted?',
    answer: 'We accept all major credit/debit cards, UPI payments, net banking, and digital wallets like Paytm, PhonePe, and Google Pay.',
    category: 'payment',
    helpful: 38,
    notHelpful: 2
  },
  {
    id: '3',
    question: 'How do I change my profile information?',
    answer: 'Go to your profile page and click on "Edit Profile". You can update your personal information, contact details, and preferences.',
    category: 'account',
    helpful: 29,
    notHelpful: 1
  },
  {
    id: '4',
    question: 'What amenities are typically included?',
    answer: 'Most hostels include basic amenities like WiFi, bed linens, and common areas. Specific amenities vary by hostel and are listed on each property page.',
    category: 'general',
    helpful: 52,
    notHelpful: 4
  }
];

// Mock support ticket data
interface SupportTicket {
  id: string;
  subject: string;
  category: 'booking' | 'payment' | 'technical' | 'complaint' | 'other';
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high';
  createdDate: string;
  lastUpdate: string;
  description: string;
}

const mockTickets: SupportTicket[] = [
  {
    id: 'TKT-001',
    subject: 'Payment not processed',
    category: 'payment',
    status: 'in_progress',
    priority: 'high',
    createdDate: '2024-01-20',
    lastUpdate: '2024-01-21',
    description: 'My payment was deducted but booking is not confirmed'
  },
  {
    id: 'TKT-002',
    subject: 'Room change request',
    category: 'booking',
    status: 'resolved',
    priority: 'medium',
    createdDate: '2024-01-18',
    lastUpdate: '2024-01-19',
    description: 'Need to change room from shared to private'
  }
];

export const SupportCenter: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'faq' | 'tickets' | 'contact'>('faq');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'booking' | 'payment' | 'account' | 'general'>('all');
  const [isNewTicketOpen, setIsNewTicketOpen] = useState(false);
  const [newTicket, setNewTicket] = useState({
    subject: '',
    category: 'room',
    priority: 'medium',
    description: '',
    subcategory: ''
  });

  // Filter FAQs based on search and category
  const filteredFAQs = mockFAQs.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const getStatusBadge = (status: string) => {
    const variants = {
      open: 'bg-blue-100 text-blue-800',
      in_progress: 'bg-yellow-100 text-yellow-800',
      resolved: 'bg-green-100 text-green-800',
      closed: 'bg-gray-100 text-gray-800'
    };
    const icons = {
      open: Clock,
      in_progress: AlertTriangle,
      resolved: CheckCircle,
      closed: XCircle
    };
    const Icon = icons[status as keyof typeof icons] || Clock;
    return (
      <Badge className={`${variants[status as keyof typeof variants] || 'bg-gray-100 text-gray-800'} flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {status.replace('_', ' ').charAt(0).toUpperCase() + status.replace('_', ' ').slice(1)}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const variants = {
      low: 'bg-green-100 text-green-800',
      medium: 'bg-yellow-100 text-yellow-800',
      high: 'bg-red-100 text-red-800'
    };
    return (
      <Badge className={variants[priority as keyof typeof variants] || 'bg-gray-100 text-gray-800'}>
        {priority.charAt(0).toUpperCase() + priority.slice(1)}
      </Badge>
    );
  };

  const handleSubmitTicket = () => {
    // In a real app, this would make an API call to submit complaint
    // and notify both hostel owner and super admin simultaneously
    console.log('Submitting complaint:', newTicket);

    // Simulate notification to hostel owner and super admin
    console.log('Notifying hostel owner and super admin of new complaint');

    setIsNewTicketOpen(false);
    setNewTicket({
      subject: '',
      category: 'room',
      priority: 'medium',
      description: '',
      subcategory: ''
    });

    // Show success message
    alert('Complaint submitted successfully! Both hostel owner and admin have been notified.');
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Support & Help Center</h1>
          <p className="text-muted-foreground">
            Get help with your account, bookings, and find answers to common questions
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Phone className="mr-2 h-4 w-4" />
            Call Support
          </Button>
          <Dialog open={isNewTicketOpen} onOpenChange={setIsNewTicketOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                New Ticket
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create Support Ticket</DialogTitle>
                <DialogDescription>
                  Describe your issue and we'll help you resolve it
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="subject">Subject</Label>
                  <Input
                    id="subject"
                    value={newTicket.subject}
                    onChange={(e) => setNewTicket({...newTicket, subject: e.target.value})}
                    placeholder="Brief description of your issue"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="category">Category</Label>
                    <Select value={newTicket.category} onValueChange={(value) => setNewTicket({...newTicket, category: value, subcategory: ''})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="room">Room Issues</SelectItem>
                        <SelectItem value="food">Food Issues</SelectItem>
                        <SelectItem value="maintenance">Maintenance</SelectItem>
                        <SelectItem value="cleanliness">Cleanliness</SelectItem>
                        <SelectItem value="noise">Noise Complaints</SelectItem>
                        <SelectItem value="service">Service Issues</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="priority">Priority</Label>
                    <Select value={newTicket.priority} onValueChange={(value) => setNewTicket({...newTicket, priority: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Subcategory based on main category */}
                {newTicket.category && newTicket.category !== 'other' && (
                  <div className="space-y-2">
                    <Label htmlFor="subcategory">Specific Issue</Label>
                    <Select value={newTicket.subcategory} onValueChange={(value) => setNewTicket({...newTicket, subcategory: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select specific issue" />
                      </SelectTrigger>
                      <SelectContent>
                        {newTicket.category === 'room' && (
                          <>
                            <SelectItem value="ac_heating">AC/Heating Issues</SelectItem>
                            <SelectItem value="plumbing">Plumbing Problems</SelectItem>
                            <SelectItem value="electrical">Electrical Issues</SelectItem>
                            <SelectItem value="furniture">Furniture Problems</SelectItem>
                            <SelectItem value="security">Room Security</SelectItem>
                          </>
                        )}
                        {newTicket.category === 'food' && (
                          <>
                            <SelectItem value="quality">Food Quality</SelectItem>
                            <SelectItem value="hygiene">Food Hygiene</SelectItem>
                            <SelectItem value="timing">Meal Timing Issues</SelectItem>
                            <SelectItem value="variety">Limited Variety</SelectItem>
                            <SelectItem value="dietary">Dietary Requirements</SelectItem>
                          </>
                        )}
                        {newTicket.category === 'maintenance' && (
                          <>
                            <SelectItem value="common_area">Common Area Issues</SelectItem>
                            <SelectItem value="bathroom">Bathroom Problems</SelectItem>
                            <SelectItem value="kitchen">Kitchen Issues</SelectItem>
                            <SelectItem value="wifi">WiFi/Internet</SelectItem>
                            <SelectItem value="laundry">Laundry Facilities</SelectItem>
                          </>
                        )}
                        {newTicket.category === 'cleanliness' && (
                          <>
                            <SelectItem value="room_cleaning">Room Cleaning</SelectItem>
                            <SelectItem value="bathroom_cleaning">Bathroom Cleaning</SelectItem>
                            <SelectItem value="common_area_cleaning">Common Area Cleaning</SelectItem>
                            <SelectItem value="pest_control">Pest Control</SelectItem>
                          </>
                        )}
                        {newTicket.category === 'noise' && (
                          <>
                            <SelectItem value="roommate_noise">Roommate Noise</SelectItem>
                            <SelectItem value="construction_noise">Construction Noise</SelectItem>
                            <SelectItem value="common_area_noise">Common Area Noise</SelectItem>
                            <SelectItem value="external_noise">External Noise</SelectItem>
                          </>
                        )}
                        {newTicket.category === 'service' && (
                          <>
                            <SelectItem value="staff_behavior">Staff Behavior</SelectItem>
                            <SelectItem value="response_time">Response Time</SelectItem>
                            <SelectItem value="facility_access">Facility Access</SelectItem>
                            <SelectItem value="billing_issues">Billing Issues</SelectItem>
                          </>
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={newTicket.description}
                    onChange={(e) => setNewTicket({...newTicket, description: e.target.value})}
                    placeholder="Please provide detailed information about your issue..."
                    rows={5}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsNewTicketOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleSubmitTicket}>
                  <Send className="mr-2 h-4 w-4" />
                  Submit Ticket
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Quick Help Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-blue-50 rounded-lg">
                <BookOpen className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <div className="font-semibold">User Guide</div>
                <div className="text-sm text-muted-foreground">Step-by-step tutorials</div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-green-50 rounded-lg">
                <Video className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <div className="font-semibold">Video Tutorials</div>
                <div className="text-sm text-muted-foreground">Watch and learn</div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-purple-50 rounded-lg">
                <Headphones className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <div className="font-semibold">Live Chat</div>
                <div className="text-sm text-muted-foreground">Chat with support</div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-orange-50 rounded-lg">
                <Phone className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <div className="font-semibold">Call Support</div>
                <div className="text-sm text-muted-foreground">+91 1800-123-4567</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tab Navigation */}
      <div className="border-b">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('faq')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'faq'
                ? 'border-primary text-primary'
                : 'border-transparent text-muted-foreground hover:text-foreground hover:border-gray-300'
            }`}
          >
            <HelpCircle className="inline-block mr-2 h-4 w-4" />
            FAQ
          </button>
          <button
            onClick={() => setActiveTab('tickets')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'tickets'
                ? 'border-primary text-primary'
                : 'border-transparent text-muted-foreground hover:text-foreground hover:border-gray-300'
            }`}
          >
            <MessageSquare className="inline-block mr-2 h-4 w-4" />
            My Tickets
          </button>
          <button
            onClick={() => setActiveTab('contact')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'contact'
                ? 'border-primary text-primary'
                : 'border-transparent text-muted-foreground hover:text-foreground hover:border-gray-300'
            }`}
          >
            <Mail className="inline-block mr-2 h-4 w-4" />
            Contact Us
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'faq' && (
        <Card>
          <CardHeader>
            <CardTitle>Frequently Asked Questions</CardTitle>
            <CardDescription>
              Find quick answers to common questions
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Search and Filter */}
            <div className="flex items-center space-x-4 mb-6">
              <div className="relative flex-1">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search FAQs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
              <Select value={selectedCategory} onValueChange={(value: any) => setSelectedCategory(value)}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="booking">Booking</SelectItem>
                  <SelectItem value="payment">Payment</SelectItem>
                  <SelectItem value="account">Account</SelectItem>
                  <SelectItem value="general">General</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* FAQ Accordion */}
            <Accordion type="single" collapsible className="w-full">
              {filteredFAQs.map((faq) => (
                <AccordionItem key={faq.id} value={faq.id}>
                  <AccordionTrigger className="text-left">
                    <div className="flex items-center justify-between w-full mr-4">
                      <span>{faq.question}</span>
                      <Badge className="bg-gray-100 text-gray-800 capitalize">
                        {faq.category}
                      </Badge>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-4">
                      <p className="text-muted-foreground">{faq.answer}</p>
                      <div className="flex items-center justify-between">
                        <div className="text-sm text-muted-foreground">
                          Was this helpful?
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button variant="outline" size="sm">
                            <ThumbsUp className="mr-2 h-4 w-4" />
                            Yes ({faq.helpful})
                          </Button>
                          <Button variant="outline" size="sm">
                            <ThumbsDown className="mr-2 h-4 w-4" />
                            No ({faq.notHelpful})
                          </Button>
                        </div>
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>

            {filteredFAQs.length === 0 && (
              <div className="text-center py-8">
                <HelpCircle className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No FAQs found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Try adjusting your search criteria or contact support.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {activeTab === 'tickets' && (
        <Card>
          <CardHeader>
            <CardTitle>My Support Tickets</CardTitle>
            <CardDescription>
              Track your support requests and their status
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockTickets.map((ticket) => (
                <div key={ticket.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className="font-semibold">{ticket.id}</div>
                      {getStatusBadge(ticket.status)}
                      {getPriorityBadge(ticket.priority)}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Created: {new Date(ticket.createdDate).toLocaleDateString()}
                    </div>
                  </div>

                  <div className="mb-3">
                    <div className="font-medium text-lg">{ticket.subject}</div>
                    <div className="text-sm text-muted-foreground capitalize">
                      Category: {ticket.category}
                    </div>
                  </div>

                  <div className="text-muted-foreground mb-4">
                    {ticket.description}
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="text-sm text-muted-foreground">
                      Last updated: {new Date(ticket.lastUpdate).toLocaleDateString()}
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        <Eye className="mr-2 h-4 w-4" />
                        View Details
                      </Button>
                      <Button variant="outline" size="sm">
                        <MessageSquare className="mr-2 h-4 w-4" />
                        Add Reply
                      </Button>
                    </div>
                  </div>
                </div>
              ))}

              {mockTickets.length === 0 && (
                <div className="text-center py-8">
                  <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No support tickets</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    You haven't created any support tickets yet.
                  </p>
                  <Button className="mt-4" onClick={() => setIsNewTicketOpen(true)}>
                    <Plus className="mr-2 h-4 w-4" />
                    Create Your First Ticket
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {activeTab === 'contact' && (
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Contact Information</CardTitle>
              <CardDescription>
                Get in touch with our support team
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-muted-foreground" />
                <div>
                  <div className="font-medium">Phone Support</div>
                  <div className="text-sm text-muted-foreground">+91 1800-123-4567</div>
                  <div className="text-sm text-muted-foreground">Available 24/7</div>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-muted-foreground" />
                <div>
                  <div className="font-medium">Email Support</div>
                  <div className="text-sm text-muted-foreground"><EMAIL></div>
                  <div className="text-sm text-muted-foreground">Response within 24 hours</div>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <MessageSquare className="h-5 w-5 text-muted-foreground" />
                <div>
                  <div className="font-medium">Live Chat</div>
                  <div className="text-sm text-muted-foreground">Chat with our support team</div>
                  <div className="text-sm text-muted-foreground">Available 9 AM - 9 PM</div>
                </div>
              </div>

              <div className="pt-4">
                <Button className="w-full">
                  <Headphones className="mr-2 h-4 w-4" />
                  Start Live Chat
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Send us a Message</CardTitle>
              <CardDescription>
                Send a direct message to our support team
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="contactSubject">Subject</Label>
                  <Input id="contactSubject" placeholder="How can we help you?" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="contactCategory">Category</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="general">General Inquiry</SelectItem>
                      <SelectItem value="booking">Booking Support</SelectItem>
                      <SelectItem value="payment">Payment Issues</SelectItem>
                      <SelectItem value="technical">Technical Support</SelectItem>
                      <SelectItem value="feedback">Feedback</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="contactMessage">Message</Label>
                  <Textarea
                    id="contactMessage"
                    placeholder="Please describe your issue or question..."
                    rows={5}
                  />
                </div>

                <Button className="w-full">
                  <Send className="mr-2 h-4 w-4" />
                  Send Message
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};
