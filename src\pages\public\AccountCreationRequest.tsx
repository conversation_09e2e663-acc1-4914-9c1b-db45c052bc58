import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  Building2,
  User,
  Mail,
  Phone,
  Upload,
  FileText,
  CheckCircle,
  AlertTriangle,
  Loader2,
  ArrowRight,
  ArrowLeft
} from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from '@/hooks/use-toast';
import { submitAccountCreationRequest, AccountCreationSubmissionData } from '@/services/registrationService';

const accountRequestSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().min(10, 'Phone number must be at least 10 digits'),
  role: z.enum(['owner', 'employee']),
  businessType: z.string().optional(),
  experienceYears: z.number().min(0).max(50).optional(),
  referenceContact: z.string().optional(),
  idProofType: z.enum(['aadhar', 'pan', 'passport', 'driving_license']),
  idProofNumber: z.string().min(5, 'ID proof number is required'),
  businessProofType: z.enum(['gst', 'shop_act', 'trade_license']).optional(),
  businessProofNumber: z.string().optional(),
});

type AccountRequestFormData = z.infer<typeof accountRequestSchema>;

export const AccountCreationRequest: React.FC = () => {
  const [currentStep, setCurrentStep] = useState<1 | 2 | 3>(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<Record<string, File>>({});
  const [submissionResult, setSubmissionResult] = useState<{ requestId?: string; success: boolean } | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    trigger,
    getValues
  } = useForm<AccountRequestFormData>({
    resolver: zodResolver(accountRequestSchema),
    mode: 'onChange'
  });

  const watchedRole = watch('role');

  const handleFileUpload = (key: string, file: File) => {
    setUploadedFiles(prev => ({
      ...prev,
      [key]: file
    }));
  };

  const onSubmit = async (data: AccountRequestFormData) => {
    setIsSubmitting(true);
    
    try {
      const submissionData: AccountCreationSubmissionData = {
        name: data.name,
        email: data.email,
        phone: data.phone,
        role: data.role,
        businessType: data.businessType,
        experienceYears: data.experienceYears,
        referenceContact: data.referenceContact,
        documents: {
          idProof: {
            type: data.idProofType,
            number: data.idProofNumber,
            file: uploadedFiles.idProof
          },
          businessProof: data.businessProofType && data.businessProofNumber ? {
            type: data.businessProofType,
            number: data.businessProofNumber,
            file: uploadedFiles.businessProof
          } : undefined
        }
      };

      const result = await submitAccountCreationRequest(submissionData);
      
      if (result.success) {
        setSubmissionResult({ requestId: result.requestId, success: true });
        setCurrentStep(3);
        toast({
          title: "Account Request Submitted!",
          description: `Your account creation request has been submitted. Request ID: ${result.requestId}`,
        });
      } else {
        toast({
          title: "Submission Failed",
          description: result.error || "There was an error submitting your request. Please try again.",
          variant: "destructive"
        });
      }
      
    } catch (error) {
      console.error('Account request submission error:', error);
      toast({
        title: "Submission Failed",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const nextStep = async () => {
    const isValid = await trigger();
    if (isValid) {
      setCurrentStep(prev => Math.min(prev + 1, 3) as 1 | 2 | 3);
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1) as 1 | 2 | 3);
  };

  if (submissionResult?.success) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary/10 via-background to-accent/10 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <CardTitle className="text-2xl">Request Submitted Successfully!</CardTitle>
            <CardDescription>
              Your account creation request has been submitted for review.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <div className="p-4 bg-muted rounded-lg">
              <p className="text-sm text-muted-foreground">Request ID</p>
              <p className="font-mono font-semibold">{submissionResult.requestId}</p>
            </div>
            <p className="text-sm text-muted-foreground">
              You will receive an email notification once your request has been reviewed by our admin team.
              This typically takes 1-2 business days.
            </p>
            <Button 
              onClick={() => window.location.href = '/'}
              className="w-full"
            >
              Return to Home
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/10 via-background to-accent/10 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Building2 className="h-10 w-10 text-primary" />
            <span className="text-2xl font-bold bg-gradient-primary bg-clip-text text-transparent">
              HostelHub
            </span>
          </div>
          <h1 className="text-3xl font-bold">Request Account Access</h1>
          <p className="text-muted-foreground mt-2">
            Join our platform as a hostel owner or employee
          </p>
        </div>

        {/* Progress Steps */}
        <div className="flex items-center justify-center mb-8">
          <div className="flex items-center space-x-4">
            {[1, 2, 3].map((step) => (
              <div key={step} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step <= currentStep 
                    ? 'bg-primary text-primary-foreground' 
                    : 'bg-muted text-muted-foreground'
                }`}>
                  {step < currentStep ? <CheckCircle className="h-4 w-4" /> : step}
                </div>
                {step < 3 && (
                  <div className={`w-12 h-0.5 mx-2 ${
                    step < currentStep ? 'bg-primary' : 'bg-muted'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        <form onSubmit={handleSubmit(onSubmit)}>
          {/* Step 1: Personal Information */}
          {currentStep === 1 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Personal Information
                </CardTitle>
                <CardDescription>
                  Please provide your basic information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Full Name *</Label>
                    <Input
                      id="name"
                      {...register('name')}
                      placeholder="Enter your full name"
                    />
                    {errors.name && (
                      <p className="text-sm text-red-600 mt-1">{errors.name.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="email">Email Address *</Label>
                    <Input
                      id="email"
                      type="email"
                      {...register('email')}
                      placeholder="Enter your email"
                    />
                    {errors.email && (
                      <p className="text-sm text-red-600 mt-1">{errors.email.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="phone">Phone Number *</Label>
                    <Input
                      id="phone"
                      {...register('phone')}
                      placeholder="Enter your phone number"
                    />
                    {errors.phone && (
                      <p className="text-sm text-red-600 mt-1">{errors.phone.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="role">Role *</Label>
                    <Select onValueChange={(value) => setValue('role', value as 'owner' | 'employee')}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select your role" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="owner">Hostel Owner</SelectItem>
                        <SelectItem value="employee">Employee</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.role && (
                      <p className="text-sm text-red-600 mt-1">{errors.role.message}</p>
                    )}
                  </div>
                </div>

                {watchedRole === 'owner' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="businessType">Business Type</Label>
                      <Input
                        id="businessType"
                        {...register('businessType')}
                        placeholder="e.g., Hospitality, Real Estate"
                      />
                    </div>

                    <div>
                      <Label htmlFor="experienceYears">Years of Experience</Label>
                      <Input
                        id="experienceYears"
                        type="number"
                        {...register('experienceYears', { valueAsNumber: true })}
                        placeholder="Years in business"
                      />
                    </div>

                    <div className="md:col-span-2">
                      <Label htmlFor="referenceContact">Reference Contact (Optional)</Label>
                      <Input
                        id="referenceContact"
                        {...register('referenceContact')}
                        placeholder="Reference person's contact"
                      />
                    </div>
                  </div>
                )}

                <div className="flex justify-end">
                  <Button type="button" onClick={nextStep}>
                    Next Step
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 2: Document Upload */}
          {currentStep === 2 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Document Verification
                </CardTitle>
                <CardDescription>
                  Upload required documents for verification
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* ID Proof */}
                <div className="space-y-4">
                  <h3 className="font-semibold">Identity Proof *</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="idProofType">ID Proof Type</Label>
                      <Select onValueChange={(value) => setValue('idProofType', value as any)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select ID type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="aadhar">Aadhar Card</SelectItem>
                          <SelectItem value="pan">PAN Card</SelectItem>
                          <SelectItem value="passport">Passport</SelectItem>
                          <SelectItem value="driving_license">Driving License</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="idProofNumber">ID Number</Label>
                      <Input
                        id="idProofNumber"
                        {...register('idProofNumber')}
                        placeholder="Enter ID number"
                      />
                      {errors.idProofNumber && (
                        <p className="text-sm text-red-600 mt-1">{errors.idProofNumber.message}</p>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="idProofFile">Upload ID Proof</Label>
                    <Input
                      id="idProofFile"
                      type="file"
                      accept=".pdf,.jpg,.jpeg,.png"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) handleFileUpload('idProof', file);
                      }}
                    />
                    {uploadedFiles.idProof && (
                      <p className="text-sm text-green-600 mt-1">
                        ✓ {uploadedFiles.idProof.name}
                      </p>
                    )}
                  </div>
                </div>

                {/* Business Proof (for owners) */}
                {watchedRole === 'owner' && (
                  <div className="space-y-4">
                    <h3 className="font-semibold">Business Proof (Optional)</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="businessProofType">Business Proof Type</Label>
                        <Select onValueChange={(value) => setValue('businessProofType', value as any)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select business proof type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="gst">GST Certificate</SelectItem>
                            <SelectItem value="shop_act">Shop Act License</SelectItem>
                            <SelectItem value="trade_license">Trade License</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor="businessProofNumber">Business Registration Number</Label>
                        <Input
                          id="businessProofNumber"
                          {...register('businessProofNumber')}
                          placeholder="Enter registration number"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="businessProofFile">Upload Business Proof</Label>
                      <Input
                        id="businessProofFile"
                        type="file"
                        accept=".pdf,.jpg,.jpeg,.png"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) handleFileUpload('businessProof', file);
                        }}
                      />
                      {uploadedFiles.businessProof && (
                        <p className="text-sm text-green-600 mt-1">
                          ✓ {uploadedFiles.businessProof.name}
                        </p>
                      )}
                    </div>
                  </div>
                )}

                <div className="flex justify-between">
                  <Button type="button" variant="outline" onClick={prevStep}>
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Previous
                  </Button>
                  <Button 
                    type="submit" 
                    disabled={isSubmitting || !uploadedFiles.idProof}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Submitting...
                      </>
                    ) : (
                      'Submit Request'
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </form>
      </div>
    </div>
  );
};
