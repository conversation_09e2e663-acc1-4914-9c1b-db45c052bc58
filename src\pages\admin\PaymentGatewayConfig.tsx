import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { 
  Settings, 
  Shield,
  Zap,
  CreditCard,
  Building2,
  Wallet,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Edit,
  Save,
  RefreshCw,
  Eye,
  EyeOff,
  Activity,
  TrendingUp,
  Clock,
  DollarSign
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { PaymentGatewayConfig, PaymentMethod } from '@/types/payment';

// Mock gateway configurations
const mockGatewayConfigs: PaymentGatewayConfig[] = [
  {
    gateway: 'phonepe',
    isEnabled: true,
    priority: 1,
    credentials: {
      merchantId: 'PHONEPE_MERCHANT_123',
      apiKey: 'pk_live_phonepe_123456789',
      secretKey: 'sk_live_phonepe_987654321',
      webhookSecret: 'whsec_phonepe_abcdef123'
    },
    supportedMethods: ['upi', 'card', 'wallet'],
    fees: {
      percentage: 1.5,
      fixed: 2,
      maxFee: 500
    },
    limits: {
      minAmount: 100,
      maxAmount: 100000
    },
    settings: {
      autoCapture: true,
      timeout: 15,
      retryAttempts: 3
    }
  },
  {
    gateway: 'cashfree',
    isEnabled: true,
    priority: 2,
    credentials: {
      merchantId: 'CASHFREE_MERCHANT_456',
      apiKey: 'pk_live_cashfree_456789123',
      secretKey: 'sk_live_cashfree_321654987',
      webhookSecret: 'whsec_cashfree_xyz789def'
    },
    supportedMethods: ['upi', 'card', 'netbanking', 'wallet'],
    fees: {
      percentage: 1.8,
      fixed: 3,
      maxFee: 750
    },
    limits: {
      minAmount: 50,
      maxAmount: 200000
    },
    settings: {
      autoCapture: true,
      timeout: 20,
      retryAttempts: 2
    }
  },
  {
    gateway: 'razorpay',
    isEnabled: false,
    priority: 3,
    credentials: {
      merchantId: 'RAZORPAY_MERCHANT_789',
      apiKey: 'pk_test_razorpay_789123456',
      secretKey: 'sk_test_razorpay_654987321',
      webhookSecret: 'whsec_razorpay_def456ghi'
    },
    supportedMethods: ['upi', 'card', 'netbanking', 'wallet', 'emi'],
    fees: {
      percentage: 2.0,
      fixed: 5,
      maxFee: 1000
    },
    limits: {
      minAmount: 100,
      maxAmount: 500000
    },
    settings: {
      autoCapture: false,
      timeout: 30,
      retryAttempts: 5
    }
  }
];

export const PaymentGatewayConfig: React.FC = () => {
  const [gatewayConfigs, setGatewayConfigs] = useState<PaymentGatewayConfig[]>(mockGatewayConfigs);
  const [selectedGateway, setSelectedGateway] = useState<PaymentGatewayConfig | null>(null);
  const [showConfigDialog, setShowConfigDialog] = useState(false);
  const [showCredentials, setShowCredentials] = useState<Record<string, boolean>>({});
  const [isTestingConnection, setIsTestingConnection] = useState<string | null>(null);

  const getGatewayIcon = (gateway: string) => {
    const icons = {
      phonepe: '📱',
      cashfree: '💳',
      razorpay: '⚡'
    };
    return icons[gateway as keyof typeof icons] || '💳';
  };

  const getMethodIcon = (method: PaymentMethod) => {
    const icons = {
      card: CreditCard,
      upi: Zap,
      netbanking: Building2,
      wallet: Wallet,
      emi: DollarSign
    };
    return icons[method] || CreditCard;
  };

  const getStatusBadge = (isEnabled: boolean, isOnline: boolean = true) => {
    if (!isEnabled) {
      return <Badge className="bg-gray-100 text-gray-800">Disabled</Badge>;
    }
    if (isOnline) {
      return <Badge className="bg-green-100 text-green-800 flex items-center gap-1">
        <CheckCircle className="h-3 w-3" />
        Online
      </Badge>;
    }
    return <Badge className="bg-red-100 text-red-800 flex items-center gap-1">
      <XCircle className="h-3 w-3" />
      Offline
    </Badge>;
  };

  const handleToggleGateway = (gateway: string, enabled: boolean) => {
    setGatewayConfigs(prev => 
      prev.map(config => 
        config.gateway === gateway 
          ? { ...config, isEnabled: enabled }
          : config
      )
    );
    
    toast({
      title: enabled ? "Gateway Enabled" : "Gateway Disabled",
      description: `${gateway} has been ${enabled ? 'enabled' : 'disabled'}`,
    });
  };

  const handleTestConnection = async (gateway: string) => {
    setIsTestingConnection(gateway);
    
    try {
      // Simulate API test
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock success/failure
      const success = Math.random() > 0.2; // 80% success rate
      
      if (success) {
        toast({
          title: "Connection Successful",
          description: `${gateway} gateway is working properly`,
        });
      } else {
        throw new Error('Connection failed');
      }
    } catch (error) {
      toast({
        title: "Connection Failed",
        description: `Unable to connect to ${gateway} gateway`,
        variant: "destructive"
      });
    } finally {
      setIsTestingConnection(null);
    }
  };

  const handleEditGateway = (config: PaymentGatewayConfig) => {
    setSelectedGateway(config);
    setShowConfigDialog(true);
  };

  const handleSaveGateway = () => {
    if (!selectedGateway) return;
    
    setGatewayConfigs(prev => 
      prev.map(config => 
        config.gateway === selectedGateway.gateway 
          ? selectedGateway
          : config
      )
    );
    
    setShowConfigDialog(false);
    setSelectedGateway(null);
    
    toast({
      title: "Configuration Saved",
      description: `${selectedGateway.gateway} settings have been updated`,
    });
  };

  const toggleCredentialVisibility = (gateway: string) => {
    setShowCredentials(prev => ({
      ...prev,
      [gateway]: !prev[gateway]
    }));
  };

  const maskCredential = (credential: string, show: boolean) => {
    if (show) return credential;
    return credential.replace(/./g, '•');
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold tracking-tight">Payment Gateway Configuration</h1>
          <p className="text-muted-foreground">
            Manage payment gateway settings and credentials
          </p>
        </div>
        <div className="flex flex-col space-y-2 md:flex-row md:space-x-2 md:space-y-0">
          <Button variant="outline" className="w-full md:w-auto">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh Status
          </Button>
          <Button className="w-full md:w-auto">
            <Shield className="mr-2 h-4 w-4" />
            Security Settings
          </Button>
        </div>
      </div>

      {/* Gateway Overview Cards */}
      <div className="grid gap-4 grid-cols-1 md:grid-cols-3">
        {gatewayConfigs.map((config) => (
          <Card key={config.gateway} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium flex items-center space-x-2">
                <span className="text-2xl">{getGatewayIcon(config.gateway)}</span>
                <span className="capitalize">{config.gateway}</span>
              </CardTitle>
              <div className="flex items-center space-x-2">
                {getStatusBadge(config.isEnabled)}
                <Badge variant="outline">Priority {config.priority}</Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span>Fee Rate</span>
                  <span>{config.fees.percentage}% + ₹{config.fees.fixed}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Limits</span>
                  <span>₹{config.limits.minAmount} - ₹{config.limits.maxAmount.toLocaleString()}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Methods</span>
                  <span>{config.supportedMethods.length} supported</span>
                </div>
                <div className="flex space-x-2 pt-2">
                  <Button 
                    size="sm" 
                    variant="outline" 
                    className="flex-1"
                    onClick={() => handleEditGateway(config)}
                  >
                    <Edit className="mr-1 h-3 w-3" />
                    Edit
                  </Button>
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => handleTestConnection(config.gateway)}
                    disabled={!config.isEnabled || isTestingConnection === config.gateway}
                  >
                    {isTestingConnection === config.gateway ? (
                      <RefreshCw className="h-3 w-3 animate-spin" />
                    ) : (
                      <Activity className="h-3 w-3" />
                    )}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Detailed Configuration Table */}
      <Card>
        <CardHeader>
          <CardTitle>Gateway Details</CardTitle>
          <CardDescription>
            Detailed configuration and credentials for each payment gateway
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border overflow-hidden">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="min-w-[120px]">Gateway</TableHead>
                    <TableHead className="min-w-[100px]">Status</TableHead>
                    <TableHead className="min-w-[150px]">Merchant ID</TableHead>
                    <TableHead className="min-w-[120px]">API Key</TableHead>
                    <TableHead className="min-w-[150px]">Supported Methods</TableHead>
                    <TableHead className="min-w-[100px]">Fee Rate</TableHead>
                    <TableHead className="text-right min-w-[100px]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {gatewayConfigs.map((config) => (
                    <TableRow key={config.gateway}>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">{getGatewayIcon(config.gateway)}</span>
                          <span className="font-medium capitalize">{config.gateway}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={config.isEnabled}
                            onCheckedChange={(checked) => handleToggleGateway(config.gateway, checked)}
                          />
                          {getStatusBadge(config.isEnabled)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-mono text-sm">
                          {maskCredential(config.credentials.merchantId, showCredentials[config.gateway])}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <div className="font-mono text-sm">
                            {maskCredential(config.credentials.apiKey, showCredentials[config.gateway])}
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleCredentialVisibility(config.gateway)}
                          >
                            {showCredentials[config.gateway] ? (
                              <EyeOff className="h-3 w-3" />
                            ) : (
                              <Eye className="h-3 w-3" />
                            )}
                          </Button>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {config.supportedMethods.map((method) => {
                            const MethodIcon = getMethodIcon(method);
                            return (
                              <Badge key={method} variant="outline" className="text-xs">
                                <MethodIcon className="mr-1 h-3 w-3" />
                                {method.toUpperCase()}
                              </Badge>
                            );
                          })}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>{config.fees.percentage}%</div>
                          <div className="text-muted-foreground">+₹{config.fees.fixed}</div>
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditGateway(config)}
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleTestConnection(config.gateway)}
                            disabled={!config.isEnabled || isTestingConnection === config.gateway}
                          >
                            {isTestingConnection === config.gateway ? (
                              <RefreshCw className="h-3 w-3 animate-spin" />
                            ) : (
                              <Activity className="h-3 w-3" />
                            )}
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
