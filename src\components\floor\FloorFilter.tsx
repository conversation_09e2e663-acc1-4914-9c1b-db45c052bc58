import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { 
  Building,
  ChevronDown,
  ChevronUp,
  Filter,
  X,
  Layers
} from 'lucide-react';
import { Floor, FloorType } from '@/types/roomManagement';
import { getHostelFloors } from '@/services/floorManagementService';
import { toast } from '@/hooks/use-toast';

interface FloorFilterProps {
  hostelId: string;
  selectedFloors: string[];
  onFloorSelectionChange: (floorIds: string[]) => void;
  onFloorChange?: (floorId: string | null) => void;
  showAllOption?: boolean;
  className?: string;
}

interface FloorFilters {
  floorTypes: FloorType[];
  statuses: string[];
  hasElevatorAccess?: boolean;
  hasCommonArea?: boolean;
}

export const FloorFilter: React.FC<FloorFilterProps> = ({
  hostelId,
  selectedFloors,
  onFloorSelectionChange,
  onFloorChange,
  showAllOption = true,
  className = ''
}) => {
  const [floors, setFloors] = useState<Floor[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isExpanded, setIsExpanded] = useState(false);
  const [filters, setFilters] = useState<FloorFilters>({
    floorTypes: [],
    statuses: []
  });

  useEffect(() => {
    if (hostelId) {
      loadFloors();
    }
  }, [hostelId]);

  const loadFloors = async () => {
    setIsLoading(true);
    try {
      const result = await getHostelFloors(hostelId);
      if (result.success) {
        setFloors(result.data);
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to load floors",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error loading floors:', error);
      toast({
        title: "Error",
        description: "Failed to load floors",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const filteredFloors = floors.filter(floor => {
    // Apply filters
    if (filters.floorTypes.length > 0 && !filters.floorTypes.includes(floor.floorType)) {
      return false;
    }
    
    if (filters.statuses.length > 0 && !filters.statuses.includes(floor.status)) {
      return false;
    }
    
    if (filters.hasElevatorAccess !== undefined && floor.details.hasElevatorAccess !== filters.hasElevatorAccess) {
      return false;
    }
    
    if (filters.hasCommonArea !== undefined && floor.details.hasCommonArea !== filters.hasCommonArea) {
      return false;
    }
    
    return true;
  });

  const handleFloorToggle = (floorId: string) => {
    const newSelection = selectedFloors.includes(floorId)
      ? selectedFloors.filter(id => id !== floorId)
      : [...selectedFloors, floorId];
    
    onFloorSelectionChange(newSelection);
  };

  const handleSelectAll = () => {
    const allFloorIds = filteredFloors.map(floor => floor.id);
    onFloorSelectionChange(allFloorIds);
  };

  const handleClearAll = () => {
    onFloorSelectionChange([]);
  };

  const handleSingleFloorSelect = (floorId: string) => {
    if (onFloorChange) {
      onFloorChange(floorId === 'all' ? null : floorId);
    }
  };

  const updateFilter = (key: keyof FloorFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const getFloorTypeBadge = (type: FloorType) => {
    const colors = {
      residential: 'bg-blue-100 text-blue-800',
      commercial: 'bg-green-100 text-green-800',
      mixed: 'bg-purple-100 text-purple-800',
      common_area: 'bg-yellow-100 text-yellow-800',
      parking: 'bg-gray-100 text-gray-800',
      storage: 'bg-orange-100 text-orange-800'
    };
    
    return (
      <Badge className={colors[type] || 'bg-gray-100 text-gray-800'}>
        {type.replace('_', ' ')}
      </Badge>
    );
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case 'inactive':
        return <Badge variant="destructive">Inactive</Badge>;
      case 'under_construction':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700">Under Construction</Badge>;
      case 'maintenance':
        return <Badge variant="outline" className="bg-orange-50 text-orange-700">Maintenance</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center h-20">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            <span className="ml-2 text-sm text-muted-foreground">Loading floors...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Layers className="h-5 w-5" />
          Floor Filter
        </CardTitle>
        <CardDescription>
          Filter and select floors to view rooms
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Quick Floor Selector (if single selection) */}
        {onFloorChange && (
          <div className="space-y-2">
            <label className="text-sm font-medium">Select Floor</label>
            <Select onValueChange={handleSingleFloorSelect}>
              <SelectTrigger>
                <SelectValue placeholder="Choose a floor" />
              </SelectTrigger>
              <SelectContent>
                {showAllOption && (
                  <SelectItem value="all">
                    <div className="flex items-center space-x-2">
                      <Building className="h-4 w-4" />
                      <span>All Floors</span>
                    </div>
                  </SelectItem>
                )}
                {filteredFloors.map((floor) => (
                  <SelectItem key={floor.id} value={floor.id}>
                    <div className="flex items-center space-x-2">
                      <Layers className="h-4 w-4" />
                      <span>Floor {floor.floorNumber}</span>
                      <span className="text-muted-foreground">
                        ({floor.totalRooms} rooms, {floor.totalBeds} beds)
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Multi-select Floor List */}
        {!onFloorChange && (
          <>
            {/* Selection Controls */}
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">
                {selectedFloors.length} of {filteredFloors.length} floors selected
              </span>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" onClick={handleSelectAll}>
                  Select All
                </Button>
                <Button variant="outline" size="sm" onClick={handleClearAll}>
                  Clear All
                </Button>
              </div>
            </div>

            {/* Floor List */}
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {filteredFloors.map((floor) => (
                <div
                  key={floor.id}
                  className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50"
                >
                  <Checkbox
                    checked={selectedFloors.includes(floor.id)}
                    onCheckedChange={() => handleFloorToggle(floor.id)}
                  />
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">Floor {floor.floorNumber}</span>
                        {getFloorTypeBadge(floor.floorType)}
                        {getStatusBadge(floor.status)}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {floor.currentOccupancy}/{floor.totalBeds} occupied
                      </div>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {floor.totalRooms} rooms • {floor.totalBeds} beds
                      {floor.details.hasElevatorAccess && ' • Elevator'}
                      {floor.details.hasCommonArea && ' • Common Area'}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </>
        )}

        {/* Advanced Filters */}
        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <CollapsibleTrigger asChild>
            <Button variant="outline" className="w-full">
              <Filter className="mr-2 h-4 w-4" />
              Advanced Filters
              {isExpanded ? (
                <ChevronUp className="ml-2 h-4 w-4" />
              ) : (
                <ChevronDown className="ml-2 h-4 w-4" />
              )}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-4 mt-4">
            {/* Floor Type Filter */}
            <div>
              <label className="text-sm font-medium mb-2 block">Floor Types</label>
              <div className="flex flex-wrap gap-2">
                {(['residential', 'commercial', 'mixed', 'common_area', 'parking', 'storage'] as FloorType[]).map((type) => (
                  <Badge
                    key={type}
                    variant={filters.floorTypes.includes(type) ? "default" : "outline"}
                    className="cursor-pointer"
                    onClick={() => {
                      const newTypes = filters.floorTypes.includes(type)
                        ? filters.floorTypes.filter(t => t !== type)
                        : [...filters.floorTypes, type];
                      updateFilter('floorTypes', newTypes);
                    }}
                  >
                    {type.replace('_', ' ')}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Status Filter */}
            <div>
              <label className="text-sm font-medium mb-2 block">Status</label>
              <div className="flex flex-wrap gap-2">
                {['active', 'inactive', 'under_construction', 'maintenance'].map((status) => (
                  <Badge
                    key={status}
                    variant={filters.statuses.includes(status) ? "default" : "outline"}
                    className="cursor-pointer"
                    onClick={() => {
                      const newStatuses = filters.statuses.includes(status)
                        ? filters.statuses.filter(s => s !== status)
                        : [...filters.statuses, status];
                      updateFilter('statuses', newStatuses);
                    }}
                  >
                    {status.replace('_', ' ')}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Feature Filters */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={filters.hasElevatorAccess === true}
                  onCheckedChange={(checked) => 
                    updateFilter('hasElevatorAccess', checked ? true : undefined)
                  }
                />
                <label className="text-sm">Has Elevator Access</label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={filters.hasCommonArea === true}
                  onCheckedChange={(checked) => 
                    updateFilter('hasCommonArea', checked ? true : undefined)
                  }
                />
                <label className="text-sm">Has Common Area</label>
              </div>
            </div>

            {/* Clear Filters */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setFilters({ floorTypes: [], statuses: [] })}
              className="w-full"
            >
              <X className="mr-2 h-4 w-4" />
              Clear Filters
            </Button>
          </CollapsibleContent>
        </Collapsible>

        {/* No Floors Message */}
        {filteredFloors.length === 0 && (
          <div className="text-center py-4">
            <Layers className="mx-auto h-8 w-8 text-gray-400" />
            <p className="mt-2 text-sm text-gray-500">
              No floors match the current filters
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
