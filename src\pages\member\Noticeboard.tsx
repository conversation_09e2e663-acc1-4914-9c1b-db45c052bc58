import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Bell, 
  Search, 
  Filter,
  Calendar,
  AlertTriangle,
  Info,
  Megaphone,
  Settings,
  Clock,
  Eye,
  EyeOff,
  Pin,
  Archive
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

// Notice interface
interface Notice {
  id: string;
  title: string;
  content: string;
  type: 'general' | 'urgent' | 'maintenance' | 'events' | 'food' | 'rules';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  postedBy: string;
  postedDate: string;
  expiryDate?: string;
  isRead: boolean;
  isPinned: boolean;
  attachments?: string[];
  targetAudience: 'all' | 'specific_rooms' | 'specific_floors';
  readBy: string[]; // Array of user IDs who have read this notice
}

// Mock notices data
const mockNotices: Notice[] = [
  {
    id: '1',
    title: 'Water Supply Maintenance - Tomorrow 10 AM to 2 PM',
    content: 'Dear residents, we will be conducting water supply maintenance tomorrow from 10 AM to 2 PM. Please store water in advance. We apologize for any inconvenience caused.',
    type: 'maintenance',
    priority: 'urgent',
    postedBy: 'Hostel Management',
    postedDate: '2024-01-22T09:00:00Z',
    expiryDate: '2024-01-24T18:00:00Z',
    isRead: false,
    isPinned: true,
    targetAudience: 'all',
    readBy: []
  },
  {
    id: '2',
    title: 'New Food Menu Starting Monday',
    content: 'We are excited to introduce our new food menu starting this Monday. The menu includes more variety and healthier options. Check the dining hall for the complete menu.',
    type: 'food',
    priority: 'medium',
    postedBy: 'Kitchen Staff',
    postedDate: '2024-01-21T14:30:00Z',
    isRead: true,
    isPinned: false,
    targetAudience: 'all',
    readBy: ['current-user-id']
  },
  {
    id: '3',
    title: 'Republic Day Celebration - January 26th',
    content: 'Join us for Republic Day celebration on January 26th at 8 AM in the common area. Flag hoisting ceremony followed by cultural programs and breakfast.',
    type: 'events',
    priority: 'medium',
    postedBy: 'Event Committee',
    postedDate: '2024-01-20T16:00:00Z',
    expiryDate: '2024-01-26T23:59:59Z',
    isRead: false,
    isPinned: true,
    targetAudience: 'all',
    readBy: []
  },
  {
    id: '4',
    title: 'WiFi Password Updated',
    content: 'The WiFi password has been updated for security reasons. New password: HostelWiFi2024. Please update your devices.',
    type: 'general',
    priority: 'high',
    postedBy: 'IT Support',
    postedDate: '2024-01-19T11:15:00Z',
    isRead: true,
    isPinned: false,
    targetAudience: 'all',
    readBy: ['current-user-id']
  },
  {
    id: '5',
    title: 'Quiet Hours Reminder',
    content: 'Please maintain quiet hours from 10 PM to 7 AM. Loud music, conversations, and phone calls should be avoided during these hours to ensure everyone gets proper rest.',
    type: 'rules',
    priority: 'low',
    postedBy: 'Hostel Management',
    postedDate: '2024-01-18T20:00:00Z',
    isRead: false,
    isPinned: false,
    targetAudience: 'all',
    readBy: []
  }
];

export const Noticeboard: React.FC = () => {
  const { user } = useAuth();
  const [notices, setNotices] = useState<Notice[]>(mockNotices);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<'all' | Notice['type']>('all');
  const [priorityFilter, setPriorityFilter] = useState<'all' | Notice['priority']>('all');
  const [showReadFilter, setShowReadFilter] = useState<'all' | 'unread' | 'read'>('all');

  // Filter notices
  const filteredNotices = notices.filter(notice => {
    const matchesSearch = notice.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         notice.content.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === 'all' || notice.type === typeFilter;
    const matchesPriority = priorityFilter === 'all' || notice.priority === priorityFilter;
    const matchesReadStatus = showReadFilter === 'all' || 
                             (showReadFilter === 'read' && notice.isRead) ||
                             (showReadFilter === 'unread' && !notice.isRead);
    
    return matchesSearch && matchesType && matchesPriority && matchesReadStatus;
  });

  // Sort notices: pinned first, then by date (newest first)
  const sortedNotices = filteredNotices.sort((a, b) => {
    if (a.isPinned && !b.isPinned) return -1;
    if (!a.isPinned && b.isPinned) return 1;
    return new Date(b.postedDate).getTime() - new Date(a.postedDate).getTime();
  });

  const getTypeIcon = (type: Notice['type']) => {
    switch (type) {
      case 'urgent': return <AlertTriangle className="h-4 w-4" />;
      case 'maintenance': return <Settings className="h-4 w-4" />;
      case 'events': return <Calendar className="h-4 w-4" />;
      case 'food': return <Megaphone className="h-4 w-4" />;
      case 'rules': return <Info className="h-4 w-4" />;
      default: return <Bell className="h-4 w-4" />;
    }
  };

  const getTypeColor = (type: Notice['type']) => {
    switch (type) {
      case 'urgent': return 'bg-red-100 text-red-800';
      case 'maintenance': return 'bg-orange-100 text-orange-800';
      case 'events': return 'bg-purple-100 text-purple-800';
      case 'food': return 'bg-green-100 text-green-800';
      case 'rules': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: Notice['priority']) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const markAsRead = (noticeId: string) => {
    setNotices(prev => 
      prev.map(notice => 
        notice.id === noticeId 
          ? { ...notice, isRead: true, readBy: [...notice.readBy, user?.id || 'current-user'] }
          : notice
      )
    );
  };

  const markAllAsRead = () => {
    setNotices(prev => 
      prev.map(notice => ({ 
        ...notice, 
        isRead: true, 
        readBy: [...notice.readBy, user?.id || 'current-user'] 
      }))
    );
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else if (diffInHours < 48) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString();
    }
  };

  const unreadCount = notices.filter(notice => !notice.isRead).length;

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Noticeboard</h1>
          <p className="text-muted-foreground">
            Stay updated with important announcements and notices
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="secondary" className="bg-blue-100 text-blue-800">
            {unreadCount} unread
          </Badge>
          <Button variant="outline" onClick={markAllAsRead} disabled={unreadCount === 0}>
            <Eye className="mr-2 h-4 w-4" />
            Mark All Read
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search notices..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Select value={typeFilter} onValueChange={(value: any) => setTypeFilter(value)}>
              <SelectTrigger className="w-full md:w-[180px]">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="general">General</SelectItem>
                <SelectItem value="urgent">Urgent</SelectItem>
                <SelectItem value="maintenance">Maintenance</SelectItem>
                <SelectItem value="events">Events</SelectItem>
                <SelectItem value="food">Food</SelectItem>
                <SelectItem value="rules">Rules</SelectItem>
              </SelectContent>
            </Select>
            <Select value={priorityFilter} onValueChange={(value: any) => setPriorityFilter(value)}>
              <SelectTrigger className="w-full md:w-[180px]">
                <SelectValue placeholder="Filter by priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priorities</SelectItem>
                <SelectItem value="urgent">Urgent</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="low">Low</SelectItem>
              </SelectContent>
            </Select>
            <Select value={showReadFilter} onValueChange={(value: any) => setShowReadFilter(value)}>
              <SelectTrigger className="w-full md:w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Notices</SelectItem>
                <SelectItem value="unread">Unread Only</SelectItem>
                <SelectItem value="read">Read Only</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Notices List */}
      <div className="space-y-4">
        {sortedNotices.length === 0 ? (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <Bell className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No notices found</h3>
                <p className="text-muted-foreground">
                  {searchTerm || typeFilter !== 'all' || priorityFilter !== 'all' 
                    ? 'Try adjusting your filters to see more notices.'
                    : 'There are no notices available at the moment.'
                  }
                </p>
              </div>
            </CardContent>
          </Card>
        ) : (
          sortedNotices.map((notice) => (
            <Card 
              key={notice.id} 
              className={`transition-all hover:shadow-md ${
                !notice.isRead ? 'border-l-4 border-l-blue-500 bg-blue-50/30' : ''
              }`}
            >
              <CardContent className="pt-6">
                <div className="flex items-start space-x-4">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      {notice.isPinned && (
                        <Pin className="h-4 w-4 text-orange-500" />
                      )}
                      <Badge className={getTypeColor(notice.type)}>
                        {getTypeIcon(notice.type)}
                        <span className="ml-1 capitalize">{notice.type}</span>
                      </Badge>
                      <Badge className={getPriorityColor(notice.priority)}>
                        {notice.priority.charAt(0).toUpperCase() + notice.priority.slice(1)}
                      </Badge>
                      {!notice.isRead && (
                        <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                          New
                        </Badge>
                      )}
                    </div>
                    
                    <h3 className="text-lg font-semibold mb-2">{notice.title}</h3>
                    <p className="text-muted-foreground mb-3">{notice.content}</p>
                    
                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <div className="flex items-center space-x-4">
                        <span>By {notice.postedBy}</span>
                        <div className="flex items-center space-x-1">
                          <Clock className="h-3 w-3" />
                          <span>{formatDate(notice.postedDate)}</span>
                        </div>
                        {notice.expiryDate && (
                          <span className="text-orange-600">
                            Expires: {new Date(notice.expiryDate).toLocaleDateString()}
                          </span>
                        )}
                      </div>
                      
                      {!notice.isRead && (
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => markAsRead(notice.id)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          Mark as Read
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};
