import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { 
  Users,
  Search,
  Filter,
  MoreHorizontal,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Star,
  Phone,
  Mail,
  MapPin,
  Calendar,
  Briefcase,
  Shield,
  TrendingUp,
  UserCheck,
  UserX,
  RefreshCw,
  Download,
  Settings
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/hooks/use-toast';
import { mockUsers, mockBookings } from '@/data/mockData';
import { MemberApplication, ApplicationStatus, ApplicationPriority, BulkAction } from '@/types/owner';

// Mock member applications data
const mockApplications: MemberApplication[] = [
  {
    id: 'APP_001',
    userId: '11',
    hostelId: '1',
    applicationDate: '2024-02-10',
    status: 'pending',
    priority: 'high',
    memberDetails: {
      name: 'Rohit Verma',
      email: '<EMAIL>',
      phone: '+91 9876543210',
      age: 24,
      gender: 'male',
      occupation: 'Software Engineer',
      company: 'Tech Corp',
      emergencyContact: {
        name: 'Priya Verma',
        phone: '+91 9876543211',
        relationship: 'Sister'
      }
    },
    preferences: {
      sharingType: 'sharing2',
      roomType: 'ac',
      foodPackage: 'with_meals',
      duration: 180,
      checkInDate: '2024-02-15',
      specialRequests: 'Prefer ground floor room'
    },
    verification: {
      idProof: true,
      backgroundCheck: true,
      employmentVerification: true,
      previousHostelReference: false
    },
    score: {
      total: 85,
      breakdown: {
        profileCompleteness: 95,
        verification: 80,
        references: 70,
        paymentHistory: 95
      }
    }
  },
  {
    id: 'APP_002',
    userId: '12',
    hostelId: '1',
    applicationDate: '2024-02-09',
    status: 'approved',
    priority: 'medium',
    memberDetails: {
      name: 'Priya Sharma',
      email: '<EMAIL>',
      phone: '+91 9876543212',
      age: 22,
      gender: 'female',
      occupation: 'Marketing Executive',
      company: 'Brand Solutions',
      emergencyContact: {
        name: 'Raj Sharma',
        phone: '+91 9876543213',
        relationship: 'Father'
      }
    },
    preferences: {
      sharingType: 'sharing1',
      roomType: 'nonAc',
      foodPackage: 'without_meals',
      duration: 90,
      checkInDate: '2024-02-12',
    },
    verification: {
      idProof: true,
      backgroundCheck: true,
      employmentVerification: true,
      previousHostelReference: true
    },
    score: {
      total: 92,
      breakdown: {
        profileCompleteness: 90,
        verification: 95,
        references: 90,
        paymentHistory: 93
      }
    },
    reviewedBy: 'owner_1',
    reviewedDate: '2024-02-10'
  }
];

export const MemberApplicationManagement: React.FC = () => {
  const { user } = useAuth();
  const [applications, setApplications] = useState<MemberApplication[]>(mockApplications);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [selectedApplications, setSelectedApplications] = useState<string[]>([]);
  const [selectedApplication, setSelectedApplication] = useState<MemberApplication | null>(null);
  const [showApplicationDialog, setShowApplicationDialog] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  // Filter applications
  const filteredApplications = applications.filter(app => {
    const matchesSearch = app.memberDetails.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         app.memberDetails.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         app.id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || app.status === statusFilter;
    const matchePriority = priorityFilter === 'all' || app.priority === priorityFilter;
    
    return matchesSearch && matchesStatus && matchePriority;
  });

  // Calculate statistics
  const stats = {
    total: applications.length,
    pending: applications.filter(a => a.status === 'pending').length,
    approved: applications.filter(a => a.status === 'approved').length,
    rejected: applications.filter(a => a.status === 'rejected').length,
    averageScore: applications.reduce((sum, a) => sum + a.score.total, 0) / applications.length,
    averageProcessingTime: 2.5 // days
  };

  const getStatusBadge = (status: ApplicationStatus) => {
    const variants = {
      pending: 'bg-yellow-100 text-yellow-800',
      approved: 'bg-green-100 text-green-800',
      rejected: 'bg-red-100 text-red-800',
      waitlisted: 'bg-blue-100 text-blue-800'
    };
    const icons = {
      pending: Clock,
      approved: CheckCircle,
      rejected: XCircle,
      waitlisted: AlertTriangle
    };
    const Icon = icons[status];
    return (
      <Badge className={`${variants[status]} flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: ApplicationPriority) => {
    const variants = {
      high: 'bg-red-100 text-red-800',
      medium: 'bg-yellow-100 text-yellow-800',
      low: 'bg-green-100 text-green-800'
    };
    return (
      <Badge className={variants[priority]}>
        {priority.charAt(0).toUpperCase() + priority.slice(1)}
      </Badge>
    );
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const handleApplicationAction = async (applicationId: string, action: 'approve' | 'reject' | 'waitlist') => {
    setIsProcessing(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setApplications(prev => 
        prev.map(app => 
          app.id === applicationId 
            ? { 
                ...app, 
                status: action === 'approve' ? 'approved' : action === 'reject' ? 'rejected' : 'waitlisted',
                reviewedBy: user?.id || 'current_user',
                reviewedDate: new Date().toISOString().split('T')[0]
              }
            : app
        )
      );
      
      toast({
        title: `Application ${action}d`,
        description: `The application has been ${action}d successfully.`,
      });
      
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to ${action} application. Please try again.`,
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleBulkAction = async (action: BulkAction) => {
    if (selectedApplications.length === 0) {
      toast({
        title: "No applications selected",
        description: "Please select applications to perform bulk action.",
        variant: "destructive"
      });
      return;
    }

    setIsProcessing(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setApplications(prev => 
        prev.map(app => 
          selectedApplications.includes(app.id)
            ? { 
                ...app, 
                status: action.type === 'approve' ? 'approved' : action.type === 'reject' ? 'rejected' : app.status,
                priority: action.newPriority || app.priority,
                reviewedBy: user?.id || 'current_user',
                reviewedDate: new Date().toISOString().split('T')[0],
                reviewNotes: action.notes
              }
            : app
        )
      );
      
      setSelectedApplications([]);
      
      toast({
        title: "Bulk action completed",
        description: `${selectedApplications.length} applications have been updated.`,
      });
      
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to perform bulk action. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleViewApplication = (application: MemberApplication) => {
    setSelectedApplication(application);
    setShowApplicationDialog(true);
  };

  const handleSelectApplication = (applicationId: string) => {
    setSelectedApplications(prev => 
      prev.includes(applicationId)
        ? prev.filter(id => id !== applicationId)
        : [...prev, applicationId]
    );
  };

  const handleSelectAll = () => {
    if (selectedApplications.length === filteredApplications.length) {
      setSelectedApplications([]);
    } else {
      setSelectedApplications(filteredApplications.map(app => app.id));
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold tracking-tight">Member Applications</h1>
          <p className="text-muted-foreground">
            Review and manage member applications for your hostel
          </p>
        </div>
        <div className="flex flex-col space-y-2 md:flex-row md:space-x-2 md:space-y-0">
          <Button variant="outline" className="w-full md:w-auto">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button variant="outline" className="w-full md:w-auto">
            <Download className="mr-2 h-4 w-4" />
            Export Data
          </Button>
          <Button className="w-full md:w-auto">
            <Settings className="mr-2 h-4 w-4" />
            Application Settings
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Applications</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              +12% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pending}</div>
            <p className="text-xs text-muted-foreground">
              Requires attention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Score</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.averageScore.toFixed(1)}</div>
            <p className="text-xs text-muted-foreground">
              Quality indicator
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Processing Time</CardTitle>
            <Clock className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.averageProcessingTime}d</div>
            <p className="text-xs text-muted-foreground">
              Average review time
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Applications Management */}
      <Card>
        <CardHeader>
          <CardTitle>Member Applications</CardTitle>
          <CardDescription>
            Review and manage member applications with advanced filtering and bulk actions
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Filters and Search */}
          <div className="flex flex-col space-y-4 md:flex-row md:items-center md:space-x-4 md:space-y-0 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by name, email, or application ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <div className="flex flex-col space-y-2 md:flex-row md:space-x-2 md:space-y-0">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full md:w-40">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                  <SelectItem value="waitlisted">Waitlisted</SelectItem>
                </SelectContent>
              </Select>

              <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                <SelectTrigger className="w-full md:w-40">
                  <SelectValue placeholder="Priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priority</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Bulk Actions */}
          {selectedApplications.length > 0 && (
            <div className="flex items-center space-x-2 mb-4 p-3 bg-muted rounded-lg">
              <span className="text-sm font-medium">
                {selectedApplications.length} application(s) selected
              </span>
              <Button
                size="sm"
                onClick={() => handleBulkAction({ type: 'approve', applicationIds: selectedApplications })}
                disabled={isProcessing}
              >
                <UserCheck className="mr-1 h-3 w-3" />
                Approve
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkAction({ type: 'reject', applicationIds: selectedApplications })}
                disabled={isProcessing}
              >
                <UserX className="mr-1 h-3 w-3" />
                Reject
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkAction({ type: 'waitlist', applicationIds: selectedApplications })}
                disabled={isProcessing}
              >
                <Clock className="mr-1 h-3 w-3" />
                Waitlist
              </Button>
            </div>
          )}

          {/* Applications Table */}
          {filteredApplications.length > 0 && (
            <div className="rounded-md border overflow-hidden">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox
                          checked={selectedApplications.length === filteredApplications.length && filteredApplications.length > 0}
                          onCheckedChange={handleSelectAll}
                        />
                      </TableHead>
                      <TableHead className="min-w-[200px]">Applicant</TableHead>
                      <TableHead className="min-w-[120px]">Application ID</TableHead>
                      <TableHead className="min-w-[100px]">Score</TableHead>
                      <TableHead className="min-w-[100px]">Priority</TableHead>
                      <TableHead className="min-w-[100px]">Status</TableHead>
                      <TableHead className="min-w-[120px]">Applied Date</TableHead>
                      <TableHead className="min-w-[150px]">Preferences</TableHead>
                      <TableHead className="text-right min-w-[100px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredApplications.map((application) => (
                      <TableRow key={application.id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedApplications.includes(application.id)}
                            onCheckedChange={() => handleSelectApplication(application.id)}
                          />
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            <Avatar>
                              <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${application.memberDetails.name}`} />
                              <AvatarFallback>
                                {application.memberDetails.name.split(' ').map(n => n[0]).join('')}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">{application.memberDetails.name}</div>
                              <div className="text-sm text-muted-foreground">{application.memberDetails.email}</div>
                              <div className="text-xs text-muted-foreground">
                                {application.memberDetails.occupation} • Age {application.memberDetails.age}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="font-mono text-sm">
                          {application.id}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <span className={`font-bold ${getScoreColor(application.score.total)}`}>
                              {application.score.total}
                            </span>
                            <div className="flex">
                              {[...Array(5)].map((_, i) => (
                                <Star
                                  key={i}
                                  className={`h-3 w-3 ${
                                    i < Math.floor(application.score.total / 20)
                                      ? 'fill-yellow-400 text-yellow-400'
                                      : 'text-gray-300'
                                  }`}
                                />
                              ))}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {getPriorityBadge(application.priority)}
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(application.status)}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-3 w-3 text-muted-foreground" />
                            <span className="text-sm">
                              {new Date(application.applicationDate).toLocaleDateString()}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div>{application.preferences.sharingType.replace('sharing', '')} sharing {application.preferences.roomType === 'ac' ? 'AC' : 'Non-AC'}</div>
                            <div className="text-muted-foreground">
                              {application.preferences.duration} days
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => handleViewApplication(application)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              {application.status === 'pending' && (
                                <>
                                  <DropdownMenuItem
                                    onClick={() => handleApplicationAction(application.id, 'approve')}
                                    disabled={isProcessing}
                                  >
                                    <CheckCircle className="mr-2 h-4 w-4" />
                                    Approve
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => handleApplicationAction(application.id, 'reject')}
                                    disabled={isProcessing}
                                  >
                                    <XCircle className="mr-2 h-4 w-4" />
                                    Reject
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => handleApplicationAction(application.id, 'waitlist')}
                                    disabled={isProcessing}
                                  >
                                    <AlertTriangle className="mr-2 h-4 w-4" />
                                    Waitlist
                                  </DropdownMenuItem>
                                </>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}

          {filteredApplications.length === 0 && (
            <div className="text-center py-8">
              <Users className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No applications found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Try adjusting your search criteria or filters.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
