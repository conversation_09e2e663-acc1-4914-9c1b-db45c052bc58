import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Building2,
  Users,
  UserCheck,
  User,
  LayoutDashboard,
  Settings,
  LogOut,
  ChevronUp,
  Bell,
  Search,
  DollarSign,
  BarChart3,
  Shield,
  FileText,
  MessageSquare,
  Cog,
  Calendar,
  Wrench,
  Megaphone,
  Package,
  TrendingUp,
  ClipboardList,
  CreditCard,
  HelpCircle,
  Gift,
  Heart,
  Clock,
  Timer,
  Wallet,
  Receipt,
  Users2,
  Zap,
  Building,
  Bed
} from 'lucide-react';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from '@/components/ui/sidebar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { But<PERSON> } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { User as UserType } from '@/data/mockData';

// Optimized navigation structure with logical grouping
interface NavigationGroup {
  title: string;
  items: NavigationItem[];
}

interface NavigationItem {
  title: string;
  url: string;
  icon: React.ComponentType<any>;
  roles: UserType['role'][];
  badge?: string;
  description?: string;
}

const getNavigationStructure = (userRole: UserType['role']): NavigationGroup[] => {
  const navigationGroups: NavigationGroup[] = [];

  // Dashboard - Always first for all roles
  navigationGroups.push({
    title: 'Overview',
    items: [
      {
        title: 'Dashboard',
        url: '/dashboard',
        icon: LayoutDashboard,
        roles: ['superadmin', 'owner', 'employee', 'member'],
        description: 'Main dashboard with key metrics'
      }
    ]
  });

  // Super Admin Navigation
  if (userRole === 'superadmin') {
    navigationGroups.push(
      {
        title: 'System Management',
        items: [
          {
            title: 'System Overview',
            url: '/admin/overview',
            icon: BarChart3,
            roles: ['superadmin'],
            description: 'Platform-wide analytics and metrics'
          },
          {
            title: 'User Management',
            url: '/admin/users',
            icon: Users,
            roles: ['superadmin'],
            description: 'Manage all platform users'
          },
          {
            title: 'Hostel Management',
            url: '/admin/hostels',
            icon: Building2,
            roles: ['superadmin'],
            description: 'Oversee all registered hostels'
          },
          {
            title: 'System Settings',
            url: '/admin/settings',
            icon: Settings,
            roles: ['superadmin'],
            description: 'Platform configuration and settings'
          }
        ]
      },
      {
        title: 'Financial Operations',
        items: [
          {
            title: 'Financial Overview',
            url: '/admin/financial',
            icon: DollarSign,
            roles: ['superadmin'],
            description: 'Revenue and financial analytics'
          },
          {
            title: 'Payment Management',
            url: '/admin/payment-management',
            icon: CreditCard,
            roles: ['superadmin'],
            description: 'Monitor all platform payments'
          },
          {
            title: 'Payment Gateway Config',
            url: '/admin/payment-gateway-config',
            icon: Zap,
            roles: ['superadmin'],
            description: 'Configure payment gateways'
          }
        ]
      },
      {
        title: 'Platform Operations',
        items: [
          {
            title: 'Analytics & Reports',
            url: '/admin/analytics',
            icon: TrendingUp,
            roles: ['superadmin'],
            description: 'Detailed platform analytics'
          },
          {
            title: 'Content Management',
            url: '/admin/content',
            icon: FileText,
            roles: ['superadmin'],
            description: 'Manage platform content'
          },
          {
            title: 'Complaints Management',
            url: '/admin/complaints',
            icon: MessageSquare,
            roles: ['superadmin'],
            description: 'Handle user complaints'
          },
          {
            title: 'Audit Logs',
            url: '/admin/audit-logs',
            icon: Shield,
            roles: ['superadmin'],
            description: 'System security and audit trails'
          }
        ]
      }
    );
  }

  // Owner Navigation
  if (userRole === 'owner') {
    navigationGroups.push(
      {
        title: 'Property Management',
        items: [
          {
            title: 'Owner Dashboard',
            url: '/owner/dashboard',
            icon: BarChart3,
            roles: ['owner'],
            description: 'Business analytics and key metrics'
          },
          {
            title: 'Hostel Registration',
            url: '/owner/registration',
            icon: Building,
            roles: ['owner'],
            description: 'Register and configure your hostel'
          },
          {
            title: 'My Hostels',
            url: '/owner/hostels',
            icon: Building2,
            roles: ['owner'],
            description: 'Manage your hostel properties'
          },
          {
            title: 'Room Allocation',
            url: '/owner/room-allocation',
            icon: Bed,
            roles: ['owner'],
            description: 'Assign rooms to members'
          }
        ]
      },
      {
        title: 'Member Operations',
        items: [
          {
            title: 'Member Applications',
            url: '/owner/member-applications',
            icon: UserCheck,
            roles: ['owner'],
            description: 'Review and approve applications'
          },
          {
            title: 'Guest Management',
            url: '/owner/guests',
            icon: Users,
            roles: ['owner'],
            description: 'Manage current residents'
          },
          {
            title: 'Bookings Overview',
            url: '/owner/bookings',
            icon: Calendar,
            roles: ['owner'],
            description: 'View all bookings and reservations'
          }
        ]
      },
      {
        title: 'Business Operations',
        items: [
          {
            title: 'Revenue Dashboard',
            url: '/owner/revenue',
            icon: DollarSign,
            roles: ['owner'],
            description: 'Financial performance and revenue'
          },
          {
            title: 'Property Analytics',
            url: '/owner/analytics',
            icon: TrendingUp,
            roles: ['owner'],
            description: 'Detailed business insights'
          },
          {
            title: 'Reports & Insights',
            url: '/owner/reports',
            icon: FileText,
            roles: ['owner'],
            description: 'Generate business reports'
          }
        ]
      },
      {
        title: 'Operations & Support',
        items: [
          {
            title: 'Staff Management',
            url: '/owner/staff',
            icon: Users2,
            roles: ['owner'],
            description: 'Manage hostel staff'
          },
          {
            title: 'Maintenance Tracking',
            url: '/owner/maintenance',
            icon: Wrench,
            roles: ['owner'],
            description: 'Track maintenance and issues'
          },
          {
            title: 'Complaint Management',
            url: '/owner/complaints',
            icon: MessageSquare,
            roles: ['owner'],
            description: 'Handle member complaints'
          },
          {
            title: 'Notice Management',
            url: '/owner/notices',
            icon: Bell,
            roles: ['owner'],
            description: 'Manage hostel announcements'
          }
        ]
      }
    );
  }

  // Employee Navigation
  if (userRole === 'employee') {
    navigationGroups.push(
      {
        title: 'Daily Operations',
        items: [
          {
            title: 'Work Dashboard',
            url: '/employee/dashboard',
            icon: LayoutDashboard,
            roles: ['employee'],
            description: 'Daily tasks and overview'
          },
          {
            title: 'Task Management',
            url: '/employee/tasks',
            icon: ClipboardList,
            roles: ['employee'],
            description: 'Manage assigned tasks'
          }
        ]
      },
      {
        title: 'Resident Services',
        items: [
          {
            title: 'Residents Management',
            url: '/employee/residents',
            icon: Users,
            roles: ['employee'],
            description: 'Manage hostel residents'
          },
          {
            title: 'Room Management',
            url: '/employee/rooms',
            icon: Building2,
            roles: ['employee'],
            description: 'Manage room assignments'
          },
          {
            title: 'Complaints Handling',
            url: '/employee/complaints',
            icon: MessageSquare,
            roles: ['employee'],
            description: 'Handle resident complaints'
          }
        ]
      }
    );
  }

  // Member Navigation
  if (userRole === 'member') {
    navigationGroups.push(
      {
        title: 'Booking & Search',
        items: [
          {
            title: 'Search Hostels',
            url: '/member/search',
            icon: Search,
            roles: ['member'],
            description: 'Find and book hostels'
          },
          {
            title: 'My Bookings',
            url: '/member/bookings',
            icon: Calendar,
            roles: ['member'],
            description: 'View your current bookings'
          },
          {
            title: 'Wishlist & Favorites',
            url: '/member/wishlist',
            icon: Heart,
            roles: ['member'],
            description: 'Saved hostels and favorites'
          }
        ]
      },
      {
        title: 'Payments & Billing',
        items: [
          {
            title: 'Payment Dashboard',
            url: '/member/payment-dashboard',
            icon: CreditCard,
            roles: ['member'],
            description: 'Manage payments and billing'
          },
          {
            title: 'Make Payment',
            url: '/member/make-payment',
            icon: DollarSign,
            roles: ['member'],
            description: 'Pay your bills'
          },
          {
            title: 'Payment History',
            url: '/member/payment-history',
            icon: Receipt,
            roles: ['member'],
            description: 'View payment history'
          },
          {
            title: 'Split Payment',
            url: '/member/split-payment',
            icon: Users2,
            roles: ['member'],
            description: 'Split bills with roommates'
          }
        ]
      },
      {
        title: 'Profile & Account',
        items: [
          {
            title: 'Personal Profile',
            url: '/member/personal-profile',
            icon: User,
            roles: ['member'],
            description: 'Manage your profile'
          },
          {
            title: 'Profile Management',
            url: '/member/profile-management',
            icon: Settings,
            roles: ['member'],
            description: 'Account settings'
          }
        ]
      },
      {
        title: 'Support & Community',
        items: [
          {
            title: 'Support Center',
            url: '/member/support',
            icon: HelpCircle,
            roles: ['member'],
            description: 'Get help and support'
          },
          {
            title: 'Noticeboard',
            url: '/member/noticeboard',
            icon: Bell,
            roles: ['member'],
            description: 'Hostel announcements'
          },
          {
            title: 'Notifications',
            url: '/member/notifications',
            icon: Bell,
            roles: ['member'],
            description: 'Your notifications'
          },
          {
            title: 'Referral Program',
            url: '/member/referrals',
            icon: Gift,
            roles: ['member'],
            description: 'Refer friends and earn'
          }
        ]
      }
    );
  }

  return navigationGroups;
};

export const AppSidebar: React.FC = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  if (!user) return null;

  const navigationGroups = getNavigationStructure(user.role);

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  const getUserInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getRoleDisplayName = (role: UserType['role']) => {
    const roleNames = {
      superadmin: 'Super Admin',
      owner: 'Hostel Owner',
      employee: 'Employee',
      member: 'Member',
    };
    return roleNames[role];
  };

  return (
    <Sidebar variant="inset">
      <SidebarHeader>
        <div className="flex items-center space-x-2 px-2 py-2">
          <Building2 className="h-8 w-8 text-primary" />
          <span className="text-xl font-bold bg-gradient-primary bg-clip-text text-transparent">
            HostelHub
          </span>
        </div>
      </SidebarHeader>

      <SidebarContent>
        {navigationGroups.map((group) => (
          <SidebarGroup key={group.title}>
            <SidebarGroupLabel>{group.title}</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {group.items.map((item) => {
                  const isActive = location.pathname === item.url;
                  return (
                    <SidebarMenuItem key={item.title}>
                      <SidebarMenuButton
                        asChild
                        isActive={isActive}
                        tooltip={item.description || item.title}
                      >
                        <button
                          onClick={() => navigate(item.url)}
                          className="flex items-center space-x-2 w-full"
                        >
                          <item.icon className="h-4 w-4" />
                          <span>{item.title}</span>
                          {item.badge && (
                            <span className="ml-auto text-xs bg-primary text-primary-foreground px-1.5 py-0.5 rounded-full">
                              {item.badge}
                            </span>
                          )}
                        </button>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  );
                })}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}

        {/* Settings Group - Always available */}
        <SidebarGroup>
          <SidebarGroupLabel>Settings</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton
                  asChild
                  isActive={location.pathname === '/settings'}
                  tooltip="Account settings and preferences"
                >
                  <button
                    onClick={() => navigate('/settings')}
                    className="flex items-center space-x-2 w-full"
                  >
                    <Settings className="h-4 w-4" />
                    <span>Settings</span>
                  </button>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton
                  size="lg"
                  className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                >
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarImage src={user.avatar} alt={user.name} />
                    <AvatarFallback className="rounded-lg">
                      {getUserInitials(user.name)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold">{user.name}</span>
                    <span className="truncate text-xs text-muted-foreground">
                      {getRoleDisplayName(user.role)}
                    </span>
                  </div>
                  <ChevronUp className="ml-auto size-4" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                side="bottom"
                align="end"
                sideOffset={4}
              >
                <DropdownMenuItem onClick={() => navigate('/profile')}>
                  <User className="mr-2 h-4 w-4" />
                  Profile
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => navigate('/settings')}>
                  <Settings className="mr-2 h-4 w-4" />
                  Settings
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout} className="text-destructive">
                  <LogOut className="mr-2 h-4 w-4" />
                  Sign Out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
};
