import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  User, 
  Mail,
  Phone,
  CreditCard,
  Shield,
  CheckCircle,
  AlertCircle,
  ArrowRight,
  ArrowLeft,
  Users,
  Calendar,
  FileText,
  Clock
} from 'lucide-react';

interface RegistrationData {
  membershipType: 'regular' | 'guest';
  personalInfo: {
    name: string;
    email: string;
    phone: string;
    dateOfBirth: string;
    gender: string;
    address: string;
  };
  idProof: {
    type: 'aadhar' | 'pan' | 'passport' | 'driving_license';
    number: string;
    file?: File;
  };
  emergencyContact: {
    name: string;
    phone: string;
    relationship: string;
  };
  preferences: {
    roomType: string;
    foodPreference: string;
    specialRequests: string;
  };
}

interface MemberRegistrationFlowProps {
  onComplete: (data: RegistrationData) => void;
  onCancel: () => void;
  className?: string;
}

export const MemberRegistrationFlow: React.FC<MemberRegistrationFlowProps> = ({
  onComplete,
  onCancel,
  className
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const [registrationData, setRegistrationData] = useState<RegistrationData>({
    membershipType: 'regular',
    personalInfo: {
      name: '',
      email: '',
      phone: '',
      dateOfBirth: '',
      gender: '',
      address: ''
    },
    idProof: {
      type: 'aadhar',
      number: ''
    },
    emergencyContact: {
      name: '',
      phone: '',
      relationship: ''
    },
    preferences: {
      roomType: '',
      foodPreference: '',
      specialRequests: ''
    }
  });

  const steps = [
    { id: 1, title: 'Membership Type', icon: Users },
    { id: 2, title: 'Personal Information', icon: User },
    { id: 3, title: 'ID Verification', icon: Shield },
    { id: 4, title: 'Emergency Contact', icon: Phone },
    { id: 5, title: 'Preferences', icon: FileText }
  ];

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    switch (step) {
      case 1:
        if (!registrationData.membershipType) {
          newErrors.membershipType = 'Please select a membership type';
        }
        break;
      
      case 2:
        if (!registrationData.personalInfo.name.trim()) {
          newErrors.name = 'Name is required';
        }
        if (!registrationData.personalInfo.email.trim()) {
          newErrors.email = 'Email is required';
        } else if (!/\S+@\S+\.\S+/.test(registrationData.personalInfo.email)) {
          newErrors.email = 'Please enter a valid email';
        }
        if (!registrationData.personalInfo.phone.trim()) {
          newErrors.phone = 'Phone number is required';
        } else if (!/^\+?[\d\s-()]{10,}$/.test(registrationData.personalInfo.phone)) {
          newErrors.phone = 'Please enter a valid phone number';
        }
        if (!registrationData.personalInfo.dateOfBirth) {
          newErrors.dateOfBirth = 'Date of birth is required';
        }
        if (!registrationData.personalInfo.gender) {
          newErrors.gender = 'Gender is required';
        }
        if (!registrationData.personalInfo.address.trim()) {
          newErrors.address = 'Address is required';
        }
        break;
      
      case 3:
        if (!registrationData.idProof.number.trim()) {
          newErrors.idNumber = 'ID number is required';
        }
        break;
      
      case 4:
        if (!registrationData.emergencyContact.name.trim()) {
          newErrors.emergencyName = 'Emergency contact name is required';
        }
        if (!registrationData.emergencyContact.phone.trim()) {
          newErrors.emergencyPhone = 'Emergency contact phone is required';
        }
        if (!registrationData.emergencyContact.relationship.trim()) {
          newErrors.emergencyRelationship = 'Relationship is required';
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, steps.length));
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) return;

    setIsLoading(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    onComplete(registrationData);
    setIsLoading(false);
  };

  const updateRegistrationData = (section: keyof RegistrationData, data: any) => {
    setRegistrationData(prev => ({
      ...prev,
      [section]: { ...prev[section], ...data }
    }));
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center space-y-2">
              <h3 className="text-lg font-semibold">Choose Your Membership Type</h3>
              <p className="text-muted-foreground">Select the option that best fits your needs</p>
            </div>
            
            <div className="grid gap-4 md:grid-cols-2">
              <Card 
                className={`cursor-pointer transition-all hover:shadow-md ${
                  registrationData.membershipType === 'regular' ? 'ring-2 ring-primary' : ''
                }`}
                onClick={() => updateRegistrationData('membershipType', 'regular')}
              >
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <Users className="h-6 w-6 text-blue-600" />
                      <h4 className="font-semibold">Regular Member</h4>
                    </div>
                    <div className="space-y-2">
                      <p className="text-sm text-muted-foreground">Monthly Subscription</p>
                      <ul className="text-sm space-y-1">
                        <li>• Monthly billing cycle</li>
                        <li>• Better rates for long-term stays</li>
                        <li>• Priority booking</li>
                        <li>• Member benefits & discounts</li>
                      </ul>
                    </div>
                    <Badge className="bg-blue-100 text-blue-800">Recommended for students</Badge>
                  </div>
                </CardContent>
              </Card>

              <Card 
                className={`cursor-pointer transition-all hover:shadow-md ${
                  registrationData.membershipType === 'guest' ? 'ring-2 ring-primary' : ''
                }`}
                onClick={() => updateRegistrationData('membershipType', 'guest')}
              >
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <Calendar className="h-6 w-6 text-green-600" />
                      <h4 className="font-semibold">Guest Member</h4>
                    </div>
                    <div className="space-y-2">
                      <p className="text-sm text-muted-foreground">Daily Booking</p>
                      <ul className="text-sm space-y-1">
                        <li>• Pay per day/hour</li>
                        <li>• Flexible check-in/out</li>
                        <li>• No long-term commitment</li>
                        <li>• Instant booking</li>
                      </ul>
                    </div>
                    <Badge className="bg-green-100 text-green-800">Perfect for short stays</Badge>
                  </div>
                </CardContent>
              </Card>
            </div>
            
            {errors.membershipType && (
              <p className="text-sm text-red-600 flex items-center space-x-1">
                <AlertCircle className="h-4 w-4" />
                <span>{errors.membershipType}</span>
              </p>
            )}
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center space-y-2">
              <h3 className="text-lg font-semibold">Personal Information</h3>
              <p className="text-muted-foreground">Please provide your basic details</p>
            </div>
            
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="name">Full Name *</Label>
                <Input
                  id="name"
                  value={registrationData.personalInfo.name}
                  onChange={(e) => updateRegistrationData('personalInfo', { name: e.target.value })}
                  placeholder="Enter your full name"
                  className={errors.name ? 'border-red-500' : ''}
                />
                {errors.name && <p className="text-sm text-red-600">{errors.name}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  type="email"
                  value={registrationData.personalInfo.email}
                  onChange={(e) => updateRegistrationData('personalInfo', { email: e.target.value })}
                  placeholder="Enter your email"
                  className={errors.email ? 'border-red-500' : ''}
                />
                {errors.email && <p className="text-sm text-red-600">{errors.email}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number *</Label>
                <Input
                  id="phone"
                  value={registrationData.personalInfo.phone}
                  onChange={(e) => updateRegistrationData('personalInfo', { phone: e.target.value })}
                  placeholder="+91 9876543210"
                  className={errors.phone ? 'border-red-500' : ''}
                />
                {errors.phone && <p className="text-sm text-red-600">{errors.phone}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="dob">Date of Birth *</Label>
                <Input
                  id="dob"
                  type="date"
                  value={registrationData.personalInfo.dateOfBirth}
                  onChange={(e) => updateRegistrationData('personalInfo', { dateOfBirth: e.target.value })}
                  className={errors.dateOfBirth ? 'border-red-500' : ''}
                />
                {errors.dateOfBirth && <p className="text-sm text-red-600">{errors.dateOfBirth}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="gender">Gender *</Label>
                <Select 
                  value={registrationData.personalInfo.gender} 
                  onValueChange={(value) => updateRegistrationData('personalInfo', { gender: value })}
                >
                  <SelectTrigger className={errors.gender ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select gender" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="male">Male</SelectItem>
                    <SelectItem value="female">Female</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                    <SelectItem value="prefer-not-to-say">Prefer not to say</SelectItem>
                  </SelectContent>
                </Select>
                {errors.gender && <p className="text-sm text-red-600">{errors.gender}</p>}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="address">Address *</Label>
              <Textarea
                id="address"
                value={registrationData.personalInfo.address}
                onChange={(e) => updateRegistrationData('personalInfo', { address: e.target.value })}
                placeholder="Enter your complete address"
                className={errors.address ? 'border-red-500' : ''}
                rows={3}
              />
              {errors.address && <p className="text-sm text-red-600">{errors.address}</p>}
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center space-y-2">
              <h3 className="text-lg font-semibold">ID Verification</h3>
              <p className="text-muted-foreground">Upload a valid government-issued ID</p>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="idType">ID Type *</Label>
                <Select
                  value={registrationData.idProof.type}
                  onValueChange={(value: any) => updateRegistrationData('idProof', { type: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select ID type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="aadhar">Aadhar Card</SelectItem>
                    <SelectItem value="pan">PAN Card</SelectItem>
                    <SelectItem value="passport">Passport</SelectItem>
                    <SelectItem value="driving_license">Driving License</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="idNumber">ID Number *</Label>
                <Input
                  id="idNumber"
                  value={registrationData.idProof.number}
                  onChange={(e) => updateRegistrationData('idProof', { number: e.target.value })}
                  placeholder="Enter ID number"
                  className={errors.idNumber ? 'border-red-500' : ''}
                />
                {errors.idNumber && <p className="text-sm text-red-600">{errors.idNumber}</p>}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="idFile">Upload ID Document</Label>
              <Input
                id="idFile"
                type="file"
                accept="image/*,.pdf"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) updateRegistrationData('idProof', { file });
                }}
              />
              <p className="text-xs text-muted-foreground">
                Accepted formats: JPG, PNG, PDF (Max 5MB)
              </p>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center space-y-2">
              <h3 className="text-lg font-semibold">Emergency Contact</h3>
              <p className="text-muted-foreground">Provide details of someone we can contact in case of emergency</p>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="emergencyName">Contact Name *</Label>
                <Input
                  id="emergencyName"
                  value={registrationData.emergencyContact.name}
                  onChange={(e) => updateRegistrationData('emergencyContact', { name: e.target.value })}
                  placeholder="Enter contact name"
                  className={errors.emergencyName ? 'border-red-500' : ''}
                />
                {errors.emergencyName && <p className="text-sm text-red-600">{errors.emergencyName}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="emergencyPhone">Contact Phone *</Label>
                <Input
                  id="emergencyPhone"
                  value={registrationData.emergencyContact.phone}
                  onChange={(e) => updateRegistrationData('emergencyContact', { phone: e.target.value })}
                  placeholder="+91 9876543210"
                  className={errors.emergencyPhone ? 'border-red-500' : ''}
                />
                {errors.emergencyPhone && <p className="text-sm text-red-600">{errors.emergencyPhone}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="relationship">Relationship *</Label>
                <Select
                  value={registrationData.emergencyContact.relationship}
                  onValueChange={(value) => updateRegistrationData('emergencyContact', { relationship: value })}
                >
                  <SelectTrigger className={errors.emergencyRelationship ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select relationship" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="parent">Parent</SelectItem>
                    <SelectItem value="sibling">Sibling</SelectItem>
                    <SelectItem value="spouse">Spouse</SelectItem>
                    <SelectItem value="friend">Friend</SelectItem>
                    <SelectItem value="relative">Relative</SelectItem>
                    <SelectItem value="guardian">Guardian</SelectItem>
                  </SelectContent>
                </Select>
                {errors.emergencyRelationship && <p className="text-sm text-red-600">{errors.emergencyRelationship}</p>}
              </div>
            </div>
          </div>
        );

      case 5:
        return (
          <div className="space-y-6">
            <div className="text-center space-y-2">
              <h3 className="text-lg font-semibold">Preferences</h3>
              <p className="text-muted-foreground">Tell us about your preferences to help us serve you better</p>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="roomType">Preferred Room Type</Label>
                <Select
                  value={registrationData.preferences.roomType}
                  onValueChange={(value) => updateRegistrationData('preferences', { roomType: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select room type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="shared">Shared Room</SelectItem>
                    <SelectItem value="semi-private">Semi-Private</SelectItem>
                    <SelectItem value="private">Private Room</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="foodPreference">Food Preference</Label>
                <Select
                  value={registrationData.preferences.foodPreference}
                  onValueChange={(value) => updateRegistrationData('preferences', { foodPreference: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select food preference" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="vegetarian">Vegetarian</SelectItem>
                    <SelectItem value="non-vegetarian">Non-Vegetarian</SelectItem>
                    <SelectItem value="vegan">Vegan</SelectItem>
                    <SelectItem value="jain">Jain</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="specialRequests">Special Requests</Label>
              <Textarea
                id="specialRequests"
                value={registrationData.preferences.specialRequests}
                onChange={(e) => updateRegistrationData('preferences', { specialRequests: e.target.value })}
                placeholder="Any special requirements or requests..."
                rows={3}
              />
            </div>

            {/* Registration Summary */}
            <Card className="bg-muted/50">
              <CardContent className="pt-4">
                <h4 className="font-medium mb-3">Registration Summary</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Membership Type:</span>
                    <Badge variant="secondary">
                      {registrationData.membershipType === 'regular' ? 'Regular Member' : 'Guest Member'}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Name:</span>
                    <span>{registrationData.personalInfo.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Email:</span>
                    <span>{registrationData.personalInfo.email}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Phone:</span>
                    <span>{registrationData.personalInfo.phone}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      default:
        return <div>Step content not implemented</div>;
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Member Registration</CardTitle>
        <CardDescription>
          Complete your registration to start booking hostels
        </CardDescription>
        
        {/* Progress Steps */}
        <div className="flex items-center justify-between mt-4">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                currentStep >= step.id 
                  ? 'bg-primary border-primary text-primary-foreground' 
                  : 'border-muted-foreground text-muted-foreground'
              }`}>
                {currentStep > step.id ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  <step.icon className="h-4 w-4" />
                )}
              </div>
              {index < steps.length - 1 && (
                <div className={`w-12 h-0.5 mx-2 ${
                  currentStep > step.id ? 'bg-primary' : 'bg-muted'
                }`} />
              )}
            </div>
          ))}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {renderStepContent()}
        
        {/* Navigation Buttons */}
        <div className="flex items-center justify-between pt-6 border-t">
          <Button 
            variant="outline" 
            onClick={currentStep === 1 ? onCancel : handlePrevious}
            disabled={isLoading}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {currentStep === 1 ? 'Cancel' : 'Previous'}
          </Button>
          
          {currentStep < steps.length ? (
            <Button onClick={handleNext} disabled={isLoading}>
              Next
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          ) : (
            <Button onClick={handleSubmit} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Submitting...
                </>
              ) : (
                <>
                  Complete Registration
                  <CheckCircle className="h-4 w-4 ml-2" />
                </>
              )}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
