import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Settings, 
  Save,
  RefreshCw,
  Shield,
  DollarSign,
  Mail,
  Bell,
  Globe,
  Database,
  Lock,
  Zap,
  AlertTriangle,
  CheckCircle,
  Clock,
  Server,
  Smartphone,
  Eye,
  EyeOff
} from 'lucide-react';

export const SystemSettings: React.FC = () => {
  // Platform Settings State
  const [platformSettings, setPlatformSettings] = useState({
    platformName: 'HostelHub',
    platformDescription: 'Your trusted hostel booking platform',
    supportEmail: '<EMAIL>',
    maintenanceMode: false,
    registrationEnabled: true,
    emailVerificationRequired: true,
    autoApproveHostels: false,
    maxBookingDuration: 365,
    minBookingDuration: 1,
    cancellationWindow: 24,
    platformCommission: 5.0,
    currency: 'INR',
    timezone: 'Asia/Kolkata',
    language: 'en'
  });

  // Payment Settings State
  const [paymentSettings, setPaymentSettings] = useState({
    enableCardPayments: true,
    enableUPIPayments: true,
    enableBankTransfer: true,
    enableWalletPayments: false,
    paymentTimeout: 15,
    autoRefundEnabled: true,
    refundProcessingDays: 7,
    minimumPaymentAmount: 100,
    maximumPaymentAmount: 100000
  });

  // Notification Settings State
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    bookingConfirmations: true,
    paymentAlerts: true,
    maintenanceAlerts: true,
    marketingEmails: false,
    weeklyReports: true,
    monthlyReports: true
  });

  // Security Settings State
  const [securitySettings, setSecuritySettings] = useState({
    passwordMinLength: 8,
    requireSpecialChars: true,
    requireNumbers: true,
    requireUppercase: true,
    sessionTimeout: 30,
    maxLoginAttempts: 5,
    lockoutDuration: 15,
    twoFactorAuth: false,
    ipWhitelisting: false,
    auditLogging: true
  });

  const handleSaveSettings = (section: string) => {
    // In a real app, this would make an API call
    console.log(`Saving ${section} settings`);
    // Show success message
  };

  const handleResetSettings = (section: string) => {
    // In a real app, this would reset to default values
    console.log(`Resetting ${section} settings`);
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">System Settings</h1>
          <p className="text-muted-foreground">
            Configure platform settings and system preferences
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Reset All
          </Button>
          <Button>
            <Save className="mr-2 h-4 w-4" />
            Save All Changes
          </Button>
        </div>
      </div>

      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Server className="h-5 w-5" />
            System Status
          </CardTitle>
          <CardDescription>
            Current system health and operational status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div className="flex items-center space-x-3">
              <div className="flex items-center justify-center w-10 h-10 bg-green-100 rounded-full">
                <CheckCircle className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <div className="font-medium">System Health</div>
                <div className="text-sm text-green-600">Operational</div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-full">
                <Database className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <div className="font-medium">Database</div>
                <div className="text-sm text-blue-600">Connected</div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="flex items-center justify-center w-10 h-10 bg-orange-100 rounded-full">
                <Clock className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <div className="font-medium">Uptime</div>
                <div className="text-sm text-orange-600">99.9%</div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="flex items-center justify-center w-10 h-10 bg-purple-100 rounded-full">
                <Zap className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <div className="font-medium">Performance</div>
                <div className="text-sm text-purple-600">Optimal</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Platform Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Platform Settings
          </CardTitle>
          <CardDescription>
            General platform configuration and branding
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="platformName">Platform Name</Label>
              <Input
                id="platformName"
                value={platformSettings.platformName}
                onChange={(e) => setPlatformSettings({...platformSettings, platformName: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="supportEmail">Support Email</Label>
              <Input
                id="supportEmail"
                type="email"
                value={platformSettings.supportEmail}
                onChange={(e) => setPlatformSettings({...platformSettings, supportEmail: e.target.value})}
              />
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="platformDescription">Platform Description</Label>
            <Textarea
              id="platformDescription"
              value={platformSettings.platformDescription}
              onChange={(e) => setPlatformSettings({...platformSettings, platformDescription: e.target.value})}
              rows={3}
            />
          </div>

          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <Label htmlFor="currency">Currency</Label>
              <Select value={platformSettings.currency} onValueChange={(value) => setPlatformSettings({...platformSettings, currency: value})}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="INR">Indian Rupee (₹)</SelectItem>
                  <SelectItem value="USD">US Dollar ($)</SelectItem>
                  <SelectItem value="EUR">Euro (€)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="timezone">Timezone</Label>
              <Select value={platformSettings.timezone} onValueChange={(value) => setPlatformSettings({...platformSettings, timezone: value})}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Asia/Kolkata">Asia/Kolkata</SelectItem>
                  <SelectItem value="UTC">UTC</SelectItem>
                  <SelectItem value="America/New_York">America/New_York</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="platformCommission">Platform Commission (%)</Label>
              <Input
                id="platformCommission"
                type="number"
                step="0.1"
                min="0"
                max="100"
                value={platformSettings.platformCommission}
                onChange={(e) => setPlatformSettings({...platformSettings, platformCommission: parseFloat(e.target.value)})}
              />
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Maintenance Mode</Label>
                <div className="text-sm text-muted-foreground">
                  Enable to temporarily disable platform access
                </div>
              </div>
              <Switch
                checked={platformSettings.maintenanceMode}
                onCheckedChange={(checked) => setPlatformSettings({...platformSettings, maintenanceMode: checked})}
              />
            </div>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>User Registration</Label>
                <div className="text-sm text-muted-foreground">
                  Allow new users to register on the platform
                </div>
              </div>
              <Switch
                checked={platformSettings.registrationEnabled}
                onCheckedChange={(checked) => setPlatformSettings({...platformSettings, registrationEnabled: checked})}
              />
            </div>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Email Verification Required</Label>
                <div className="text-sm text-muted-foreground">
                  Require email verification for new accounts
                </div>
              </div>
              <Switch
                checked={platformSettings.emailVerificationRequired}
                onCheckedChange={(checked) => setPlatformSettings({...platformSettings, emailVerificationRequired: checked})}
              />
            </div>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Auto-approve Hostels</Label>
                <div className="text-sm text-muted-foreground">
                  Automatically approve new hostel registrations
                </div>
              </div>
              <Switch
                checked={platformSettings.autoApproveHostels}
                onCheckedChange={(checked) => setPlatformSettings({...platformSettings, autoApproveHostels: checked})}
              />
            </div>
          </div>

          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => handleResetSettings('platform')}>
              Reset
            </Button>
            <Button onClick={() => handleSaveSettings('platform')}>
              <Save className="mr-2 h-4 w-4" />
              Save Platform Settings
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Payment Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Payment Settings
          </CardTitle>
          <CardDescription>
            Configure payment methods and processing options
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Card Payments</Label>
                <div className="text-sm text-muted-foreground">
                  Accept credit and debit card payments
                </div>
              </div>
              <Switch
                checked={paymentSettings.enableCardPayments}
                onCheckedChange={(checked) => setPaymentSettings({...paymentSettings, enableCardPayments: checked})}
              />
            </div>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>UPI Payments</Label>
                <div className="text-sm text-muted-foreground">
                  Accept UPI payments
                </div>
              </div>
              <Switch
                checked={paymentSettings.enableUPIPayments}
                onCheckedChange={(checked) => setPaymentSettings({...paymentSettings, enableUPIPayments: checked})}
              />
            </div>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Bank Transfer</Label>
                <div className="text-sm text-muted-foreground">
                  Accept direct bank transfers
                </div>
              </div>
              <Switch
                checked={paymentSettings.enableBankTransfer}
                onCheckedChange={(checked) => setPaymentSettings({...paymentSettings, enableBankTransfer: checked})}
              />
            </div>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Auto Refund</Label>
                <div className="text-sm text-muted-foreground">
                  Automatically process refunds for cancellations
                </div>
              </div>
              <Switch
                checked={paymentSettings.autoRefundEnabled}
                onCheckedChange={(checked) => setPaymentSettings({...paymentSettings, autoRefundEnabled: checked})}
              />
            </div>
          </div>

          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <Label htmlFor="paymentTimeout">Payment Timeout (minutes)</Label>
              <Input
                id="paymentTimeout"
                type="number"
                min="5"
                max="60"
                value={paymentSettings.paymentTimeout}
                onChange={(e) => setPaymentSettings({...paymentSettings, paymentTimeout: parseInt(e.target.value)})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="minPayment">Minimum Payment (₹)</Label>
              <Input
                id="minPayment"
                type="number"
                min="1"
                value={paymentSettings.minimumPaymentAmount}
                onChange={(e) => setPaymentSettings({...paymentSettings, minimumPaymentAmount: parseInt(e.target.value)})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="maxPayment">Maximum Payment (₹)</Label>
              <Input
                id="maxPayment"
                type="number"
                min="1000"
                value={paymentSettings.maximumPaymentAmount}
                onChange={(e) => setPaymentSettings({...paymentSettings, maximumPaymentAmount: parseInt(e.target.value)})}
              />
            </div>
          </div>

          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => handleResetSettings('payment')}>
              Reset
            </Button>
            <Button onClick={() => handleSaveSettings('payment')}>
              <Save className="mr-2 h-4 w-4" />
              Save Payment Settings
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Security Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Security Settings
          </CardTitle>
          <CardDescription>
            Configure security policies and authentication requirements
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="passwordMinLength">Minimum Password Length</Label>
              <Input
                id="passwordMinLength"
                type="number"
                min="6"
                max="20"
                value={securitySettings.passwordMinLength}
                onChange={(e) => setSecuritySettings({...securitySettings, passwordMinLength: parseInt(e.target.value)})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="sessionTimeout">Session Timeout (minutes)</Label>
              <Input
                id="sessionTimeout"
                type="number"
                min="5"
                max="480"
                value={securitySettings.sessionTimeout}
                onChange={(e) => setSecuritySettings({...securitySettings, sessionTimeout: parseInt(e.target.value)})}
              />
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Require Special Characters</Label>
                <div className="text-sm text-muted-foreground">
                  Passwords must contain special characters
                </div>
              </div>
              <Switch
                checked={securitySettings.requireSpecialChars}
                onCheckedChange={(checked) => setSecuritySettings({...securitySettings, requireSpecialChars: checked})}
              />
            </div>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Require Numbers</Label>
                <div className="text-sm text-muted-foreground">
                  Passwords must contain numbers
                </div>
              </div>
              <Switch
                checked={securitySettings.requireNumbers}
                onCheckedChange={(checked) => setSecuritySettings({...securitySettings, requireNumbers: checked})}
              />
            </div>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Two-Factor Authentication</Label>
                <div className="text-sm text-muted-foreground">
                  Require 2FA for admin accounts
                </div>
              </div>
              <Switch
                checked={securitySettings.twoFactorAuth}
                onCheckedChange={(checked) => setSecuritySettings({...securitySettings, twoFactorAuth: checked})}
              />
            </div>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Audit Logging</Label>
                <div className="text-sm text-muted-foreground">
                  Log all administrative actions
                </div>
              </div>
              <Switch
                checked={securitySettings.auditLogging}
                onCheckedChange={(checked) => setSecuritySettings({...securitySettings, auditLogging: checked})}
              />
            </div>
          </div>

          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => handleResetSettings('security')}>
              Reset
            </Button>
            <Button onClick={() => handleSaveSettings('security')}>
              <Save className="mr-2 h-4 w-4" />
              Save Security Settings
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
