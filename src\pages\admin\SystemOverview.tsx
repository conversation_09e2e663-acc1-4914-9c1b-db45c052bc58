import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Tabs,
  Ta<PERSON>Content,
  Ta<PERSON>List,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Building2,
  Users,
  CreditCard,
  AlertTriangle,
  TrendingUp,
  DollarSign,
  Activity,
  ArrowUpRight,
  ArrowDownRight,
  Settings,
  Download,
  BarChart3,
  Plus,
  PieChart
} from 'lucide-react';
import { mockHostels, mockUsers, mockPayments, mockComplaints } from '@/data/mockData';
import { AdminDashboardMetrics } from '@/types/admin';
import { useNavigate } from 'react-router-dom';
import { toast } from '@/hooks/use-toast';

// Mock admin dashboard metrics
const mockAdminMetrics: AdminDashboardMetrics = {
  platform: {
    totalHostels: mockHostels.length,
    activeHostels: mockHostels.filter(h => h.status === 'active').length,
    totalUsers: mockUsers.length,
    activeUsers: mockUsers.filter(u => u.status === 'active').length,
    totalRevenue: mockPayments.reduce((sum, p) => sum + p.amount, 0),
    platformRevenue: mockPayments.reduce((sum, p) => sum + p.splitDetails.platform, 0),
    occupancyRate: 85.2,
    growthRate: 12.5
  },
  hostels: {
    registrationTrend: [
      { month: 'Jan', count: 5, revenue: 125000 },
      { month: 'Feb', count: 8, revenue: 200000 },
      { month: 'Mar', count: 12, revenue: 300000 },
      { month: 'Apr', count: 15, revenue: 375000 },
      { month: 'May', count: 18, revenue: 450000 },
      { month: 'Jun', count: 22, revenue: 550000 }
    ],
    topPerforming: mockHostels.slice(0, 5).map(h => ({
      id: h.id,
      name: h.name,
      city: h.city,
      area: h.areaId,
      occupancyRate: ((h.totalBeds - h.availableBeds) / h.totalBeds) * 100,
      revenue: h.pricePerBed * (h.totalBeds - h.availableBeds),
      rating: h.rating
    })),
    byLocation: [
      {
        city: 'Mumbai',
        areas: [
          { name: 'Andheri', hostelCount: 8, totalBeds: 800, occupiedBeds: 680, revenue: 544000 },
          { name: 'Bandra', hostelCount: 6, totalBeds: 600, occupiedBeds: 510, revenue: 408000 }
        ]
      },
      {
        city: 'Delhi',
        areas: [
          { name: 'CP', hostelCount: 5, totalBeds: 500, occupiedBeds: 425, revenue: 340000 },
          { name: 'Karol Bagh', hostelCount: 7, totalBeds: 700, occupiedBeds: 595, revenue: 476000 }
        ]
      }
    ]
  },
  users: {
    growthTrend: [
      { month: 'Jan', members: 150, owners: 5, employees: 12 },
      { month: 'Feb', members: 220, owners: 8, employees: 18 },
      { month: 'Mar', members: 310, owners: 12, employees: 25 },
      { month: 'Apr', members: 420, owners: 15, employees: 32 },
      { month: 'May', members: 550, owners: 18, employees: 40 },
      { month: 'Jun', members: 680, owners: 22, employees: 48 }
    ],
    registrationsByRole: {
      members: 680,
      owners: 22,
      employees: 48
    },
    activeUsers: {
      daily: 245,
      weekly: 580,
      monthly: 680
    }
  },
  payments: {
    transactionTrend: [
      { month: 'Jan', totalAmount: 125000, transactionCount: 45, successRate: 94.2 },
      { month: 'Feb', totalAmount: 200000, transactionCount: 72, successRate: 95.8 },
      { month: 'Mar', totalAmount: 300000, transactionCount: 108, successRate: 96.5 },
      { month: 'Apr', totalAmount: 375000, transactionCount: 135, successRate: 97.1 },
      { month: 'May', totalAmount: 450000, transactionCount: 162, successRate: 96.8 },
      { month: 'Jun', totalAmount: 550000, transactionCount: 198, successRate: 97.5 }
    ],
    methodDistribution: [
      { method: 'UPI', count: 120, amount: 300000 },
      { method: 'Card', count: 78, amount: 250000 },
      { method: 'Net Banking', count: 45, amount: 150000 },
      { method: 'Wallet', count: 32, amount: 80000 }
    ],
    failureAnalysis: {
      totalFailures: 15,
      failureRate: 2.5,
      commonReasons: [
        { reason: 'Insufficient Balance', count: 8 },
        { reason: 'Network Error', count: 4 },
        { reason: 'Card Declined', count: 3 }
      ]
    }
  }
};

export const SystemOverview: React.FC = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('overview');

  const metrics = mockAdminMetrics;

  const systemStats = [
    {
      title: 'Total Hostels',
      value: metrics.platform.totalHostels,
      change: '+12%',
      trend: 'up' as const,
      icon: Building2,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Active Users',
      value: metrics.platform.activeUsers,
      change: '+8%',
      trend: 'up' as const,
      icon: Users,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Platform Revenue',
      value: `₹${(metrics.platform.platformRevenue / 1000).toFixed(0)}K`,
      change: '+15%',
      trend: 'up' as const,
      icon: DollarSign,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      title: 'Occupancy Rate',
      value: `${metrics.platform.occupancyRate}%`,
      change: '+3%',
      trend: 'up' as const,
      icon: Activity,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
    {
      title: 'Open Complaints',
      value: mockComplaints.filter(c => c.status === 'open').length,
      change: '-5%',
      trend: 'down' as const,
      icon: AlertTriangle,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
    },
  ];

  // Helper functions
  const handleExportData = () => {
    toast({
      title: "Export Started",
      description: "Your report is being generated and will be downloaded shortly.",
    });
  };

  const handleAddHostel = () => {
    navigate('/admin/hostels/add');
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">System Overview</h1>
          <p className="text-muted-foreground">
            Monitor platform performance, manage hostels, and access comprehensive analytics
          </p>
        </div>
        <div className="flex flex-col space-y-2 md:flex-row md:space-x-2 md:space-y-0">
          <Button variant="outline" size="sm" onClick={handleExportData}>
            <Download className="mr-2 h-4 w-4" />
            Export Reports
          </Button>
          <Button variant="outline" size="sm" onClick={() => navigate('/admin/analytics')}>
            <BarChart3 className="mr-2 h-4 w-4" />
            Advanced Analytics
          </Button>
          <Button size="sm" onClick={() => navigate('/admin/settings')}>
            <Settings className="mr-2 h-4 w-4" />
            Platform Settings
          </Button>
        </div>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {systemStats.map((stat, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                {stat.trend === 'up' ? (
                  <ArrowUpRight className="mr-1 h-3 w-3 text-green-500" />
                ) : (
                  <ArrowDownRight className="mr-1 h-3 w-3 text-red-500" />
                )}
                <span>{stat.change} from last month</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="hostels">Hostel Management</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Location-based Hostel Overview */}
            <Card>
              <CardHeader>
                <CardTitle>Hostels by Location</CardTitle>
                <CardDescription>
                  City → Area hierarchy with performance metrics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {metrics.hostels.byLocation.map((city) => (
                    <div key={city.city} className="border rounded-lg p-4">
                      <h4 className="font-medium mb-2">{city.city}</h4>
                      <div className="space-y-2">
                        {city.areas.map((area) => (
                          <div key={area.name} className="flex justify-between items-center text-sm">
                            <span>{area.name}</span>
                            <div className="text-right">
                              <div>{area.hostelCount} hostels</div>
                              <div className="text-muted-foreground">
                                {((area.occupiedBeds / area.totalBeds) * 100).toFixed(1)}% occupied
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Top Performing Hostels */}
            <Card>
              <CardHeader>
                <CardTitle>Top Performing Hostels</CardTitle>
                <CardDescription>
                  Highest revenue and occupancy rates
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {metrics.hostels.topPerforming.map((hostel, index) => (
                    <div key={hostel.id} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-sm font-medium">
                          {index + 1}
                        </div>
                        <div>
                          <div className="font-medium">{hostel.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {hostel.city}, {hostel.area}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">₹{(hostel.revenue / 1000).toFixed(0)}K</div>
                        <div className="text-sm text-muted-foreground">
                          {hostel.occupancyRate.toFixed(1)}% occupied
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>
                Common administrative tasks
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-4">
                <Button variant="outline" className="h-20 flex-col" onClick={handleAddHostel}>
                  <Building2 className="h-6 w-6 mb-2" />
                  <span className="text-sm">Add Hostel</span>
                </Button>
                <Button variant="outline" className="h-20 flex-col" onClick={() => navigate('/admin/users')}>
                  <Users className="h-6 w-6 mb-2" />
                  <span className="text-sm">Manage Users</span>
                </Button>
                <Button variant="outline" className="h-20 flex-col" onClick={() => navigate('/admin/complaints')}>
                  <AlertTriangle className="h-6 w-6 mb-2" />
                  <span className="text-sm">View Complaints</span>
                </Button>
                <Button variant="outline" className="h-20 flex-col" onClick={() => navigate('/admin/payment-management')}>
                  <CreditCard className="h-6 w-6 mb-2" />
                  <span className="text-sm">Payment Reports</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Hostel Management Tab */}
        <TabsContent value="hostels" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Hostel Management</CardTitle>
              <CardDescription>
                Comprehensive hostel management with CRUD operations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Building2 className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-medium mb-2">Hostel Management Interface</h3>
                <p className="text-muted-foreground mb-4">
                  Full CRUD operations, location-based organization, and advanced filtering
                </p>
                <Button onClick={handleAddHostel}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add New Hostel
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Trends</CardTitle>
                <CardDescription>Monthly revenue growth across the platform</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center text-muted-foreground">
                  <div className="text-center">
                    <BarChart3 className="h-12 w-12 mx-auto mb-2" />
                    <p>Revenue chart would be displayed here</p>
                    <p className="text-sm">Integration with charting library needed</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>User Growth</CardTitle>
                <CardDescription>User registration trends by role</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center text-muted-foreground">
                  <div className="text-center">
                    <PieChart className="h-12 w-12 mx-auto mb-2" />
                    <p>User growth chart would be displayed here</p>
                    <p className="text-sm">Integration with charting library needed</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Reports Tab */}
        <TabsContent value="reports" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Generate Reports</CardTitle>
              <CardDescription>Create and export comprehensive platform reports</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <Button variant="outline" className="h-24 flex-col" onClick={handleExportData}>
                  <Building2 className="h-8 w-8 mb-2" />
                  <span>Hostel Performance Report</span>
                </Button>
                <Button variant="outline" className="h-24 flex-col" onClick={handleExportData}>
                  <DollarSign className="h-8 w-8 mb-2" />
                  <span>Financial Summary Report</span>
                </Button>
                <Button variant="outline" className="h-24 flex-col" onClick={handleExportData}>
                  <Users className="h-8 w-8 mb-2" />
                  <span>User Analytics Report</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
