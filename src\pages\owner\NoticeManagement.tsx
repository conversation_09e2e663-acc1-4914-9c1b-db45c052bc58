import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Bell, 
  Plus,
  Search, 
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Send,
  Calendar,
  AlertTriangle,
  Info,
  Megaphone,
  Settings,
  Clock,
  Users,
  Pin,
  Archive,
  Filter
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

// Notice interface
interface Notice {
  id: string;
  title: string;
  content: string;
  type: 'general' | 'urgent' | 'maintenance' | 'events' | 'food' | 'rules';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  postedBy: string;
  postedDate: string;
  expiryDate?: string;
  isPinned: boolean;
  isActive: boolean;
  targetAudience: 'all' | 'specific_rooms' | 'specific_floors';
  targetRooms?: string[];
  targetFloors?: string[];
  readCount: number;
  totalMembers: number;
  attachments?: string[];
}

// Mock notices data
const mockNotices: Notice[] = [
  {
    id: '1',
    title: 'Water Supply Maintenance - Tomorrow 10 AM to 2 PM',
    content: 'Dear residents, we will be conducting water supply maintenance tomorrow from 10 AM to 2 PM. Please store water in advance. We apologize for any inconvenience caused.',
    type: 'maintenance',
    priority: 'urgent',
    postedBy: 'Hostel Management',
    postedDate: '2024-01-22T09:00:00Z',
    expiryDate: '2024-01-24T18:00:00Z',
    isPinned: true,
    isActive: true,
    targetAudience: 'all',
    readCount: 45,
    totalMembers: 75
  },
  {
    id: '2',
    title: 'New Food Menu Starting Monday',
    content: 'We are excited to introduce our new food menu starting this Monday. The menu includes more variety and healthier options. Check the dining hall for the complete menu.',
    type: 'food',
    priority: 'medium',
    postedBy: 'Kitchen Staff',
    postedDate: '2024-01-21T14:30:00Z',
    isPinned: false,
    isActive: true,
    targetAudience: 'all',
    readCount: 68,
    totalMembers: 75
  },
  {
    id: '3',
    title: 'Republic Day Celebration - January 26th',
    content: 'Join us for Republic Day celebration on January 26th at 8 AM in the common area. Flag hoisting ceremony followed by cultural programs and breakfast.',
    type: 'events',
    priority: 'medium',
    postedBy: 'Event Committee',
    postedDate: '2024-01-20T16:00:00Z',
    expiryDate: '2024-01-26T23:59:59Z',
    isPinned: true,
    isActive: true,
    targetAudience: 'all',
    readCount: 52,
    totalMembers: 75
  }
];

export const NoticeManagement: React.FC = () => {
  const { user } = useAuth();
  const [notices, setNotices] = useState<Notice[]>(mockNotices);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<'all' | Notice['type']>('all');
  const [isNewNoticeOpen, setIsNewNoticeOpen] = useState(false);
  const [selectedNotice, setSelectedNotice] = useState<Notice | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);

  const [newNotice, setNewNotice] = useState({
    title: '',
    content: '',
    type: 'general' as Notice['type'],
    priority: 'medium' as Notice['priority'],
    expiryDate: '',
    isPinned: false,
    targetAudience: 'all' as Notice['targetAudience'],
    targetRooms: [] as string[],
    targetFloors: [] as string[]
  });

  // Filter notices
  const filteredNotices = notices.filter(notice => {
    const matchesSearch = notice.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         notice.content.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === 'all' || notice.type === typeFilter;
    return matchesSearch && matchesType;
  });

  const getTypeIcon = (type: Notice['type']) => {
    switch (type) {
      case 'urgent': return <AlertTriangle className="h-4 w-4" />;
      case 'maintenance': return <Settings className="h-4 w-4" />;
      case 'events': return <Calendar className="h-4 w-4" />;
      case 'food': return <Megaphone className="h-4 w-4" />;
      case 'rules': return <Info className="h-4 w-4" />;
      default: return <Bell className="h-4 w-4" />;
    }
  };

  const getTypeColor = (type: Notice['type']) => {
    switch (type) {
      case 'urgent': return 'bg-red-100 text-red-800';
      case 'maintenance': return 'bg-orange-100 text-orange-800';
      case 'events': return 'bg-purple-100 text-purple-800';
      case 'food': return 'bg-green-100 text-green-800';
      case 'rules': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: Notice['priority']) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleCreateNotice = () => {
    const notice: Notice = {
      id: Date.now().toString(),
      ...newNotice,
      postedBy: user?.name || 'Hostel Owner',
      postedDate: new Date().toISOString(),
      isActive: true,
      readCount: 0,
      totalMembers: 75 // This would come from actual member count
    };

    setNotices(prev => [notice, ...prev]);
    setIsNewNoticeOpen(false);
    setNewNotice({
      title: '',
      content: '',
      type: 'general',
      priority: 'medium',
      expiryDate: '',
      isPinned: false,
      targetAudience: 'all',
      targetRooms: [],
      targetFloors: []
    });

    // Simulate sending push notifications to members
    console.log('Sending push notifications to all registered members for notice:', notice.title);
    alert('Notice posted successfully! Push notifications sent to all members.');
  };

  const handleEditNotice = (notice: Notice) => {
    setSelectedNotice(notice);
    setNewNotice({
      title: notice.title,
      content: notice.content,
      type: notice.type,
      priority: notice.priority,
      expiryDate: notice.expiryDate || '',
      isPinned: notice.isPinned,
      targetAudience: notice.targetAudience,
      targetRooms: notice.targetRooms || [],
      targetFloors: notice.targetFloors || []
    });
    setIsEditMode(true);
    setIsNewNoticeOpen(true);
  };

  const handleUpdateNotice = () => {
    if (!selectedNotice) return;

    setNotices(prev => prev.map(notice => 
      notice.id === selectedNotice.id 
        ? { ...notice, ...newNotice }
        : notice
    ));

    setIsNewNoticeOpen(false);
    setIsEditMode(false);
    setSelectedNotice(null);
    setNewNotice({
      title: '',
      content: '',
      type: 'general',
      priority: 'medium',
      expiryDate: '',
      isPinned: false,
      targetAudience: 'all',
      targetRooms: [],
      targetFloors: []
    });

    alert('Notice updated successfully!');
  };

  const handleDeleteNotice = (noticeId: string) => {
    if (confirm('Are you sure you want to delete this notice?')) {
      setNotices(prev => prev.filter(notice => notice.id !== noticeId));
    }
  };

  const toggleNoticeStatus = (noticeId: string) => {
    setNotices(prev => prev.map(notice => 
      notice.id === noticeId 
        ? { ...notice, isActive: !notice.isActive }
        : notice
    ));
  };

  const togglePinStatus = (noticeId: string) => {
    setNotices(prev => prev.map(notice => 
      notice.id === noticeId 
        ? { ...notice, isPinned: !notice.isPinned }
        : notice
    ));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getReadPercentage = (readCount: number, totalMembers: number) => {
    return Math.round((readCount / totalMembers) * 100);
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Notice Management</h1>
          <p className="text-muted-foreground">
            Create and manage notices for your hostel members
          </p>
        </div>
        <Dialog open={isNewNoticeOpen} onOpenChange={setIsNewNoticeOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => {
              setIsEditMode(false);
              setSelectedNotice(null);
            }}>
              <Plus className="mr-2 h-4 w-4" />
              Create Notice
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {isEditMode ? 'Edit Notice' : 'Create New Notice'}
              </DialogTitle>
              <DialogDescription>
                {isEditMode 
                  ? 'Update the notice details below.'
                  : 'Create a new notice to inform your hostel members.'
                }
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  value={newNotice.title}
                  onChange={(e) => setNewNotice({...newNotice, title: e.target.value})}
                  placeholder="Enter notice title"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="type">Type</Label>
                  <Select value={newNotice.type} onValueChange={(value: Notice['type']) => setNewNotice({...newNotice, type: value})}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="general">General</SelectItem>
                      <SelectItem value="urgent">Urgent</SelectItem>
                      <SelectItem value="maintenance">Maintenance</SelectItem>
                      <SelectItem value="events">Events</SelectItem>
                      <SelectItem value="food">Food</SelectItem>
                      <SelectItem value="rules">Rules</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="priority">Priority</Label>
                  <Select value={newNotice.priority} onValueChange={(value: Notice['priority']) => setNewNotice({...newNotice, priority: value})}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="urgent">Urgent</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="content">Content</Label>
                <Textarea
                  id="content"
                  value={newNotice.content}
                  onChange={(e) => setNewNotice({...newNotice, content: e.target.value})}
                  placeholder="Enter notice content"
                  rows={4}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="expiryDate">Expiry Date (Optional)</Label>
                  <Input
                    id="expiryDate"
                    type="datetime-local"
                    value={newNotice.expiryDate}
                    onChange={(e) => setNewNotice({...newNotice, expiryDate: e.target.value})}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="targetAudience">Target Audience</Label>
                  <Select value={newNotice.targetAudience} onValueChange={(value: Notice['targetAudience']) => setNewNotice({...newNotice, targetAudience: value})}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Members</SelectItem>
                      <SelectItem value="specific_rooms">Specific Rooms</SelectItem>
                      <SelectItem value="specific_floors">Specific Floors</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="isPinned"
                  checked={newNotice.isPinned}
                  onChange={(e) => setNewNotice({...newNotice, isPinned: e.target.checked})}
                  className="rounded"
                />
                <Label htmlFor="isPinned">Pin this notice to the top</Label>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsNewNoticeOpen(false)}>
                Cancel
              </Button>
              <Button onClick={isEditMode ? handleUpdateNotice : handleCreateNotice}>
                <Send className="mr-2 h-4 w-4" />
                {isEditMode ? 'Update Notice' : 'Post Notice'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Notices</CardTitle>
            <Bell className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{notices.length}</div>
            <p className="text-xs text-muted-foreground">
              {notices.filter(n => n.isActive).length} active
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pinned Notices</CardTitle>
            <Pin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{notices.filter(n => n.isPinned).length}</div>
            <p className="text-xs text-muted-foreground">
              High priority notices
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Read Rate</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(notices.reduce((acc, n) => acc + getReadPercentage(n.readCount, n.totalMembers), 0) / notices.length)}%
            </div>
            <p className="text-xs text-muted-foreground">
              Member engagement
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Members</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">75</div>
            <p className="text-xs text-muted-foreground">
              Registered members
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search notices..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Select value={typeFilter} onValueChange={(value: any) => setTypeFilter(value)}>
              <SelectTrigger className="w-full md:w-[180px]">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="general">General</SelectItem>
                <SelectItem value="urgent">Urgent</SelectItem>
                <SelectItem value="maintenance">Maintenance</SelectItem>
                <SelectItem value="events">Events</SelectItem>
                <SelectItem value="food">Food</SelectItem>
                <SelectItem value="rules">Rules</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Notices Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Notices</CardTitle>
          <CardDescription>
            Manage all notices posted to your hostel members
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Notice</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead>Posted</TableHead>
                  <TableHead>Read Rate</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredNotices.map((notice) => (
                  <TableRow key={notice.id}>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        {notice.isPinned && <Pin className="h-3 w-3 text-orange-500" />}
                        <div>
                          <div className="font-medium">{notice.title}</div>
                          <div className="text-sm text-muted-foreground truncate max-w-xs">
                            {notice.content}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getTypeColor(notice.type)}>
                        {getTypeIcon(notice.type)}
                        <span className="ml-1 capitalize">{notice.type}</span>
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={getPriorityColor(notice.priority)}>
                        {notice.priority.charAt(0).toUpperCase() + notice.priority.slice(1)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {formatDate(notice.postedDate)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <div className="text-sm font-medium">
                          {getReadPercentage(notice.readCount, notice.totalMembers)}%
                        </div>
                        <div className="text-xs text-muted-foreground">
                          ({notice.readCount}/{notice.totalMembers})
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={notice.isActive ? 'default' : 'secondary'}>
                        {notice.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => handleEditNotice(notice)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => togglePinStatus(notice.id)}>
                            <Pin className="mr-2 h-4 w-4" />
                            {notice.isPinned ? 'Unpin' : 'Pin'}
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => toggleNoticeStatus(notice.id)}>
                            {notice.isActive ? <Archive className="mr-2 h-4 w-4" /> : <Eye className="mr-2 h-4 w-4" />}
                            {notice.isActive ? 'Deactivate' : 'Activate'}
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => handleDeleteNotice(notice.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
