import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Building2,
  Users,
  Shield,
  CreditCard,
  MessageSquare,
  Star,
  ArrowRight,
  CheckCircle
} from 'lucide-react';
import { Link } from 'react-router-dom';
import heroImage from '@/assets/hero-hostel.jpg';

interface LandingPageProps {
  onPanelChange?: (panel: 'superadmin' | 'owner' | 'employee' | 'user') => void;
}

export const LandingPage = ({ onPanelChange }: LandingPageProps) => {
  const features = [
    {
      icon: Building2,
      title: 'Multi-Platform Management',
      description: 'Comprehensive hostel management across web and mobile platforms with role-based access.'
    },
    {
      icon: Users,
      title: 'Role-Based Access Control',
      description: 'Secure access management for Super Admins, Hostel Owners, Employees, and Members.'
    },
    {
      icon: Shield,
      title: 'Secure & Reliable',
      description: 'Enterprise-grade security with robust data protection and reliable uptime.'
    },
    {
      icon: CreditCard,
      title: 'Payment Processing',
      description: 'Integrated payment system with split billing and multiple payment methods.'
    },
    {
      icon: MessageSquare,
      title: 'Communication Hub',
      description: 'Built-in messaging system with WhatsApp integration and push notifications.'
    },
    {
      icon: Star,
      title: 'Quality Management',
      description: 'Complaint management system with priority handling and resolution tracking.'
    }
  ];

  const stats = [
    { label: 'Active Hostels', value: '500+' },
    { label: 'Happy Members', value: '10K+' },
    { label: 'Cities Covered', value: '50+' },
    { label: 'Uptime', value: '99.9%' }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32 bg-gradient-hero overflow-hidden">
        <div className="absolute inset-0 bg-black/20" />
        <div 
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-20"
          style={{ backgroundImage: `url(${heroImage})` }}
        />
        
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center text-white">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              Modern Hostel Management
              <span className="block bg-gradient-accent bg-clip-text text-transparent">
                Made Simple
              </span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-white/90 max-w-3xl mx-auto">
              Comprehensive multi-platform solution for hostel management with role-based access, 
              payment processing, and seamless communication.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/login">
                <Button
                  size="lg"
                  className="bg-white text-primary hover:bg-white/90 shadow-glow"
                >
                  Find Hostels
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link to="/login">
                <Button
                  size="lg"
                  variant="outline"
                  className="border-white text-white hover:bg-white hover:text-primary"
                >
                  List Your Hostel
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-primary mb-2">
                  {stat.value}
                </div>
                <div className="text-muted-foreground">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Everything You Need to Manage Hostels
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Our comprehensive platform provides all the tools you need for efficient hostel management.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <Card key={index} className="group hover:shadow-elevation transition-all duration-300">
                  <CardHeader>
                    <div className="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center mb-4 group-hover:shadow-glow transition-all duration-300">
                      <Icon className="h-6 w-6 text-white" />
                    </div>
                    <CardTitle className="text-xl">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">{feature.description}</p>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Panels Section */}
      <section className="py-20 bg-background">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Access Your Dashboard
            </h2>
            <p className="text-xl text-muted-foreground">
              Choose your role to access the appropriate management panel
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="hover:shadow-elevation transition-all duration-300 group">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4 group-hover:shadow-glow transition-all duration-300">
                  <Users className="h-8 w-8 text-white" />
                </div>
                <CardTitle>Super Admin</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-muted-foreground mb-4">
                  System-wide management and analytics
                </p>
                <Link to="/login">
                  <Button variant="outline" className="w-full">
                    Access Panel
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card className="hover:shadow-elevation transition-all duration-300 group">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-gradient-accent rounded-full flex items-center justify-center mx-auto mb-4 group-hover:shadow-glow transition-all duration-300">
                  <Building2 className="h-8 w-8 text-white" />
                </div>
                <CardTitle>Hostel Provider</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-muted-foreground mb-4">
                  Manage your hostels and employees
                </p>
                <div className="space-y-2">
                  <Link to="/login">
                    <Button variant="outline" className="w-full">
                      Access Panel
                    </Button>
                  </Link>
                  <Link to="/request-account">
                    <Button variant="ghost" className="w-full text-sm">
                      Request Account Access
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-elevation transition-all duration-300 group">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4 group-hover:shadow-glow transition-all duration-300">
                  <Users className="h-8 w-8 text-white" />
                </div>
                <CardTitle>Employee</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-muted-foreground mb-4">
                  Daily operations and member management
                </p>
                <Link to="/login">
                  <Button variant="outline" className="w-full">
                    Access Panel
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card className="hover:shadow-elevation transition-all duration-300 group">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-gradient-accent rounded-full flex items-center justify-center mx-auto mb-4 group-hover:shadow-glow transition-all duration-300">
                  <Users className="h-8 w-8 text-white" />
                </div>
                <CardTitle>User Panel</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-muted-foreground mb-4">
                  Find and book hostel accommodations
                </p>
                <Link to="/login">
                  <Button variant="outline" className="w-full">
                    Access Panel
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 bg-muted/50 border-t">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <Building2 className="h-6 w-6 text-primary" />
              <span className="text-lg font-semibold">HostelHub</span>
            </div>
            <div className="text-muted-foreground text-sm">
              © 2024 HostelHub. All rights reserved.
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};