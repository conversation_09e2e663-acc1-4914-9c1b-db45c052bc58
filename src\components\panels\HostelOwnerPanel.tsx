import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Building2, 
  Users, 
  Bed, 
  DollarSign,
  UserPlus,
  Settings,
  Eye,
  Plus
} from 'lucide-react';
import { mockHostels, mockUsers, mockBookings, mockPayments, getBookingsByUserId } from '@/data/mockData';

export const HostelOwnerPanel = () => {
  // Assuming current user is hostel owner with ID '2' (<PERSON><PERSON>)
  const currentOwnerId = '2';
  const ownedHostels = mockHostels.filter(h => h.ownerId === currentOwnerId);
  const employees = mockUsers.filter(u => u.role === 'employee' && ownedHostels.some(h => h.id === u.hostelId));
  
  // Calculate revenue for owned hostels
  const hostelBookings = mockBookings.filter(b => ownedHostels.some(h => h.id === b.hostelId));
  const totalRevenue = mockPayments
    .filter(p => hostelBookings.some(b => b.id === p.bookingId))
    .reduce((sum, p) => sum + p.splitDetails.hostelOwner, 0);

  const stats = [
    {
      title: 'Total Hostels',
      value: ownedHostels.length,
      icon: Building2,
      color: 'text-blue-600'
    },
    {
      title: 'Total Beds',
      value: ownedHostels.reduce((sum, h) => sum + h.totalBeds, 0),
      icon: Bed,
      color: 'text-green-600'
    },
    {
      title: 'Occupancy Rate',
      value: '75%',
      icon: Users,
      color: 'text-purple-600'
    },
    {
      title: 'Monthly Revenue',
      value: `₹${(totalRevenue / 1000).toFixed(0)}K`,
      icon: DollarSign,
      color: 'text-orange-600'
    }
  ];

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Hostel Provider Dashboard</h1>
          <p className="text-muted-foreground">Manage your hostels and employees</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </Button>
          <Button className="bg-gradient-primary text-primary-foreground shadow-elevation">
            <Plus className="mr-2 h-4 w-4" />
            Add Hostel
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index} className="hover:shadow-elevation transition-all duration-300">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                <Icon className={`h-4 w-4 ${stat.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Main Content */}
      <div className="grid lg:grid-cols-2 gap-6">
        {/* My Hostels */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                My Hostels
              </span>
              <Button size="sm" variant="outline">
                <Plus className="h-4 w-4 mr-1" />
                Add New
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {ownedHostels.map((hostel) => (
                <div key={hostel.id} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h3 className="font-semibold">{hostel.name}</h3>
                      <p className="text-sm text-muted-foreground">
                        {hostel.address}, {hostel.city}
                      </p>
                    </div>
                    <Badge variant={hostel.status === 'active' ? 'default' : 'secondary'}>
                      {hostel.status}
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm mb-3">
                    <div>
                      <span className="text-muted-foreground">Total Beds:</span>
                      <span className="ml-2 font-medium">{hostel.totalBeds}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Available:</span>
                      <span className="ml-2 font-medium text-green-600">{hostel.availableBeds}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Price:</span>
                      <span className="ml-2 font-medium">₹{hostel.pricePerBed}/month</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Rating:</span>
                      <span className="ml-2 font-medium">⭐ {hostel.rating}</span>
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" className="flex-1">
                      <Eye className="h-4 w-4 mr-1" />
                      View Details
                    </Button>
                    <Button size="sm" variant="outline" className="flex-1">
                      <Settings className="h-4 w-4 mr-1" />
                      Manage
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Employees */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Employees
              </span>
              <Button size="sm" variant="outline">
                <UserPlus className="h-4 w-4 mr-1" />
                Add Employee
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {employees.map((employee) => {
                const hostel = ownedHostels.find(h => h.id === employee.hostelId);
                return (
                  <div key={employee.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium">{employee.name}</div>
                      <div className="text-sm text-muted-foreground">{employee.email}</div>
                      <div className="text-sm text-muted-foreground">
                        {hostel?.name}
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge variant={employee.status === 'active' ? 'default' : 'secondary'}>
                        {employee.status}
                      </Badge>
                      <div className="text-sm text-muted-foreground mt-1">
                        Joined: {new Date(employee.joinedDate).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Revenue Analytics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Revenue Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-3 gap-6">
            <div className="p-4 border rounded-lg">
              <div className="text-2xl font-bold text-green-600">₹{(totalRevenue / 1000).toFixed(0)}K</div>
              <div className="text-sm text-muted-foreground">Total Revenue</div>
            </div>
            <div className="p-4 border rounded-lg">
              <div className="text-2xl font-bold text-blue-600">₹{(totalRevenue * 0.8 / 1000).toFixed(0)}K</div>
              <div className="text-sm text-muted-foreground">Net Income (80%)</div>
            </div>
            <div className="p-4 border rounded-lg">
              <div className="text-2xl font-bold text-purple-600">₹{(totalRevenue / hostelBookings.length / 1000).toFixed(1)}K</div>
              <div className="text-sm text-muted-foreground">Avg. Booking Value</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};