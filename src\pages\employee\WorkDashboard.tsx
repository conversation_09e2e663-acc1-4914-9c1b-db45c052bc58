import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { 
  Clock, 
  CheckCircle,
  AlertTriangle,
  Calendar,
  Users,
  MessageSquare,
  Bell,
  Play,
  Pause,
  Square,
  Target,
  TrendingUp,
  Award,
  Coffee,
  Wrench,
  ClipboardList,
  UserCheck,
  Building2,
  ArrowUpRight,
  ArrowDownRight,
  Timer,
  MapPin,
  Phone
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

// Mock employee data and tasks
interface Task {
  id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  status: 'pending' | 'in_progress' | 'completed';
  dueDate: string;
  category: 'maintenance' | 'guest_service' | 'cleaning' | 'admin';
  assignedBy: string;
  estimatedTime: number; // in minutes
}

const mockTasks: Task[] = [
  {
    id: '1',
    title: 'Fix AC in Room 201',
    description: 'Air conditioning unit not cooling properly',
    priority: 'high',
    status: 'pending',
    dueDate: '2024-01-25T14:00:00Z',
    category: 'maintenance',
    assignedBy: 'Manager',
    estimatedTime: 60
  },
  {
    id: '2',
    title: 'Guest Check-in Assistance',
    description: 'Help new guest with check-in process and room orientation',
    priority: 'medium',
    status: 'in_progress',
    dueDate: '2024-01-25T11:00:00Z',
    category: 'guest_service',
    assignedBy: 'Front Desk',
    estimatedTime: 30
  },
  {
    id: '3',
    title: 'Clean Common Area',
    description: 'Deep clean the common lounge area',
    priority: 'medium',
    status: 'completed',
    dueDate: '2024-01-25T09:00:00Z',
    category: 'cleaning',
    assignedBy: 'Supervisor',
    estimatedTime: 90
  },
  {
    id: '4',
    title: 'Update Inventory Records',
    description: 'Update cleaning supplies inventory in the system',
    priority: 'low',
    status: 'pending',
    dueDate: '2024-01-25T16:00:00Z',
    category: 'admin',
    assignedBy: 'Manager',
    estimatedTime: 45
  }
];

// Mock announcements
interface Announcement {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'success';
  date: string;
  author: string;
}

const mockAnnouncements: Announcement[] = [
  {
    id: '1',
    title: 'New Safety Protocols',
    message: 'Please review the updated safety protocols in the staff handbook.',
    type: 'warning',
    date: '2024-01-24T10:00:00Z',
    author: 'Management'
  },
  {
    id: '2',
    title: 'Staff Meeting Tomorrow',
    message: 'Monthly staff meeting scheduled for tomorrow at 2 PM in the conference room.',
    type: 'info',
    date: '2024-01-24T08:30:00Z',
    author: 'HR Department'
  },
  {
    id: '3',
    title: 'Great Job This Week!',
    message: 'Excellent work on maintaining high guest satisfaction scores.',
    type: 'success',
    date: '2024-01-23T16:00:00Z',
    author: 'Manager'
  }
];

export const WorkDashboard: React.FC = () => {
  const { user } = useAuth();
  const [isClockingIn, setIsClockingIn] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [workStatus, setWorkStatus] = useState<'clocked_out' | 'clocked_in' | 'on_break'>('clocked_out');

  // Mock work session data
  const todayHours = 6.5;
  const weeklyHours = 32.5;
  const targetWeeklyHours = 40;
  const currentShift = {
    start: '09:00 AM',
    end: '06:00 PM',
    break: '01:00 PM - 02:00 PM'
  };

  // Calculate task statistics
  const totalTasks = mockTasks.length;
  const completedTasks = mockTasks.filter(t => t.status === 'completed').length;
  const pendingTasks = mockTasks.filter(t => t.status === 'pending').length;
  const inProgressTasks = mockTasks.filter(t => t.status === 'in_progress').length;
  const highPriorityTasks = mockTasks.filter(t => t.priority === 'high').length;

  const workStats = [
    {
      title: 'Today\'s Hours',
      value: `${todayHours}h`,
      change: '+1.5h',
      trend: 'up',
      icon: Clock,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Tasks Completed',
      value: `${completedTasks}/${totalTasks}`,
      change: '+2',
      trend: 'up',
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Pending Tasks',
      value: pendingTasks.toString(),
      change: '-1',
      trend: 'down',
      icon: ClipboardList,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
    {
      title: 'High Priority',
      value: highPriorityTasks.toString(),
      change: '0',
      trend: 'neutral',
      icon: AlertTriangle,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
    },
  ];

  const getTaskPriorityBadge = (priority: string) => {
    const variants = {
      low: 'bg-green-100 text-green-800',
      medium: 'bg-yellow-100 text-yellow-800',
      high: 'bg-red-100 text-red-800'
    };
    return (
      <Badge className={variants[priority as keyof typeof variants]}>
        {priority.charAt(0).toUpperCase() + priority.slice(1)}
      </Badge>
    );
  };

  const getTaskStatusBadge = (status: string) => {
    const variants = {
      pending: 'bg-gray-100 text-gray-800',
      in_progress: 'bg-blue-100 text-blue-800',
      completed: 'bg-green-100 text-green-800'
    };
    const icons = {
      pending: Clock,
      in_progress: Timer,
      completed: CheckCircle
    };
    const Icon = icons[status as keyof typeof icons] || Clock;
    return (
      <Badge className={`${variants[status as keyof typeof variants]} flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {status.replace('_', ' ').charAt(0).toUpperCase() + status.replace('_', ' ').slice(1)}
      </Badge>
    );
  };

  const getTaskCategoryIcon = (category: string) => {
    const icons = {
      maintenance: Wrench,
      guest_service: UserCheck,
      cleaning: Coffee,
      admin: ClipboardList
    };
    return icons[category as keyof typeof icons] || ClipboardList;
  };

  const getAnnouncementIcon = (type: string) => {
    const icons = {
      info: MessageSquare,
      warning: AlertTriangle,
      success: CheckCircle
    };
    return icons[type as keyof typeof icons] || MessageSquare;
  };

  const getAnnouncementColor = (type: string) => {
    const colors = {
      info: 'text-blue-600 bg-blue-50',
      warning: 'text-yellow-600 bg-yellow-50',
      success: 'text-green-600 bg-green-50'
    };
    return colors[type as keyof typeof colors] || 'text-gray-600 bg-gray-50';
  };

  const handleClockAction = () => {
    if (workStatus === 'clocked_out') {
      setWorkStatus('clocked_in');
    } else if (workStatus === 'clocked_in') {
      setWorkStatus('on_break');
    } else {
      setWorkStatus('clocked_out');
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    });
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Work Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome back! Here's your work overview for today
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="text-right">
            <div className="text-sm text-muted-foreground">Current Time</div>
            <div className="text-lg font-semibold">{formatTime(currentTime)}</div>
          </div>
          <Button 
            className={`${
              workStatus === 'clocked_out' ? 'bg-green-600 hover:bg-green-700' :
              workStatus === 'clocked_in' ? 'bg-yellow-600 hover:bg-yellow-700' :
              'bg-red-600 hover:bg-red-700'
            }`}
            onClick={handleClockAction}
          >
            {workStatus === 'clocked_out' ? (
              <>
                <Play className="mr-2 h-4 w-4" />
                Clock In
              </>
            ) : workStatus === 'clocked_in' ? (
              <>
                <Pause className="mr-2 h-4 w-4" />
                Take Break
              </>
            ) : (
              <>
                <Square className="mr-2 h-4 w-4" />
                Clock Out
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Work Status Card */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Avatar className="h-16 w-16">
                <AvatarImage src={user?.avatar} alt={user?.name} />
                <AvatarFallback className="text-lg">
                  {user?.name?.split(' ').map(n => n[0]).join('').toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div>
                <h2 className="text-2xl font-bold">{user?.name}</h2>
                <p className="text-muted-foreground">Maintenance Staff</p>
                <div className="flex items-center space-x-4 mt-2">
                  <Badge className={`${
                    workStatus === 'clocked_in' ? 'bg-green-100 text-green-800' :
                    workStatus === 'on_break' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {workStatus === 'clocked_in' ? 'On Duty' :
                     workStatus === 'on_break' ? 'On Break' : 'Off Duty'}
                  </Badge>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Building2 className="mr-1 h-4 w-4" />
                    Urban Stay Hostel
                  </div>
                </div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm text-muted-foreground mb-1">Today's Shift</div>
              <div className="font-semibold">{currentShift.start} - {currentShift.end}</div>
              <div className="text-sm text-muted-foreground">Break: {currentShift.break}</div>
              <div className="mt-2">
                <div className="text-sm text-muted-foreground">Weekly Progress</div>
                <Progress value={(weeklyHours / targetWeeklyHours) * 100} className="w-32 mt-1" />
                <div className="text-xs text-muted-foreground mt-1">
                  {weeklyHours}h / {targetWeeklyHours}h
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Work Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {workStats.map((stat, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                {stat.trend === 'up' ? (
                  <ArrowUpRight className="mr-1 h-3 w-3 text-green-500" />
                ) : stat.trend === 'down' ? (
                  <ArrowDownRight className="mr-1 h-3 w-3 text-red-500" />
                ) : null}
                <span className={
                  stat.trend === 'up' ? 'text-green-600' : 
                  stat.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                }>
                  {stat.change}
                </span>
                <span className="ml-1">from yesterday</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Today's Tasks */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Today's Tasks</CardTitle>
              <CardDescription>
                Your assigned tasks for today
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockTasks.map((task) => {
                  const CategoryIcon = getTaskCategoryIcon(task.category);
                  return (
                    <div key={task.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                      <div className="p-2 bg-gray-100 rounded-lg">
                        <CategoryIcon className="h-5 w-5 text-gray-600" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <h4 className="font-semibold">{task.title}</h4>
                          <div className="flex items-center space-x-2">
                            {getTaskPriorityBadge(task.priority)}
                            {getTaskStatusBadge(task.status)}
                          </div>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">
                          {task.description}
                        </p>
                        <div className="flex items-center justify-between text-sm text-muted-foreground">
                          <div className="flex items-center space-x-4">
                            <div className="flex items-center">
                              <Clock className="mr-1 h-4 w-4" />
                              Due: {new Date(task.dueDate).toLocaleTimeString('en-US', { 
                                hour: '2-digit', 
                                minute: '2-digit' 
                              })}
                            </div>
                            <div className="flex items-center">
                              <Timer className="mr-1 h-4 w-4" />
                              Est: {task.estimatedTime}min
                            </div>
                          </div>
                          <div>Assigned by: {task.assignedBy}</div>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        {task.status === 'pending' && (
                          <Button size="sm">
                            <Play className="mr-2 h-4 w-4" />
                            Start
                          </Button>
                        )}
                        {task.status === 'in_progress' && (
                          <Button size="sm" variant="outline">
                            <CheckCircle className="mr-2 h-4 w-4" />
                            Complete
                          </Button>
                        )}
                        {task.status === 'completed' && (
                          <Badge className="bg-green-100 text-green-800">
                            <CheckCircle className="mr-1 h-3 w-3" />
                            Done
                          </Badge>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Announcements & Quick Actions */}
        <div className="space-y-6">
          {/* Announcements */}
          <Card>
            <CardHeader>
              <CardTitle>Announcements</CardTitle>
              <CardDescription>
                Latest updates from management
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockAnnouncements.map((announcement) => {
                  const Icon = getAnnouncementIcon(announcement.type);
                  const colorClass = getAnnouncementColor(announcement.type);
                  return (
                    <div key={announcement.id} className="flex items-start space-x-3">
                      <div className={`p-2 rounded-lg ${colorClass}`}>
                        <Icon className="h-4 w-4" />
                      </div>
                      <div className="flex-1">
                        <div className="font-medium text-sm">{announcement.title}</div>
                        <p className="text-sm text-muted-foreground mt-1">
                          {announcement.message}
                        </p>
                        <div className="text-xs text-muted-foreground mt-2">
                          {announcement.author} • {new Date(announcement.date).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full justify-start" variant="outline">
                <Wrench className="mr-2 h-4 w-4" />
                Report Maintenance Issue
              </Button>
              
              <Button className="w-full justify-start" variant="outline">
                <UserCheck className="mr-2 h-4 w-4" />
                Guest Request
              </Button>
              
              <Button className="w-full justify-start" variant="outline">
                <MessageSquare className="mr-2 h-4 w-4" />
                Team Chat
              </Button>
              
              <Button className="w-full justify-start" variant="outline">
                <Phone className="mr-2 h-4 w-4" />
                Emergency Contact
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
