import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Building,
  Layers,
  Users,
  Bed,
  DollarSign,
  TrendingUp,
  Eye,
  BarChart3
} from 'lucide-react';
import { FloorStatistics as FloorStatsType, HostelStatistics } from '@/types/roomManagement';
import { getFloorStatisticsService, getHostelStatisticsService } from '@/services/floorManagementService';
import { toast } from '@/hooks/use-toast';

interface FloorStatisticsProps {
  hostelId: string;
  selectedFloors?: string[];
  className?: string;
}

export const FloorStatistics: React.FC<FloorStatisticsProps> = ({
  hostelId,
  selectedFloors = [],
  className = ''
}) => {
  const [hostelStats, setHostelStats] = useState<HostelStatistics | null>(null);
  const [selectedFloorStats, setSelectedFloorStats] = useState<FloorStatsType | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isFloorDetailsOpen, setIsFloorDetailsOpen] = useState(false);

  useEffect(() => {
    if (hostelId) {
      loadHostelStatistics();
    }
  }, [hostelId]);

  const loadHostelStatistics = async () => {
    setIsLoading(true);
    try {
      const result = await getHostelStatisticsService(hostelId);
      if (result.success) {
        setHostelStats(result.data);
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to load statistics",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error loading hostel statistics:', error);
      toast({
        title: "Error",
        description: "Failed to load statistics",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewFloorDetails = async (floorId: string) => {
    try {
      const result = await getFloorStatisticsService(floorId);
      if (result.success) {
        setSelectedFloorStats(result.data);
        setIsFloorDetailsOpen(true);
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to load floor details",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error loading floor statistics:', error);
      toast({
        title: "Error",
        description: "Failed to load floor details",
        variant: "destructive"
      });
    }
  };

  const getOccupancyColor = (rate: number) => {
    if (rate >= 80) return 'text-green-600';
    if (rate >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getOccupancyBadge = (rate: number) => {
    if (rate >= 80) return <Badge className="bg-green-100 text-green-800">High</Badge>;
    if (rate >= 60) return <Badge className="bg-yellow-100 text-yellow-800">Medium</Badge>;
    return <Badge className="bg-red-100 text-red-800">Low</Badge>;
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2 text-sm text-muted-foreground">Loading statistics...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!hostelStats) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <BarChart3 className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No statistics available</h3>
            <p className="mt-1 text-sm text-gray-500">
              Unable to load hostel statistics.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Filter floor statistics based on selected floors
  const displayedFloorStats = selectedFloors.length > 0 
    ? hostelStats.floorStatistics.filter(stat => selectedFloors.includes(stat.floorId))
    : hostelStats.floorStatistics;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Overall Hostel Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building className="h-5 w-5" />
            Hostel Overview
          </CardTitle>
          <CardDescription>
            Overall statistics for the selected hostel
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{hostelStats.totalFloors}</div>
              <div className="text-sm text-blue-600">Total Floors</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{hostelStats.totalRooms}</div>
              <div className="text-sm text-green-600">Total Rooms</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{hostelStats.totalBeds}</div>
              <div className="text-sm text-purple-600">Total Beds</div>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className={`text-2xl font-bold ${getOccupancyColor(hostelStats.occupancyRate)}`}>
                {hostelStats.occupancyRate.toFixed(1)}%
              </div>
              <div className="text-sm text-orange-600">Occupancy Rate</div>
            </div>
          </div>

          {/* Revenue Statistics */}
          <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 border rounded-lg">
              <div className="text-xl font-bold">₹{hostelStats.revenueStats.dailyRevenue.toLocaleString()}</div>
              <div className="text-sm text-muted-foreground">Daily Revenue</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-xl font-bold">₹{hostelStats.revenueStats.monthlyRevenue.toLocaleString()}</div>
              <div className="text-sm text-muted-foreground">Monthly Revenue</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-xl font-bold">₹{hostelStats.revenueStats.averageRatePerBed.toFixed(0)}</div>
              <div className="text-sm text-muted-foreground">Avg Rate/Bed</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Floor-wise Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Layers className="h-5 w-5" />
            Floor-wise Statistics
            {selectedFloors.length > 0 && (
              <Badge variant="outline">
                {selectedFloors.length} floor(s) selected
              </Badge>
            )}
          </CardTitle>
          <CardDescription>
            Detailed statistics for each floor
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Floor</TableHead>
                  <TableHead>Rooms</TableHead>
                  <TableHead>Beds</TableHead>
                  <TableHead>Occupied</TableHead>
                  <TableHead>Occupancy</TableHead>
                  <TableHead>Daily Revenue</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {displayedFloorStats.map((floorStat) => (
                  <TableRow key={floorStat.floorId}>
                    <TableCell className="font-medium">
                      Floor {floorStat.floorNumber}
                    </TableCell>
                    <TableCell>{floorStat.totalRooms}</TableCell>
                    <TableCell>{floorStat.totalBeds}</TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <span>{floorStat.occupiedBeds}</span>
                        <div className="w-16 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full" 
                            style={{ width: `${floorStat.occupancyRate}%` }}
                          ></div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <span className={getOccupancyColor(floorStat.occupancyRate)}>
                          {floorStat.occupancyRate.toFixed(1)}%
                        </span>
                        {getOccupancyBadge(floorStat.occupancyRate)}
                      </div>
                    </TableCell>
                    <TableCell>₹{floorStat.revenueStats.dailyRevenue.toLocaleString()}</TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewFloorDetails(floorStat.floorId)}
                      >
                        <Eye className="mr-2 h-4 w-4" />
                        Details
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {displayedFloorStats.length === 0 && (
            <div className="text-center py-8">
              <Layers className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No floor data</h3>
              <p className="mt-1 text-sm text-gray-500">
                {selectedFloors.length > 0 
                  ? "No statistics available for selected floors."
                  : "No floor statistics available."
                }
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Floor Details Dialog */}
      <Dialog open={isFloorDetailsOpen} onOpenChange={setIsFloorDetailsOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Layers className="h-5 w-5" />
              Floor {selectedFloorStats?.floorNumber} Details
            </DialogTitle>
            <DialogDescription>
              Comprehensive statistics and information for this floor
            </DialogDescription>
          </DialogHeader>

          {selectedFloorStats && (
            <div className="space-y-6">
              {/* Overview Stats */}
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-xl font-bold text-blue-600">{selectedFloorStats.totalRooms}</div>
                  <div className="text-sm text-blue-600">Total Rooms</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-xl font-bold text-green-600">{selectedFloorStats.totalBeds}</div>
                  <div className="text-sm text-green-600">Total Beds</div>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-xl font-bold text-purple-600">{selectedFloorStats.occupiedBeds}</div>
                  <div className="text-sm text-purple-600">Occupied Beds</div>
                </div>
                <div className="text-center p-4 bg-orange-50 rounded-lg">
                  <div className={`text-xl font-bold ${getOccupancyColor(selectedFloorStats.occupancyRate)}`}>
                    {selectedFloorStats.occupancyRate.toFixed(1)}%
                  </div>
                  <div className="text-sm text-orange-600">Occupancy Rate</div>
                </div>
              </div>

              {/* Bed Status Breakdown */}
              <div>
                <h4 className="font-semibold mb-3">Bed Status Breakdown</h4>
                <div className="grid grid-cols-4 gap-2">
                  <div className="text-center p-2 bg-green-50 rounded">
                    <div className="font-bold text-green-600">{selectedFloorStats.availableBeds}</div>
                    <div className="text-xs text-green-600">Available</div>
                  </div>
                  <div className="text-center p-2 bg-blue-50 rounded">
                    <div className="font-bold text-blue-600">{selectedFloorStats.occupiedBeds}</div>
                    <div className="text-xs text-blue-600">Occupied</div>
                  </div>
                  <div className="text-center p-2 bg-yellow-50 rounded">
                    <div className="font-bold text-yellow-600">{selectedFloorStats.maintenanceBeds}</div>
                    <div className="text-xs text-yellow-600">Maintenance</div>
                  </div>
                  <div className="text-center p-2 bg-purple-50 rounded">
                    <div className="font-bold text-purple-600">{selectedFloorStats.reservedBeds}</div>
                    <div className="text-xs text-purple-600">Reserved</div>
                  </div>
                </div>
              </div>

              {/* Revenue Information */}
              <div>
                <h4 className="font-semibold mb-3">Revenue Statistics</h4>
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center p-3 border rounded-lg">
                    <div className="font-bold">₹{selectedFloorStats.revenueStats.dailyRevenue.toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground">Daily Revenue</div>
                  </div>
                  <div className="text-center p-3 border rounded-lg">
                    <div className="font-bold">₹{selectedFloorStats.revenueStats.monthlyRevenue.toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground">Monthly Revenue</div>
                  </div>
                  <div className="text-center p-3 border rounded-lg">
                    <div className="font-bold">₹{selectedFloorStats.revenueStats.averageRatePerBed.toFixed(0)}</div>
                    <div className="text-sm text-muted-foreground">Avg Rate/Bed</div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};
