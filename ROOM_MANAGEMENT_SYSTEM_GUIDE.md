# Room and Bed Management System - User Guide

## Overview

The Room and Bed Management System allows hostel owners to efficiently manage their room configurations, add new rooms and beds, and track occupancy. The system includes approval workflows for major changes and provides comprehensive analytics.

## Features

### 1. Room Management Dashboard
- **Overview Statistics**: Total rooms, beds, occupancy rates, and revenue metrics
- **Room Listing**: Complete view of all rooms with status, occupancy, and pricing
- **Search and Filter**: Find rooms by number, type, status, or other criteria
- **Quick Actions**: Add rooms, manage beds, view details, and update settings

### 2. Add New Rooms
- **Multi-step Form**: Guided process for adding new rooms
- **Bed Configuration**: Define bed types, sizes, and locker assignments
- **Pricing Setup**: Set daily, weekly, and monthly rates with deposits
- **Feature Selection**: Configure amenities and room features
- **Approval Workflow**: Automatic submission for admin approval

### 3. Bed Management
- **Add Beds**: Add individual beds to existing rooms
- **Status Management**: Update bed availability, maintenance, and occupancy
- **Locker Assignment**: Manage locker numbers and assignments
- **Capacity Control**: Automatic validation for room capacity limits

### 4. Approval System
- **Smart Approval**: Minor changes applied immediately, major changes require approval
- **Request Tracking**: Monitor status of pending requests
- **Admin Review**: Super admins review and approve/reject requests
- **Notification System**: Email and in-app notifications for status updates

## Getting Started

### Accessing Room Management

1. **Login** to your hostel owner account
2. **Navigate** to the owner dashboard
3. **Click** on "Room Management" in the sidebar menu
4. **View** your current room statistics and listings

### Understanding the Dashboard

The main dashboard provides:

- **Statistics Cards**: Key metrics at a glance
  - Total Rooms: Number of active rooms
  - Total Beds: Available and occupied bed count
  - Occupancy Rate: Current occupancy percentage
  - Monthly Revenue: Estimated monthly income

- **Room Table**: Detailed view of all rooms
  - Room Number and Type
  - Floor and Capacity
  - Current Occupancy with visual indicator
  - Daily Rate and Status
  - Action menu for management options

## Adding New Rooms

### Step 1: Basic Information
1. Click **"Add Room"** button
2. Enter **Room Number** (e.g., 101, A1, etc.)
3. Select **Room Type** (single, double, triple, quad, dormitory, suite, studio, family)
4. Specify **Floor** number
5. Set **Bed Capacity**
6. Enter **Room Area** in square feet
7. Select **Amenities** from available options
8. Add optional **Description**

### Step 2: Bed Configuration
1. Review auto-generated bed configurations
2. Customize **Bed Numbers** (e.g., 101A, 101B)
3. Select **Bed Types** (single, double, bunk top/bottom, queen, king)
4. Choose **Bed Sizes**
5. Configure **Locker Assignments**
6. Add or remove beds as needed

### Step 3: Pricing Setup
1. Set **Daily Rate** per bed
2. Configure **Weekly Rate** (usually discounted)
3. Set **Monthly Rate** (best value option)
4. Define **Security Deposit** amount
5. Add optional **Cleaning Fee**
6. Provide **Reason** for adding the room
7. Estimate **Expected Occupancy Increase**

### Step 4: Features and Amenities
1. Select **Room Features**:
   - Window, Balcony, Attached Bathroom
   - AC, Fan, WiFi, TV, Refrigerator
   - Wardrobe, Study Table, Chair
2. Review all configurations
3. **Submit Request** for approval

### Approval Process

**Immediate Approval**: Simple room additions with standard configurations
**Admin Approval Required**: 
- Rooms with more than 6 beds
- Unusual configurations
- Significant pricing changes
- Major structural modifications

## Managing Existing Rooms

### Adding Beds to Rooms

1. **Select Room** from the room table
2. Click **"Add Beds"** from the action menu
3. **Configure New Beds**:
   - Bed numbers (auto-generated)
   - Bed types and sizes
   - Locker assignments
4. **Review Summary**:
   - Number of beds to add
   - Total capacity after addition
   - Approval status (immediate or pending)
5. **Submit Request**

**Approval Rules**:
- Adding 1-2 beds: Usually immediate
- Adding 3+ beds: Requires approval
- Total beds > 6: Requires approval

### Updating Bed Status

1. **View Room Details** by clicking on a room
2. Navigate to **"Beds"** tab
3. Use **Action Menu** for each bed:
   - Mark Available
   - Mark Occupied
   - Mark Under Maintenance
   - Mark Reserved

### Room Details View

Access comprehensive room information:

**Overview Tab**:
- Room information and status
- Occupancy metrics with visual indicators
- Amenities list
- Room description

**Beds Tab**:
- Complete bed listing
- Individual bed status
- Occupant information
- Locker assignments
- Quick status updates

**Pricing Tab**:
- Current rate structure
- Revenue calculations
- Full occupancy potential
- Current earnings

**Features Tab**:
- Room amenities checklist
- Special features
- Restrictions and policies

## Approval Workflow

### For Hostel Owners

1. **Submit Request**: Complete the form and submit
2. **Receive Confirmation**: Get request ID and confirmation
3. **Track Status**: Monitor request in the dashboard
4. **Get Notification**: Receive email when reviewed
5. **Implement Changes**: Approved changes are automatically applied

### Request Types and Approval Criteria

**Add Room Requests**:
- Always require admin approval
- Include complete room configuration
- Require business justification
- May include supporting documents

**Add Beds Requests**:
- 1-2 beds: Usually immediate
- 3+ beds or total > 6: Require approval
- Include capacity justification

**Modify Room Requests**:
- Minor changes: Immediate (amenities, description)
- Major changes: Require approval (pricing >20%, room type, capacity)

**Pricing Updates**:
- Small adjustments (<20%): Immediate
- Large changes (>20%): Require approval
- Seasonal rates: Usually immediate

## Best Practices

### Room Planning
1. **Start Small**: Begin with standard room configurations
2. **Plan Capacity**: Consider demand and space constraints
3. **Price Competitively**: Research local market rates
4. **Document Everything**: Provide clear descriptions and photos

### Bed Management
1. **Consistent Numbering**: Use logical bed numbering system
2. **Regular Updates**: Keep bed status current
3. **Maintenance Schedule**: Plan regular maintenance windows
4. **Occupancy Tracking**: Monitor and optimize occupancy rates

### Approval Strategy
1. **Batch Requests**: Submit multiple changes together when possible
2. **Provide Justification**: Clear business reasons speed approval
3. **Follow Guidelines**: Understand approval criteria
4. **Plan Ahead**: Allow time for approval process

## Troubleshooting

### Common Issues

**Request Rejected**:
- Review rejection reason in admin notes
- Address concerns and resubmit
- Contact admin for clarification if needed

**Bed Status Not Updating**:
- Check internet connection
- Refresh the page
- Verify bed is not locked by another process

**Pricing Changes Not Applied**:
- Large pricing changes may require approval
- Check request status in admin panel
- Ensure changes are within allowed limits

**Room Not Appearing**:
- New rooms require admin approval
- Check request status
- Verify all required information was provided

### Getting Help

1. **Documentation**: Review this guide and system help
2. **Support Tickets**: Submit support requests through the platform
3. **Admin Contact**: Reach out to super admin for urgent issues
4. **Training Resources**: Access video tutorials and guides

## System Limits and Guidelines

### Room Limits
- **Maximum Beds per Room**: 8 (requires special approval for more)
- **Minimum Room Area**: 50 sq ft per bed
- **Floor Range**: Ground floor (0) to 10th floor

### Pricing Guidelines
- **Daily Rate Range**: ₹200 - ₹2000 per bed
- **Security Deposit**: 1-3 months of monthly rate
- **Cleaning Fee**: ₹100 - ₹500 per room

### Approval Timeframes
- **Simple Requests**: 1-2 business days
- **Complex Requests**: 3-5 business days
- **Urgent Requests**: Contact admin directly

## Revenue Optimization Tips

### Pricing Strategy
1. **Dynamic Pricing**: Adjust rates based on demand
2. **Seasonal Rates**: Higher rates during peak seasons
3. **Long-term Discounts**: Encourage monthly bookings
4. **Competitive Analysis**: Monitor competitor pricing

### Occupancy Management
1. **Optimize Mix**: Balance room types based on demand
2. **Flexible Configurations**: Convert rooms based on needs
3. **Maintenance Planning**: Schedule during low-demand periods
4. **Marketing Integration**: Promote available rooms

### Analytics Usage
1. **Monitor Trends**: Track occupancy patterns
2. **Revenue Tracking**: Analyze income by room type
3. **Capacity Planning**: Plan expansions based on data
4. **Performance Metrics**: Set and track KPIs

## Security and Compliance

### Data Protection
- All room and occupancy data is encrypted
- Access is restricted to authorized personnel
- Regular backups ensure data safety

### Compliance Requirements
- Follow local housing regulations
- Maintain safety standards
- Keep accurate occupancy records
- Report as required by authorities

### Privacy Considerations
- Guest information is protected
- Access logs are maintained
- Data sharing is controlled
- Privacy policies are enforced

## Future Enhancements

### Planned Features
- **Mobile App**: Manage rooms from mobile devices
- **Advanced Analytics**: Detailed reporting and insights
- **Integration APIs**: Connect with booking platforms
- **Automated Pricing**: AI-driven pricing optimization

### Feedback and Suggestions
We welcome your feedback to improve the room management system:
- Submit feature requests through the platform
- Participate in user surveys
- Join beta testing programs
- Contact support with suggestions

---

**Last Updated**: January 2024  
**Version**: 1.0.0  
**Support**: Contact your system administrator or submit a support ticket
