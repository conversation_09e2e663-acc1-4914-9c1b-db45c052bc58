import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { 
  CreditCard, 
  DollarSign,
  TrendingUp,
  TrendingDown,
  ArrowUpRight,
  ArrowDownRight,
  Search,
  Filter,
  MoreHorizontal,
  Eye,
  Download,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Clock,
  XCircle,
  Users,
  Building2,
  Calendar,
  Shield,
  Activity,
  Zap,
  Settings,
  Bell
} from 'lucide-react';
import { mockPayments, mockBookings, mockUsers, getHostelById, getUserById } from '@/data/mockData';
import { EnhancedPayment, PaymentAnalytics } from '@/types/payment';

// Mock enhanced payment data for admin view
const mockAdminPayments: EnhancedPayment[] = mockPayments.map((payment, index) => ({
  ...payment,
  gateway: index % 2 === 0 ? 'phonepe' : 'cashfree',
  gatewayTransactionId: `TXN_${payment.id}_${Date.now()}`,
  paymentMethod: ['upi', 'card', 'netbanking'][index % 3] as any,
  receiptUrl: `/receipts/${payment.id}.pdf`,
  remindersSent: Math.floor(Math.random() * 3),
  dueDate: new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
  lateFee: Math.random() > 0.7 ? Math.floor(Math.random() * 500) : 0,
  discountApplied: Math.random() > 0.5 ? Math.floor(Math.random() * 1000) : 0
}));

// Mock payment analytics
const mockAnalytics: PaymentAnalytics = {
  totalRevenue: 2450000,
  totalTransactions: 156,
  successRate: 94.2,
  averageTransactionValue: 15705,
  gatewayPerformance: [
    {
      gateway: 'phonepe',
      transactions: 89,
      revenue: 1340000,
      successRate: 96.1,
      averageProcessingTime: 2.3
    },
    {
      gateway: 'cashfree',
      transactions: 67,
      revenue: 1110000,
      successRate: 91.8,
      averageProcessingTime: 3.1
    }
  ],
  methodDistribution: [
    { method: 'upi', count: 78, percentage: 50, revenue: 1225000 },
    { method: 'card', count: 45, percentage: 29, revenue: 890000 },
    { method: 'netbanking', count: 33, percentage: 21, revenue: 335000 }
  ],
  monthlyTrends: [
    { month: 'Jan 2024', revenue: 2100000, transactions: 142, successRate: 93.5 },
    { month: 'Feb 2024', revenue: 2450000, transactions: 156, successRate: 94.2 }
  ],
  overduePayments: {
    count: 12,
    totalAmount: 185000,
    averageDaysOverdue: 8.5
  }
};

export const PaymentManagement: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [gatewayFilter, setGatewayFilter] = useState<string>('all');
  const [methodFilter, setMethodFilter] = useState<string>('all');
  const [selectedPayment, setSelectedPayment] = useState<EnhancedPayment | null>(null);
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);

  // Filter payments
  const filteredPayments = mockAdminPayments.filter(payment => {
    const user = getUserById(payment.userId);
    const booking = mockBookings.find(b => b.id === payment.bookingId);
    const hostel = booking ? getHostelById(booking.hostelId) : null;
    
    const matchesSearch = payment.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         hostel?.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || payment.status === statusFilter;
    const matchesGateway = gatewayFilter === 'all' || payment.gateway === gatewayFilter;
    const matchesMethod = methodFilter === 'all' || payment.paymentMethod === methodFilter;
    
    return matchesSearch && matchesStatus && matchesGateway && matchesMethod;
  });

  const getStatusBadge = (status: string) => {
    const variants = {
      success: 'bg-green-100 text-green-800',
      pending: 'bg-yellow-100 text-yellow-800',
      failed: 'bg-red-100 text-red-800'
    };
    const icons = {
      success: CheckCircle,
      pending: Clock,
      failed: XCircle
    };
    const Icon = icons[status as keyof typeof icons] || Clock;
    return (
      <Badge className={`${variants[status as keyof typeof variants] || 'bg-gray-100 text-gray-800'} flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getGatewayBadge = (gateway: string) => {
    const variants = {
      phonepe: 'bg-purple-100 text-purple-800',
      cashfree: 'bg-blue-100 text-blue-800',
      razorpay: 'bg-green-100 text-green-800'
    };
    return (
      <Badge className={variants[gateway as keyof typeof variants] || 'bg-gray-100 text-gray-800'}>
        {gateway.charAt(0).toUpperCase() + gateway.slice(1)}
      </Badge>
    );
  };

  const getMethodIcon = (method: string) => {
    const icons = {
      card: CreditCard,
      upi: Zap,
      netbanking: Building2,
      wallet: Shield
    };
    return icons[method as keyof typeof icons] || CreditCard;
  };

  // Calculate statistics
  const totalRevenue = mockAnalytics.totalRevenue;
  const totalTransactions = mockAnalytics.totalTransactions;
  const successRate = mockAnalytics.successRate;
  const overdueCount = mockAnalytics.overduePayments.count;

  const paymentStats = [
    {
      title: 'Total Revenue',
      value: `₹${totalRevenue.toLocaleString()}`,
      icon: DollarSign,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      trend: 'up',
      change: '+12%',
      description: 'This month'
    },
    {
      title: 'Total Transactions',
      value: totalTransactions.toString(),
      icon: Activity,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      trend: 'up',
      change: '+8%',
      description: 'This month'
    },
    {
      title: 'Success Rate',
      value: `${successRate}%`,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      trend: 'up',
      change: '+0.7%',
      description: 'Last 30 days'
    },
    {
      title: 'Overdue Payments',
      value: overdueCount.toString(),
      icon: AlertTriangle,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      trend: 'down',
      change: '-3',
      description: 'Requires attention'
    }
  ];

  const handleViewPayment = (payment: EnhancedPayment) => {
    setSelectedPayment(payment);
    setShowPaymentDialog(true);
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold tracking-tight">Payment Management</h1>
          <p className="text-muted-foreground">
            Monitor and manage all payment transactions across the platform
          </p>
        </div>
        <div className="flex flex-col space-y-2 md:flex-row md:space-x-2 md:space-y-0">
          <Button variant="outline" className="w-full md:w-auto">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button variant="outline" className="w-full md:w-auto">
            <Download className="mr-2 h-4 w-4" />
            Export Data
          </Button>
          <Button className="w-full md:w-auto">
            <Settings className="mr-2 h-4 w-4" />
            Gateway Settings
          </Button>
        </div>
      </div>

      {/* Payment Statistics */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
        {paymentStats.map((stat, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                {stat.trend === 'up' ? (
                  <ArrowUpRight className="mr-1 h-3 w-3 text-green-500" />
                ) : (
                  <ArrowDownRight className="mr-1 h-3 w-3 text-red-500" />
                )}
                <span className={stat.trend === 'up' ? 'text-green-600' : 'text-red-600'}>
                  {stat.change}
                </span>
                <span className="ml-1">{stat.description}</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Payment Transactions Table */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Transactions</CardTitle>
          <CardDescription>
            View and manage all payment transactions across the platform
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex flex-col space-y-4 md:flex-row md:items-center md:space-x-4 md:space-y-0 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by payment ID, user, or hostel..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <div className="flex flex-col space-y-2 md:flex-row md:space-x-2 md:space-y-0">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full md:w-40">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="success">Success</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                </SelectContent>
              </Select>

              <Select value={gatewayFilter} onValueChange={setGatewayFilter}>
                <SelectTrigger className="w-full md:w-40">
                  <SelectValue placeholder="Gateway" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Gateways</SelectItem>
                  <SelectItem value="phonepe">PhonePe</SelectItem>
                  <SelectItem value="cashfree">Cashfree</SelectItem>
                  <SelectItem value="razorpay">Razorpay</SelectItem>
                </SelectContent>
              </Select>

              <Select value={methodFilter} onValueChange={setMethodFilter}>
                <SelectTrigger className="w-full md:w-40">
                  <SelectValue placeholder="Method" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Methods</SelectItem>
                  <SelectItem value="upi">UPI</SelectItem>
                  <SelectItem value="card">Card</SelectItem>
                  <SelectItem value="netbanking">Net Banking</SelectItem>
                  <SelectItem value="wallet">Wallet</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Payments Table */}
          <div className="rounded-md border overflow-hidden">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="min-w-[120px]">Payment ID</TableHead>
                    <TableHead className="min-w-[150px]">User</TableHead>
                    <TableHead className="min-w-[150px]">Hostel</TableHead>
                    <TableHead className="min-w-[100px]">Amount</TableHead>
                    <TableHead className="min-w-[100px]">Method</TableHead>
                    <TableHead className="min-w-[100px]">Gateway</TableHead>
                    <TableHead className="min-w-[100px]">Status</TableHead>
                    <TableHead className="min-w-[120px]">Date</TableHead>
                    <TableHead className="text-right min-w-[100px]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredPayments.map((payment) => {
                    const user = getUserById(payment.userId);
                    const booking = mockBookings.find(b => b.id === payment.bookingId);
                    const hostel = booking ? getHostelById(booking.hostelId) : null;
                    const MethodIcon = getMethodIcon(payment.paymentMethod);

                    return (
                      <TableRow key={payment.id}>
                        <TableCell className="font-medium">
                          {payment.id}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Users className="h-4 w-4 text-muted-foreground" />
                            <div>
                              <div className="font-medium text-sm">{user?.name}</div>
                              <div className="text-xs text-muted-foreground">{user?.email}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Building2 className="h-4 w-4 text-muted-foreground" />
                            <div>
                              <div className="font-medium text-sm">{hostel?.name}</div>
                              <div className="text-xs text-muted-foreground">{booking?.bedNumber}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="font-medium">₹{payment.amount.toLocaleString()}</span>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <MethodIcon className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm capitalize">{payment.paymentMethod}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          {getGatewayBadge(payment.gateway)}
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(payment.status)}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-3 w-3 text-muted-foreground" />
                            <span className="text-sm">
                              {new Date(payment.date).toLocaleDateString()}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => handleViewPayment(payment)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Download className="mr-2 h-4 w-4" />
                                Download Receipt
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>
                                <Bell className="mr-2 h-4 w-4" />
                                Send Reminder
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          </div>

          {filteredPayments.length === 0 && (
            <div className="text-center py-8">
              <CreditCard className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No payments found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Try adjusting your search criteria.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Payment Details Dialog */}
      <Dialog open={showPaymentDialog} onOpenChange={setShowPaymentDialog}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Payment Details</DialogTitle>
            <DialogDescription>
              Complete information about this payment transaction
            </DialogDescription>
          </DialogHeader>
          {selectedPayment && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Payment ID</label>
                  <div className="mt-1 font-medium">{selectedPayment.id}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Gateway Transaction ID</label>
                  <div className="mt-1 font-medium">{selectedPayment.gatewayTransactionId}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Amount</label>
                  <div className="mt-1 font-medium">₹{selectedPayment.amount.toLocaleString()}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Status</label>
                  <div className="mt-1">{getStatusBadge(selectedPayment.status)}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Payment Method</label>
                  <div className="mt-1 capitalize">{selectedPayment.paymentMethod}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Gateway</label>
                  <div className="mt-1">{getGatewayBadge(selectedPayment.gateway)}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Date</label>
                  <div className="mt-1">{new Date(selectedPayment.date).toLocaleString()}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Reminders Sent</label>
                  <div className="mt-1">{selectedPayment.remindersSent || 0}</div>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPaymentDialog(false)}>
              Close
            </Button>
            <Button>
              <Download className="mr-2 h-4 w-4" />
              Download Receipt
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
