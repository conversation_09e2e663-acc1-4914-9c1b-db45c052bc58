import { useState } from 'react';
import { Navigation } from '@/components/Navigation';
import { LandingPage } from '@/components/LandingPage';
import { SuperAdminPanel } from '@/components/panels/SuperAdminPanel';
import { HostelOwnerPanel } from '@/components/panels/HostelOwnerPanel';
import { EmployeePanel } from '@/components/panels/EmployeePanel';
import { UserPanel } from '@/components/panels/UserPanel';

const Index = () => {
  const [currentPanel, setCurrentPanel] = useState<'landing' | 'superadmin' | 'owner' | 'employee' | 'user'>('landing');

  const renderPanel = () => {
    switch (currentPanel) {
      case 'landing':
        return <LandingPage onPanelChange={setCurrentPanel} />;
      case 'superadmin':
        return <SuperAdminPanel />;
      case 'owner':
        return <HostelOwnerPanel />;
      case 'employee':
        return <EmployeePanel />;
      case 'user':
        return <UserPanel />;
      default:
        return <LandingPage onPanelChange={setCurrentPanel} />;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation currentPanel={currentPanel} onPanelChange={setCurrentPanel} />
      {renderPanel()}
    </div>
  );
};

export default Index;
