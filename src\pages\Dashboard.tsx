import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { SuperAdminPanel } from '@/components/panels/SuperAdminPanel';
import { HostelOwnerPanel } from '@/components/panels/HostelOwnerPanel';
import { EmployeePanel } from '@/components/panels/EmployeePanel';
import { UserPanel } from '@/components/panels/UserPanel';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Building2, Users, UserCheck, User, ArrowRight, DollarSign, BarChart3, MessageSquare, FileText, Calendar, Wrench, Megaphone, Package, TrendingUp, ClipboardList, CreditCard, HelpCircle, Gift, Heart, Bell, Search, Timer, LayoutDashboard, Bed } from 'lucide-react';

// Welcome component for first-time users or role-based dashboard selection
const DashboardWelcome: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();

  if (!user) return null;

  const getWelcomeMessage = () => {
    const hour = new Date().getHours();
    let greeting = 'Good morning';
    if (hour >= 12 && hour < 18) greeting = 'Good afternoon';
    else if (hour >= 18) greeting = 'Good evening';

    return `${greeting}, ${user.name.split(' ')[0]}!`;
  };

  const getRoleDescription = () => {
    const descriptions = {
      superadmin: 'You have full system access to manage all hostels, users, and platform operations.',
      owner: 'Manage your hostels, employees, and track your business performance.',
      employee: 'Handle daily operations, manage residents, and maintain hostel facilities.',
      member: 'Find and book hostel accommodations that suit your needs.',
    };
    return descriptions[user.role];
  };

  const getQuickActions = () => {
    const actions = {
      superadmin: [
        { title: 'System Overview', description: 'View platform analytics', icon: Building2, route: '/admin/overview' },
        { title: 'Financial Management', description: 'Revenue & payments', icon: DollarSign, route: '/admin/financial' },
        { title: 'Payment Management', description: 'Monitor all payments', icon: CreditCard, route: '/admin/payment-management' },
        { title: 'Payment Gateway Config', description: 'Configure payment gateways', icon: Wrench, route: '/admin/payment-gateway-config' },
      ],
      owner: [
        { title: 'Owner Dashboard', description: 'View analytics & metrics', icon: BarChart3, route: '/owner/dashboard' },
        { title: 'Member Applications', description: 'Review pending applications', icon: UserCheck, route: '/owner/member-applications' },
        { title: 'Room Allocation', description: 'Manage bed assignments', icon: Bed, route: '/owner/room-allocation' },
        { title: 'Hostel Registration', description: 'Update hostel information', icon: Building2, route: '/owner/registration' },
      ],
      employee: [
        { title: 'Work Dashboard', description: 'Daily work overview', icon: LayoutDashboard, route: '/employee/dashboard' },
        { title: 'Task Management', description: 'Manage assigned tasks', icon: ClipboardList, route: '/employee/tasks' },
        { title: 'Residents', description: 'Manage current residents', icon: Users, route: '/employee/residents' },
        { title: 'Rooms', description: 'Room management', icon: Building2, route: '/employee/rooms' },
        { title: 'Complaints', description: 'Handle complaints', icon: UserCheck, route: '/employee/complaints' },
      ],
      member: [
        { title: 'Find Hostels', description: 'Search accommodations', icon: Search, route: '/member/search' },
        { title: 'My Bookings', description: 'View your bookings', icon: Calendar, route: '/member/bookings' },
        { title: 'Personal Profile', description: 'Manage personal info', icon: User, route: '/member/personal-profile' },
        { title: 'Payment Dashboard', description: 'Manage payments & dues', icon: CreditCard, route: '/member/payment-dashboard' },
        { title: 'Make Payment', description: 'Pay hostel fees', icon: DollarSign, route: '/member/make-payment' },
        { title: 'Split Payment', description: 'Share costs with roommates', icon: Users, route: '/member/split-payment' },
        { title: 'Payment History', description: 'View payment history', icon: BarChart3, route: '/member/payment-history' },
        { title: 'Notifications', description: 'View notifications', icon: Bell, route: '/member/notifications' },
        { title: 'Wishlist & Favorites', description: 'Saved hostels', icon: Heart, route: '/member/wishlist' },
        { title: 'Referral Program', description: 'Invite friends & earn', icon: Gift, route: '/member/referrals' },
        { title: 'Support Center', description: 'Get help & support', icon: HelpCircle, route: '/member/support' },
      ],
    };
    return actions[user.role] || [];
  };

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">{getWelcomeMessage()}</h1>
        <p className="text-muted-foreground text-lg">
          {getRoleDescription()}
        </p>
      </div>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {getQuickActions().map((action, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow cursor-pointer">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {action.title}
              </CardTitle>
              <action.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <p className="text-xs text-muted-foreground mb-3">
                {action.description}
              </p>
              <Button
                size="sm"
                variant="outline"
                className="w-full"
                onClick={() => navigate(action.route)}
              >
                Get Started
                <ArrowRight className="ml-2 h-3 w-3" />
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Role-specific Dashboard Content */}
      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle>Your Dashboard</CardTitle>
            <CardDescription>
              Role-specific tools and information for your daily tasks
            </CardDescription>
          </CardHeader>
          <CardContent>
            <DashboardContent />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

// Component that renders role-specific dashboard content
const DashboardContent: React.FC = () => {
  const { user } = useAuth();

  if (!user) return null;

  // Render the appropriate panel based on user role
  switch (user.role) {
    case 'superadmin':
      return <SuperAdminPanel />;
    case 'owner':
      return <HostelOwnerPanel />;
    case 'employee':
      return <EmployeePanel />;
    case 'member':
      return <UserPanel />;
    default:
      return (
        <div className="text-center py-8">
          <p className="text-muted-foreground">
            Dashboard content not available for your role.
          </p>
        </div>
      );
  }
};

// Main Dashboard component
export const Dashboard: React.FC = () => {
  return <DashboardWelcome />;
};
