import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { 
  Plus,
  Building,
  Layers,
  Users,
  DollarSign,
  FileText,
  AlertCircle
} from 'lucide-react';
import { FloorType, AddFloorRequest, HostelInfo } from '@/types/roomManagement';
import { submitAddFloorRequest } from '@/services/floorManagementService';
import { toast } from '@/hooks/use-toast';

interface AddFloorDialogProps {
  hostel: HostelInfo;
  ownerId: string;
  onFloorAdded?: () => void;
  trigger?: React.ReactNode;
}

const FLOOR_TYPES: { value: FloorType; label: string; description: string }[] = [
  { value: 'residential', label: 'Residential', description: 'Living quarters for residents' },
  { value: 'commercial', label: 'Commercial', description: 'Business or retail spaces' },
  { value: 'mixed', label: 'Mixed Use', description: 'Combination of residential and commercial' },
  { value: 'common_area', label: 'Common Area', description: 'Shared facilities and amenities' },
  { value: 'parking', label: 'Parking', description: 'Vehicle parking space' },
  { value: 'storage', label: 'Storage', description: 'Storage and utility areas' }
];

const COMMON_AMENITIES = [
  'WiFi', 'CCTV', 'Intercom', 'Power Backup', 'Water Supply',
  'Study Area', 'Common Room', 'Laundry', 'Kitchen Access',
  'Elevator Access', 'Fire Exit', 'Security'
];

export const AddFloorDialog: React.FC<AddFloorDialogProps> = ({
  hostel,
  ownerId,
  onFloorAdded,
  trigger
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  
  const [formData, setFormData] = useState<AddFloorRequest>({
    floorNumber: hostel.totalFloors + 1,
    floorType: 'residential',
    plannedRooms: 4,
    plannedBeds: 12,
    amenities: ['WiFi', 'CCTV', 'Power Backup', 'Water Supply'],
    details: {
      area: 1000,
      ceilingHeight: 9,
      hasElevatorAccess: false,
      hasStairAccess: true,
      hasFireExit: true,
      hasCommonArea: false,
      hasWiFi: true,
      hasCCTV: true,
      hasIntercom: true,
      hasPowerBackup: true,
      hasWaterSupply: true,
      description: ''
    },
    constructionTimeline: '3 months',
    estimatedCost: 500000,
    reason: '',
    expectedOccupancyIncrease: 12,
    businessJustification: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (isOpen) {
      // Reset form when dialog opens
      setCurrentStep(1);
      setErrors({});
      setFormData(prev => ({
        ...prev,
        floorNumber: hostel.totalFloors + 1,
        expectedOccupancyIncrease: prev.plannedBeds
      }));
    }
  }, [isOpen, hostel.totalFloors]);

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    switch (step) {
      case 1:
        if (formData.floorNumber <= 0) {
          newErrors.floorNumber = 'Floor number must be positive';
        }
        if (formData.plannedRooms <= 0) {
          newErrors.plannedRooms = 'Number of rooms must be positive';
        }
        if (formData.plannedBeds <= 0) {
          newErrors.plannedBeds = 'Number of beds must be positive';
        }
        if (formData.details.area <= 0) {
          newErrors.area = 'Floor area must be positive';
        }
        break;

      case 2:
        if (formData.estimatedCost <= 0) {
          newErrors.estimatedCost = 'Estimated cost must be positive';
        }
        if (!formData.constructionTimeline.trim()) {
          newErrors.constructionTimeline = 'Construction timeline is required';
        }
        break;

      case 3:
        if (!formData.reason.trim()) {
          newErrors.reason = 'Reason for adding floor is required';
        }
        if (!formData.businessJustification.trim()) {
          newErrors.businessJustification = 'Business justification is required';
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => prev - 1);
  };

  const handleSubmit = async () => {
    if (!validateStep(3)) return;

    setIsSubmitting(true);
    try {
      const result = await submitAddFloorRequest(hostel.id, ownerId, formData);
      
      if (result.success) {
        toast({
          title: "Floor Addition Request Submitted",
          description: `Your request to add Floor ${formData.floorNumber} has been submitted for admin approval.`,
        });
        
        setIsOpen(false);
        if (onFloorAdded) {
          onFloorAdded();
        }
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to submit floor addition request",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error submitting floor request:', error);
      toast({
        title: "Error",
        description: "Failed to submit floor addition request",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const updateFormData = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof AddFloorRequest],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }

    // Auto-calculate expected occupancy increase
    if (field === 'plannedBeds') {
      setFormData(prev => ({
        ...prev,
        expectedOccupancyIncrease: value
      }));
    }
  };

  const toggleAmenity = (amenity: string) => {
    setFormData(prev => ({
      ...prev,
      amenities: prev.amenities.includes(amenity)
        ? prev.amenities.filter(a => a !== amenity)
        : [...prev.amenities, amenity]
    }));
  };

  const renderStep1 = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="floorNumber">Floor Number</Label>
          <Input
            id="floorNumber"
            type="number"
            value={formData.floorNumber}
            onChange={(e) => updateFormData('floorNumber', parseInt(e.target.value))}
            min="1"
          />
          {errors.floorNumber && (
            <p className="text-sm text-red-600">{errors.floorNumber}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="floorType">Floor Type</Label>
          <Select value={formData.floorType} onValueChange={(value) => updateFormData('floorType', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {FLOOR_TYPES.map((type) => (
                <SelectItem key={type.value} value={type.value}>
                  <div>
                    <div className="font-medium">{type.label}</div>
                    <div className="text-sm text-muted-foreground">{type.description}</div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="plannedRooms">Planned Rooms</Label>
          <Input
            id="plannedRooms"
            type="number"
            value={formData.plannedRooms}
            onChange={(e) => updateFormData('plannedRooms', parseInt(e.target.value))}
            min="1"
          />
          {errors.plannedRooms && (
            <p className="text-sm text-red-600">{errors.plannedRooms}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="plannedBeds">Planned Beds</Label>
          <Input
            id="plannedBeds"
            type="number"
            value={formData.plannedBeds}
            onChange={(e) => updateFormData('plannedBeds', parseInt(e.target.value))}
            min="1"
          />
          {errors.plannedBeds && (
            <p className="text-sm text-red-600">{errors.plannedBeds}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="area">Floor Area (sq ft)</Label>
          <Input
            id="area"
            type="number"
            value={formData.details.area}
            onChange={(e) => updateFormData('details.area', parseInt(e.target.value))}
            min="1"
          />
          {errors.area && (
            <p className="text-sm text-red-600">{errors.area}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="ceilingHeight">Ceiling Height (ft)</Label>
          <Input
            id="ceilingHeight"
            type="number"
            value={formData.details.ceilingHeight}
            onChange={(e) => updateFormData('details.ceilingHeight', parseInt(e.target.value))}
            min="1"
          />
        </div>
      </div>

      <div className="space-y-3">
        <Label>Floor Features</Label>
        <div className="grid grid-cols-2 gap-3">
          <div className="flex items-center space-x-2">
            <Checkbox
              checked={formData.details.hasElevatorAccess}
              onCheckedChange={(checked) => updateFormData('details.hasElevatorAccess', checked)}
            />
            <Label className="text-sm">Elevator Access</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              checked={formData.details.hasStairAccess}
              onCheckedChange={(checked) => updateFormData('details.hasStairAccess', checked)}
            />
            <Label className="text-sm">Stair Access</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              checked={formData.details.hasFireExit}
              onCheckedChange={(checked) => updateFormData('details.hasFireExit', checked)}
            />
            <Label className="text-sm">Fire Exit</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              checked={formData.details.hasCommonArea}
              onCheckedChange={(checked) => updateFormData('details.hasCommonArea', checked)}
            />
            <Label className="text-sm">Common Area</Label>
          </div>
        </div>
      </div>

      <div className="space-y-3">
        <Label>Amenities</Label>
        <div className="flex flex-wrap gap-2">
          {COMMON_AMENITIES.map((amenity) => (
            <Badge
              key={amenity}
              variant={formData.amenities.includes(amenity) ? "default" : "outline"}
              className="cursor-pointer"
              onClick={() => toggleAmenity(amenity)}
            >
              {amenity}
            </Badge>
          ))}
        </div>
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="estimatedCost">Estimated Cost (₹)</Label>
          <Input
            id="estimatedCost"
            type="number"
            value={formData.estimatedCost}
            onChange={(e) => updateFormData('estimatedCost', parseInt(e.target.value))}
            min="1"
          />
          {errors.estimatedCost && (
            <p className="text-sm text-red-600">{errors.estimatedCost}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="constructionTimeline">Construction Timeline</Label>
          <Input
            id="constructionTimeline"
            value={formData.constructionTimeline}
            onChange={(e) => updateFormData('constructionTimeline', e.target.value)}
            placeholder="e.g., 3 months"
          />
          {errors.constructionTimeline && (
            <p className="text-sm text-red-600">{errors.constructionTimeline}</p>
          )}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="contractorInfo">Contractor Information (Optional)</Label>
        <Input
          id="contractorInfo"
          value={formData.contractorInfo || ''}
          onChange={(e) => updateFormData('contractorInfo', e.target.value)}
          placeholder="Contractor name and contact details"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Floor Description</Label>
        <Textarea
          id="description"
          value={formData.details.description}
          onChange={(e) => updateFormData('details.description', e.target.value)}
          placeholder="Describe the floor layout, special features, etc."
          rows={3}
        />
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="reason">Reason for Adding Floor</Label>
        <Textarea
          id="reason"
          value={formData.reason}
          onChange={(e) => updateFormData('reason', e.target.value)}
          placeholder="Explain why this floor addition is needed"
          rows={3}
        />
        {errors.reason && (
          <p className="text-sm text-red-600">{errors.reason}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="businessJustification">Business Justification</Label>
        <Textarea
          id="businessJustification"
          value={formData.businessJustification}
          onChange={(e) => updateFormData('businessJustification', e.target.value)}
          placeholder="Explain the business benefits and ROI of this addition"
          rows={3}
        />
        {errors.businessJustification && (
          <p className="text-sm text-red-600">{errors.businessJustification}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="expectedOccupancyIncrease">Expected Occupancy Increase</Label>
        <Input
          id="expectedOccupancyIncrease"
          type="number"
          value={formData.expectedOccupancyIncrease}
          onChange={(e) => updateFormData('expectedOccupancyIncrease', parseInt(e.target.value))}
          min="1"
        />
      </div>

      {/* Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Request Summary</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="flex justify-between">
            <span>Floor Number:</span>
            <span className="font-medium">{formData.floorNumber}</span>
          </div>
          <div className="flex justify-between">
            <span>Floor Type:</span>
            <span className="font-medium">{FLOOR_TYPES.find(t => t.value === formData.floorType)?.label}</span>
          </div>
          <div className="flex justify-between">
            <span>Planned Rooms:</span>
            <span className="font-medium">{formData.plannedRooms}</span>
          </div>
          <div className="flex justify-between">
            <span>Planned Beds:</span>
            <span className="font-medium">{formData.plannedBeds}</span>
          </div>
          <div className="flex justify-between">
            <span>Estimated Cost:</span>
            <span className="font-medium">₹{formData.estimatedCost.toLocaleString()}</span>
          </div>
          <div className="flex justify-between">
            <span>Timeline:</span>
            <span className="font-medium">{formData.constructionTimeline}</span>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Floor
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Layers className="h-5 w-5" />
            Add New Floor - {hostel.name}
          </DialogTitle>
          <DialogDescription>
            Submit a request to add a new floor to your hostel. This request will be reviewed by administrators.
          </DialogDescription>
        </DialogHeader>

        {/* Step Indicator */}
        <div className="flex items-center justify-center space-x-4 py-4">
          {[1, 2, 3].map((step) => (
            <div key={step} className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                step <= currentStep ? 'bg-primary text-primary-foreground' : 'bg-gray-200 text-gray-600'
              }`}>
                {step}
              </div>
              {step < 3 && (
                <div className={`w-12 h-1 mx-2 ${
                  step < currentStep ? 'bg-primary' : 'bg-gray-200'
                }`} />
              )}
            </div>
          ))}
        </div>

        {/* Step Content */}
        <div className="py-4">
          {currentStep === 1 && renderStep1()}
          {currentStep === 2 && renderStep2()}
          {currentStep === 3 && renderStep3()}
        </div>

        <DialogFooter className="flex justify-between">
          <div>
            {currentStep > 1 && (
              <Button variant="outline" onClick={handlePrevious}>
                Previous
              </Button>
            )}
          </div>
          <div className="space-x-2">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            {currentStep < 3 ? (
              <Button onClick={handleNext}>
                Next
              </Button>
            ) : (
              <Button onClick={handleSubmit} disabled={isSubmitting}>
                {isSubmitting ? 'Submitting...' : 'Submit Request'}
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
