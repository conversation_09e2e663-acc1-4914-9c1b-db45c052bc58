import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, Plus, X, Layers, CheckCircle } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from '@/hooks/use-toast';
import { RoomType, BedType, BedSize, Floor } from '@/types/roomManagement';
import { submitAddRoomRequest } from '@/services/roomManagementService';
import { getFloorsByHostelId } from '@/data/mockData';

const addRoomSchema = z.object({
  roomNumber: z.string().min(1, 'Room number is required'),
  roomType: z.enum(['single', 'double', 'triple', 'quad', 'dormitory', 'suite', 'studio', 'family']),
  floorId: z.string().min(1, 'Floor selection is required'),
  floor: z.number().min(0, 'Floor must be 0 or higher'),
  capacity: z.number().min(1, 'Capacity must be at least 1'),
  area: z.number().min(1, 'Area is required'),
  dailyRate: z.number().min(1, 'Daily rate is required'),
  weeklyRate: z.number().min(1, 'Weekly rate is required'),
  monthlyRate: z.number().min(1, 'Monthly rate is required'),
  securityDeposit: z.number().min(0, 'Security deposit must be 0 or higher'),
  cleaningFee: z.number().min(0, 'Cleaning fee must be 0 or higher'),
  description: z.string().optional(),
  reason: z.string().min(10, 'Please provide a reason for adding this room'),
  expectedOccupancyIncrease: z.number().min(1, 'Expected occupancy increase is required')
});

type AddRoomFormData = z.infer<typeof addRoomSchema>;

interface BedConfig {
  bedNumber: string;
  bedType: BedType;
  size: BedSize;
  hasLocker: boolean;
  lockerNumber?: string;
}

interface AddRoomDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onRoomAdded: () => void;
  hostelId: string;
  ownerId: string;
}

export const AddRoomDialog: React.FC<AddRoomDialogProps> = ({
  isOpen,
  onClose,
  onRoomAdded,
  hostelId,
  ownerId
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedAmenities, setSelectedAmenities] = useState<string[]>([]);
  const [bedConfigs, setBedConfigs] = useState<BedConfig[]>([]);
  const [availableFloors, setAvailableFloors] = useState<Floor[]>([]);
  const [selectedFloor, setSelectedFloor] = useState<Floor | null>(null);
  const [roomFeatures, setRoomFeatures] = useState({
    hasWindow: false,
    hasBalcony: false,
    hasAttachedBathroom: false,
    hasAC: false,
    hasFan: true,
    hasWiFi: true,
    hasTV: false,
    hasRefrigerator: false,
    hasWardrobe: true,
    hasStudyTable: true,
    hasChair: true
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    reset
  } = useForm<AddRoomFormData>({
    resolver: zodResolver(addRoomSchema),
    defaultValues: {
      floor: 1,
      capacity: 2,
      area: 100,
      dailyRate: 300,
      weeklyRate: 2000,
      monthlyRate: 8000,
      securityDeposit: 5000,
      cleaningFee: 200,
      expectedOccupancyIncrease: 2
    }
  });

  const watchedCapacity = watch('capacity');
  const watchedRoomNumber = watch('roomNumber');

  // Load available floors when dialog opens
  useEffect(() => {
    if (isOpen && hostelId) {
      const floors = getFloorsByHostelId(hostelId);
      setAvailableFloors(floors);

      if (floors.length > 0) {
        setSelectedFloor(floors[0]);
        setValue('floorId', floors[0].id);
        setValue('floor', floors[0].floorNumber);
      }
    }
  }, [isOpen, hostelId, setValue]);

  const availableAmenities = [
    'AC', 'WiFi', 'Attached Bathroom', 'Shared Bathroom', 'Study Table', 
    'Wardrobe', 'TV', 'Refrigerator', 'Balcony', 'Window', 'Fan', 'Locker'
  ];

  // Auto-generate bed configurations when capacity changes
  React.useEffect(() => {
    if (watchedCapacity && watchedRoomNumber) {
      const newBedConfigs: BedConfig[] = [];
      for (let i = 0; i < watchedCapacity; i++) {
        newBedConfigs.push({
          bedNumber: `${watchedRoomNumber}${String.fromCharCode(65 + i)}`, // A, B, C, etc.
          bedType: 'single',
          size: 'single',
          hasLocker: true,
          lockerNumber: `${watchedRoomNumber}${String.fromCharCode(65 + i)}`
        });
      }
      setBedConfigs(newBedConfigs);
    }
  }, [watchedCapacity, watchedRoomNumber]);

  const handleAmenityToggle = (amenity: string) => {
    setSelectedAmenities(prev =>
      prev.includes(amenity)
        ? prev.filter(a => a !== amenity)
        : [...prev, amenity]
    );
  };

  const handleFloorChange = (floorId: string) => {
    const floor = availableFloors.find(f => f.id === floorId);
    if (floor) {
      setSelectedFloor(floor);
      setValue('floorId', floor.id);
      setValue('floor', floor.floorNumber);
    }
  };

  const updateBedConfig = (index: number, field: keyof BedConfig, value: any) => {
    setBedConfigs(prev => prev.map((bed, i) => 
      i === index ? { ...bed, [field]: value } : bed
    ));
  };

  const onSubmit = async (data: AddRoomFormData) => {
    setIsSubmitting(true);

    try {
      const roomData = {
        roomNumber: data.roomNumber,
        roomType: data.roomType,
        floor: data.floor,
        capacity: data.capacity,
        amenities: selectedAmenities,
        beds: bedConfigs.map(bed => ({
          bedNumber: bed.bedNumber,
          bedType: bed.bedType,
          status: 'available' as const,
          size: bed.size,
          hasLocker: bed.hasLocker,
          lockerNumber: bed.hasLocker ? bed.lockerNumber : undefined
        })),
        pricing: {
          dailyRate: data.dailyRate,
          weeklyRate: data.weeklyRate,
          monthlyRate: data.monthlyRate,
          securityDeposit: data.securityDeposit,
          cleaningFee: data.cleaningFee
        },
        details: {
          area: data.area,
          ...roomFeatures,
          bathroomType: roomFeatures.hasAttachedBathroom ? 'private' as const : 'shared' as const,
          description: data.description
        },
        photos: [], // Would be handled by file upload in real implementation
        reason: data.reason,
        expectedOccupancyIncrease: data.expectedOccupancyIncrease
      };

      const result = await submitAddRoomRequest(hostelId, ownerId, roomData);

      if (result.success) {
        toast({
          title: "Room Addition Request Submitted",
          description: `Your request to add room ${data.roomNumber} has been submitted for admin approval.`,
        });
        onRoomAdded();
        handleClose();
      } else {
        toast({
          title: "Submission Failed",
          description: result.error || "Failed to submit room addition request",
          variant: "destructive"
        });
      }

    } catch (error) {
      console.error('Error submitting room addition request:', error);
      toast({
        title: "Submission Failed",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    reset();
    setSelectedAmenities([]);
    setBedConfigs([]);
    setRoomFeatures({
      hasWindow: false,
      hasBalcony: false,
      hasAttachedBathroom: false,
      hasAC: false,
      hasFan: true,
      hasWiFi: true,
      hasTV: false,
      hasRefrigerator: false,
      hasWardrobe: true,
      hasStudyTable: true,
      hasChair: true
    });
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add New Room</DialogTitle>
          <DialogDescription>
            Submit a request to add a new room to your hostel. This request will be reviewed by the admin.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)}>
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="beds">Bed Config</TabsTrigger>
              <TabsTrigger value="pricing">Pricing</TabsTrigger>
              <TabsTrigger value="features">Features</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="roomNumber">Room Number *</Label>
                  <Input
                    id="roomNumber"
                    {...register('roomNumber')}
                    placeholder="e.g., 101, A1, etc."
                  />
                  {errors.roomNumber && (
                    <p className="text-sm text-red-600 mt-1">{errors.roomNumber.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="roomType">Room Type *</Label>
                  <Select onValueChange={(value) => setValue('roomType', value as RoomType)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select room type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="single">Single</SelectItem>
                      <SelectItem value="double">Double</SelectItem>
                      <SelectItem value="triple">Triple</SelectItem>
                      <SelectItem value="quad">Quad</SelectItem>
                      <SelectItem value="dormitory">Dormitory</SelectItem>
                      <SelectItem value="suite">Suite</SelectItem>
                      <SelectItem value="studio">Studio</SelectItem>
                      <SelectItem value="family">Family</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.roomType && (
                    <p className="text-sm text-red-600 mt-1">{errors.roomType.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="floor">Select Floor *</Label>
                  {availableFloors.length > 0 ? (
                    <Select value={selectedFloor?.id || ''} onValueChange={handleFloorChange}>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a floor" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableFloors.map((floor) => (
                          <SelectItem key={floor.id} value={floor.id}>
                            <div className="flex items-center space-x-2">
                              <Layers className="h-4 w-4" />
                              <span>Floor {floor.floorNumber}</span>
                              <span className="text-muted-foreground">
                                ({floor.floorType} • {floor.totalRooms} rooms)
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  ) : (
                    <div className="p-3 border border-dashed rounded-lg text-center">
                      <Layers className="mx-auto h-6 w-6 text-gray-400" />
                      <p className="text-sm text-gray-500 mt-1">No floors available</p>
                      <p className="text-xs text-gray-400">Add a floor first before creating rooms</p>
                    </div>
                  )}
                  {errors.floorId && (
                    <p className="text-sm text-red-600 mt-1">{errors.floorId.message}</p>
                  )}
                  {selectedFloor && (
                    <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded text-sm">
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span className="text-green-800">
                          Selected: Floor {selectedFloor.floorNumber} ({selectedFloor.floorType})
                        </span>
                      </div>
                      <div className="text-green-700 text-xs mt-1">
                        Floor ID: {selectedFloor.id} • Current rooms: {selectedFloor.totalRooms}
                      </div>
                    </div>
                  )}
                </div>

                <div>
                  <Label htmlFor="capacity">Bed Capacity *</Label>
                  <Input
                    id="capacity"
                    type="number"
                    {...register('capacity', { valueAsNumber: true })}
                    placeholder="Number of beds"
                  />
                  {errors.capacity && (
                    <p className="text-sm text-red-600 mt-1">{errors.capacity.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="area">Room Area (sq ft) *</Label>
                  <Input
                    id="area"
                    type="number"
                    {...register('area', { valueAsNumber: true })}
                    placeholder="Area in square feet"
                  />
                  {errors.area && (
                    <p className="text-sm text-red-600 mt-1">{errors.area.message}</p>
                  )}
                </div>
              </div>

              <div>
                <Label>Amenities</Label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {availableAmenities.map((amenity) => (
                    <Badge
                      key={amenity}
                      variant={selectedAmenities.includes(amenity) ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => handleAmenityToggle(amenity)}
                    >
                      {amenity}
                    </Badge>
                  ))}
                </div>
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  {...register('description')}
                  placeholder="Describe the room features and layout"
                  rows={3}
                />
              </div>
            </TabsContent>

            <TabsContent value="beds" className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold mb-4">Bed Configuration</h3>
                <div className="space-y-4">
                  {bedConfigs.map((bed, index) => (
                    <Card key={index}>
                      <CardHeader>
                        <CardTitle className="text-sm">Bed {index + 1}</CardTitle>
                      </CardHeader>
                      <CardContent className="grid grid-cols-2 gap-4">
                        <div>
                          <Label>Bed Number</Label>
                          <Input
                            value={bed.bedNumber}
                            onChange={(e) => updateBedConfig(index, 'bedNumber', e.target.value)}
                          />
                        </div>
                        <div>
                          <Label>Bed Type</Label>
                          <Select 
                            value={bed.bedType} 
                            onValueChange={(value) => updateBedConfig(index, 'bedType', value)}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="single">Single</SelectItem>
                              <SelectItem value="double">Double</SelectItem>
                              <SelectItem value="bunk_top">Bunk Top</SelectItem>
                              <SelectItem value="bunk_bottom">Bunk Bottom</SelectItem>
                              <SelectItem value="queen">Queen</SelectItem>
                              <SelectItem value="king">King</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label>Bed Size</Label>
                          <Select 
                            value={bed.size} 
                            onValueChange={(value) => updateBedConfig(index, 'size', value)}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="single">Single</SelectItem>
                              <SelectItem value="double">Double</SelectItem>
                              <SelectItem value="queen">Queen</SelectItem>
                              <SelectItem value="king">King</SelectItem>
                              <SelectItem value="super_king">Super King</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            checked={bed.hasLocker}
                            onCheckedChange={(checked) => updateBedConfig(index, 'hasLocker', !!checked)}
                          />
                          <Label>Has Locker</Label>
                        </div>
                        {bed.hasLocker && (
                          <div>
                            <Label>Locker Number</Label>
                            <Input
                              value={bed.lockerNumber || ''}
                              onChange={(e) => updateBedConfig(index, 'lockerNumber', e.target.value)}
                            />
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="pricing" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="dailyRate">Daily Rate (₹) *</Label>
                  <Input
                    id="dailyRate"
                    type="number"
                    {...register('dailyRate', { valueAsNumber: true })}
                    placeholder="Daily rate per bed"
                  />
                  {errors.dailyRate && (
                    <p className="text-sm text-red-600 mt-1">{errors.dailyRate.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="weeklyRate">Weekly Rate (₹) *</Label>
                  <Input
                    id="weeklyRate"
                    type="number"
                    {...register('weeklyRate', { valueAsNumber: true })}
                    placeholder="Weekly rate per bed"
                  />
                  {errors.weeklyRate && (
                    <p className="text-sm text-red-600 mt-1">{errors.weeklyRate.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="monthlyRate">Monthly Rate (₹) *</Label>
                  <Input
                    id="monthlyRate"
                    type="number"
                    {...register('monthlyRate', { valueAsNumber: true })}
                    placeholder="Monthly rate per bed"
                  />
                  {errors.monthlyRate && (
                    <p className="text-sm text-red-600 mt-1">{errors.monthlyRate.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="securityDeposit">Security Deposit (₹) *</Label>
                  <Input
                    id="securityDeposit"
                    type="number"
                    {...register('securityDeposit', { valueAsNumber: true })}
                    placeholder="Security deposit amount"
                  />
                  {errors.securityDeposit && (
                    <p className="text-sm text-red-600 mt-1">{errors.securityDeposit.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="cleaningFee">Cleaning Fee (₹)</Label>
                  <Input
                    id="cleaningFee"
                    type="number"
                    {...register('cleaningFee', { valueAsNumber: true })}
                    placeholder="One-time cleaning fee"
                  />
                  {errors.cleaningFee && (
                    <p className="text-sm text-red-600 mt-1">{errors.cleaningFee.message}</p>
                  )}
                </div>
              </div>

              <div>
                <Label htmlFor="reason">Reason for Adding Room *</Label>
                <Textarea
                  id="reason"
                  {...register('reason')}
                  placeholder="Explain why you want to add this room (e.g., increased demand, expansion plans)"
                  rows={3}
                />
                {errors.reason && (
                  <p className="text-sm text-red-600 mt-1">{errors.reason.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="expectedOccupancyIncrease">Expected Occupancy Increase *</Label>
                <Input
                  id="expectedOccupancyIncrease"
                  type="number"
                  {...register('expectedOccupancyIncrease', { valueAsNumber: true })}
                  placeholder="Number of additional guests expected"
                />
                {errors.expectedOccupancyIncrease && (
                  <p className="text-sm text-red-600 mt-1">{errors.expectedOccupancyIncrease.message}</p>
                )}
              </div>
            </TabsContent>

            <TabsContent value="features" className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold mb-4">Room Features</h3>
                <div className="grid grid-cols-2 gap-4">
                  {Object.entries(roomFeatures).map(([feature, value]) => (
                    <div key={feature} className="flex items-center space-x-2">
                      <Checkbox
                        checked={value}
                        onCheckedChange={(checked) => 
                          setRoomFeatures(prev => ({ ...prev, [feature]: !!checked }))
                        }
                      />
                      <Label className="capitalize">
                        {feature.replace(/([A-Z])/g, ' $1').replace(/^has/, 'Has')}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <DialogFooter className="mt-6">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Submitting...
                </>
              ) : (
                'Submit Request'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
