import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Building2,
  Layers,
  Home,
  Bed,
  Plus,
  ArrowRight,
  CheckCircle,
  Clock,
  AlertCircle,
  Users,
  Settings,
  Eye
} from 'lucide-react';
import { HostelInfo, Floor, Room } from '@/types/roomManagement';
import { 
  getHostelsByOwnerId, 
  getFloorsByHostelId, 
  getRoomsByHostelId,
  getRoomsByFloorId 
} from '@/data/mockData';
import { AddFloorDialog } from '@/components/floor/AddFloorDialog';
import { AddRoomDialog } from '@/components/room/AddRoomDialog';
import { toast } from '@/hooks/use-toast';

interface RoomAssignmentManagerProps {
  ownerId: string;
}

export const RoomAssignmentManager: React.FC<RoomAssignmentManagerProps> = ({ ownerId }) => {
  const [hostels, setHostels] = useState<HostelInfo[]>([]);
  const [selectedHostel, setSelectedHostel] = useState<HostelInfo | null>(null);
  const [floors, setFloors] = useState<Floor[]>([]);
  const [selectedFloor, setSelectedFloor] = useState<Floor | null>(null);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [isAddRoomDialogOpen, setIsAddRoomDialogOpen] = useState(false);

  useEffect(() => {
    loadOwnerHostels();
  }, [ownerId]);

  useEffect(() => {
    if (selectedHostel) {
      loadHostelData();
    }
  }, [selectedHostel]);

  useEffect(() => {
    if (selectedFloor) {
      loadFloorRooms();
    }
  }, [selectedFloor]);

  const loadOwnerHostels = () => {
    const ownerHostels = getHostelsByOwnerId(ownerId);
    setHostels(ownerHostels);
    
    if (ownerHostels.length > 0) {
      setSelectedHostel(ownerHostels[0]);
    }
  };

  const loadHostelData = () => {
    if (!selectedHostel) return;
    
    const hostelFloors = getFloorsByHostelId(selectedHostel.id);
    const hostelRooms = getRoomsByHostelId(selectedHostel.id);
    
    setFloors(hostelFloors);
    setRooms(hostelRooms);
    
    if (hostelFloors.length > 0) {
      setSelectedFloor(hostelFloors[0]);
    }
  };

  const loadFloorRooms = () => {
    if (!selectedFloor) return;
    
    const floorRooms = getRoomsByFloorId(selectedFloor.id);
    setRooms(floorRooms);
  };

  const handleHostelChange = (hostelId: string) => {
    const hostel = hostels.find(h => h.id === hostelId);
    if (hostel) {
      setSelectedHostel(hostel);
      setSelectedFloor(null);
    }
  };

  const handleFloorChange = (floorId: string) => {
    const floor = floors.find(f => f.id === floorId);
    if (floor) {
      setSelectedFloor(floor);
    }
  };

  const handleRoomAdded = () => {
    loadHostelData();
    toast({
      title: "Room Request Submitted",
      description: "Your room addition request has been submitted for admin approval.",
    });
  };

  const handleFloorAdded = () => {
    loadHostelData();
    toast({
      title: "Floor Request Submitted", 
      description: "Your floor addition request has been submitted for admin approval.",
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'maintenance':
        return <AlertCircle className="h-4 w-4 text-orange-500" />;
      default:
        return <CheckCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  if (hostels.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <Building2 className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No Approved Hostels</h3>
            <p className="mt-1 text-sm text-gray-500">
              You don't have any approved hostels yet. Submit a hostel registration request to get started.
            </p>
            <Button className="mt-4" onClick={() => window.location.href = '/owner/registration'}>
              Register New Hostel
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Room Assignment Manager</h2>
          <p className="text-gray-600">Assign floors and rooms to your approved hostels</p>
        </div>
        <div className="flex space-x-2">
          {selectedHostel && (
            <>
              <AddFloorDialog
                hostel={selectedHostel}
                ownerId={ownerId}
                onFloorAdded={handleFloorAdded}
                trigger={
                  <Button variant="outline">
                    <Layers className="mr-2 h-4 w-4" />
                    Add Floor
                  </Button>
                }
              />
              <Button onClick={() => setIsAddRoomDialogOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Room
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Step 1: Hostel Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Step 1: Select Your Approved Hostel
          </CardTitle>
          <CardDescription>
            Choose which approved hostel you want to manage
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="hostel-select">Select Hostel</Label>
              <Select value={selectedHostel?.id || ''} onValueChange={handleHostelChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a hostel" />
                </SelectTrigger>
                <SelectContent>
                  {hostels.map((hostel) => (
                    <SelectItem key={hostel.id} value={hostel.id}>
                      <div className="flex items-center space-x-2">
                        <Building2 className="h-4 w-4" />
                        <span>{hostel.name}</span>
                        <span className="text-muted-foreground">- {hostel.city}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {selectedHostel && (
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-blue-900">{selectedHostel.name}</h4>
                    <p className="text-sm text-blue-700">
                      {selectedHostel.address}, {selectedHostel.city}, {selectedHostel.state}
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-blue-900">
                      {selectedHostel.totalFloors} floors • {selectedHostel.totalRooms} rooms
                    </div>
                    <Badge variant="outline" className="bg-blue-100 text-blue-800">
                      ID: {selectedHostel.id}
                    </Badge>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Step 2: Floor Management */}
      {selectedHostel && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Layers className="h-5 w-5" />
              Step 2: Manage Floors in {selectedHostel.name}
            </CardTitle>
            <CardDescription>
              Add floors to your hostel and assign rooms to specific floors
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {floors.length > 0 ? (
                <>
                  <div className="grid gap-2">
                    <Label htmlFor="floor-select">Select Floor</Label>
                    <Select value={selectedFloor?.id || ''} onValueChange={handleFloorChange}>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a floor" />
                      </SelectTrigger>
                      <SelectContent>
                        {floors.map((floor) => (
                          <SelectItem key={floor.id} value={floor.id}>
                            <div className="flex items-center space-x-2">
                              <Layers className="h-4 w-4" />
                              <span>Floor {floor.floorNumber}</span>
                              <span className="text-muted-foreground">
                                ({floor.totalRooms} rooms, {floor.totalBeds} beds)
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid gap-3">
                    {floors.map((floor) => (
                      <div
                        key={floor.id}
                        className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                          selectedFloor?.id === floor.id 
                            ? 'border-green-500 bg-green-50' 
                            : 'hover:bg-gray-50'
                        }`}
                        onClick={() => setSelectedFloor(floor)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            {getStatusIcon(floor.status)}
                            <div>
                              <div className="font-medium">Floor {floor.floorNumber}</div>
                              <div className="text-sm text-gray-500">
                                {floor.floorType} • {floor.totalRooms} rooms • {floor.totalBeds} beds
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline">floorId: {floor.id}</Badge>
                            <Badge variant={floor.status === 'active' ? 'default' : 'secondary'}>
                              {floor.status}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </>
              ) : (
                <div className="text-center py-6">
                  <Layers className="mx-auto h-8 w-8 text-gray-400" />
                  <h4 className="mt-2 text-sm font-medium text-gray-900">No floors added yet</h4>
                  <p className="mt-1 text-sm text-gray-500">
                    Add your first floor to start organizing rooms
                  </p>
                  <AddFloorDialog
                    hostel={selectedHostel}
                    ownerId={ownerId}
                    onFloorAdded={handleFloorAdded}
                    trigger={
                      <Button className="mt-3">
                        <Plus className="mr-2 h-4 w-4" />
                        Add First Floor
                      </Button>
                    }
                  />
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step 3: Room Management */}
      {selectedHostel && selectedFloor && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Home className="h-5 w-5" />
              Step 3: Manage Rooms on Floor {selectedFloor.floorNumber}
            </CardTitle>
            <CardDescription>
              Add and manage rooms on the selected floor
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {rooms.length > 0 ? (
                <div className="grid gap-3">
                  {rooms.map((room) => (
                    <div key={room.id} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Home className="h-4 w-4 text-purple-500" />
                          <div>
                            <div className="font-medium">Room {room.roomNumber}</div>
                            <div className="text-sm text-gray-500">
                              {room.roomType} • {room.beds.length}/{room.capacity} beds • Floor {room.floor}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline">roomId: {room.id}</Badge>
                          <Badge variant="outline">floorId: {room.floorId}</Badge>
                          <Badge variant={room.status === 'active' ? 'default' : 'secondary'}>
                            {room.status}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6">
                  <Home className="mx-auto h-8 w-8 text-gray-400" />
                  <h4 className="mt-2 text-sm font-medium text-gray-900">No rooms on this floor</h4>
                  <p className="mt-1 text-sm text-gray-500">
                    Add your first room to Floor {selectedFloor.floorNumber}
                  </p>
                  <Button className="mt-3" onClick={() => setIsAddRoomDialogOpen(true)}>
                    <Plus className="mr-2 h-4 w-4" />
                    Add First Room
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Assignment Summary */}
      {selectedHostel && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Assignment Summary
            </CardTitle>
            <CardDescription>
              Current assignment structure for {selectedHostel.name}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Building2 className="h-4 w-4 text-blue-500" />
                  <span className="font-medium">Hostel: {selectedHostel.name}</span>
                </div>
                <Badge variant="outline">hostelId: {selectedHostel.id}</Badge>
              </div>
              
              <div className="ml-4 space-y-2">
                {floors.map((floor) => (
                  <div key={floor.id}>
                    <div className="flex items-center justify-between p-2 bg-green-50 rounded">
                      <div className="flex items-center space-x-2">
                        <Layers className="h-4 w-4 text-green-500" />
                        <span>Floor {floor.floorNumber} ({floor.totalRooms} rooms)</span>
                      </div>
                      <Badge variant="outline">floorId: {floor.id}</Badge>
                    </div>
                    
                    <div className="ml-6 mt-1 space-y-1">
                      {getRoomsByFloorId(floor.id).map((room) => (
                        <div key={room.id} className="flex items-center justify-between p-2 bg-purple-50 rounded text-sm">
                          <div className="flex items-center space-x-2">
                            <Home className="h-3 w-3 text-purple-500" />
                            <span>Room {room.roomNumber} ({room.beds.length} beds)</span>
                          </div>
                          <Badge variant="outline" className="text-xs">roomId: {room.id}</Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Add Room Dialog */}
      {selectedHostel && (
        <AddRoomDialog
          isOpen={isAddRoomDialogOpen}
          onClose={() => setIsAddRoomDialogOpen(false)}
          onRoomAdded={handleRoomAdded}
          hostelId={selectedHostel.id}
          ownerId={ownerId}
        />
      )}
    </div>
  );
};
