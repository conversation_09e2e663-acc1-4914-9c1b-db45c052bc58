// Service for handling hostel owner access requests

import { HostelOwnerAccessRequest } from '@/types/admin';
import { User } from '@/data/mockData';
import {
  mockHostelOwnerAccessRequests,
  mockUsers
} from '@/data/mockData';
import { sendEmailNotification, createInAppNotification } from '@/services/notificationService';

export interface OwnerAccessRequestSubmissionData {
  // Personal Information
  name: string;
  email: string;
  phone: string;
  alternatePhone?: string;
  address: string;
  city: string;
  state: string;
  pincode: string;
  
  // Business Information
  businessName?: string;
  businessType: string;
  experienceYears: number;
  previousHostelExperience: boolean;
  numberOfPropertiesOwned?: number;
  estimatedInvestment?: number;
  targetLocation: string;
  expectedLaunchDate?: string;
  
  // Documents
  idProofType: 'aadhar' | 'pan' | 'passport' | 'driving_license';
  idProofNumber: string;
  
  // Additional Information
  motivation: string;
  businessPlan?: string;
  
  // Files will be handled separately
  files?: Record<string, File>;
  references?: Array<{
    name: string;
    relationship: string;
    phone: string;
    email?: string;
  }>;
}

export interface OwnerAccessRequestResult {
  success: boolean;
  requestId?: string;
  error?: string;
}

export interface ApprovalResult {
  success: boolean;
  error?: string;
  generatedCredentials?: {
    userId: string;
    temporaryPassword: string;
  };
}

// Mock file upload service
const uploadFile = async (file: File): Promise<string> => {
  // Simulate file upload delay
  await new Promise(resolve => setTimeout(resolve, 800));
  
  // Return mock file URL
  return `/documents/${file.name}`;
};

// Generate random password
const generateTemporaryPassword = (): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789@#$%';
  let password = '';
  for (let i = 0; i < 12; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
};

// Submit hostel owner access request
export const submitOwnerAccessRequest = async (
  data: OwnerAccessRequestSubmissionData
): Promise<OwnerAccessRequestResult> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Check if email already exists
    const existingUser = mockUsers.find(user => user.email.toLowerCase() === data.email.toLowerCase());
    if (existingUser) {
      return {
        success: false,
        error: 'An account with this email already exists'
      };
    }

    // Check if there's already a pending request with this email
    const existingRequest = mockHostelOwnerAccessRequests.find(
      request => request.applicantDetails.email.toLowerCase() === data.email.toLowerCase() && 
      request.status === 'pending'
    );
    if (existingRequest) {
      return {
        success: false,
        error: 'You already have a pending request with this email'
      };
    }

    // Upload files and get URLs
    const documentUrls: Record<string, string> = {};
    if (data.files) {
      for (const [key, file] of Object.entries(data.files)) {
        documentUrls[key] = await uploadFile(file);
      }
    }

    // Generate unique request ID
    const requestId = `oar_${Date.now()}`;

    // Create hostel owner access request
    const accessRequest: HostelOwnerAccessRequest = {
      id: requestId,
      requestType: 'owner_access',
      submittedAt: new Date().toISOString(),
      status: 'pending',
      applicantDetails: {
        name: data.name,
        email: data.email,
        phone: data.phone,
        alternatePhone: data.alternatePhone,
        address: data.address,
        city: data.city,
        state: data.state,
        pincode: data.pincode
      },
      businessDetails: {
        businessName: data.businessName,
        businessType: data.businessType,
        experienceYears: data.experienceYears,
        previousHostelExperience: data.previousHostelExperience,
        numberOfPropertiesOwned: data.numberOfPropertiesOwned,
        estimatedInvestment: data.estimatedInvestment,
        targetLocation: data.targetLocation,
        expectedLaunchDate: data.expectedLaunchDate
      },
      documents: {
        idProof: {
          type: data.idProofType,
          number: data.idProofNumber,
          fileUrl: documentUrls.idProof,
          verified: false
        },
        addressProof: documentUrls.addressProof ? {
          type: 'utility_bill',
          fileUrl: documentUrls.addressProof,
          verified: false
        } : undefined,
        businessProof: documentUrls.businessProof ? {
          type: 'gst',
          fileUrl: documentUrls.businessProof,
          verified: false
        } : undefined,
        financialProof: documentUrls.financialProof ? {
          type: 'bank_statement',
          fileUrl: documentUrls.financialProof,
          verified: false
        } : undefined
      },
      additionalInfo: {
        motivation: data.motivation,
        businessPlan: data.businessPlan,
        references: data.references
      }
    };

    // Add to mock data (in real app, this would be an API call)
    mockHostelOwnerAccessRequests.push(accessRequest);

    // Notify admin about new request
    notifyNewOwnerAccessRequest(accessRequest);

    return {
      success: true,
      requestId: requestId
    };

  } catch (error) {
    console.error('Error submitting owner access request:', error);
    return {
      success: false,
      error: 'Failed to submit access request'
    };
  }
};

// Approve hostel owner access request
export const approveOwnerAccessRequest = async (
  requestId: string,
  adminId: string,
  notes?: string
): Promise<ApprovalResult> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    const requestIndex = mockHostelOwnerAccessRequests.findIndex(
      request => request.id === requestId
    );

    if (requestIndex === -1) {
      return {
        success: false,
        error: 'Access request not found'
      };
    }

    const request = mockHostelOwnerAccessRequests[requestIndex];

    // Generate temporary password
    const temporaryPassword = generateTemporaryPassword();
    const userId = `owner_${Date.now()}`;

    // Create new user account
    const newUser: User = {
      id: userId,
      name: request.applicantDetails.name,
      email: request.applicantDetails.email,
      phone: request.applicantDetails.phone,
      role: 'owner',
      joinedDate: new Date().toISOString(),
      status: 'active',
      emailVerified: true,
      registrationStatus: 'approved'
    };

    // Add to mock users
    mockUsers.push(newUser);

    // Update request with approval details
    const updatedRequest: HostelOwnerAccessRequest = {
      ...request,
      status: 'approved',
      reviewedBy: adminId,
      reviewedAt: new Date().toISOString(),
      reviewNotes: notes,
      generatedCredentials: {
        userId: userId,
        temporaryPassword: temporaryPassword,
        passwordResetRequired: true
      }
    };

    mockHostelOwnerAccessRequests[requestIndex] = updatedRequest;

    // Send welcome email with credentials
    await sendEmailNotification('OWNER_ACCESS_APPROVED', request.applicantDetails.email, {
      name: request.applicantDetails.name,
      email: request.applicantDetails.email,
      temporaryPassword: temporaryPassword
    });

    // Create in-app notification for admin
    createInAppNotification(
      'success',
      'Owner Access Approved',
      `${request.applicantDetails.name} has been granted owner access`,
      '/admin/registration-approvals'
    );

    return {
      success: true,
      generatedCredentials: {
        userId: userId,
        temporaryPassword: temporaryPassword
      }
    };

  } catch (error) {
    console.error('Error approving owner access request:', error);
    return {
      success: false,
      error: 'Failed to approve access request'
    };
  }
};

// Reject hostel owner access request
export const rejectOwnerAccessRequest = async (
  requestId: string,
  adminId: string,
  rejectionReason: string
): Promise<ApprovalResult> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    const requestIndex = mockHostelOwnerAccessRequests.findIndex(
      request => request.id === requestId
    );

    if (requestIndex === -1) {
      return {
        success: false,
        error: 'Access request not found'
      };
    }

    const request = mockHostelOwnerAccessRequests[requestIndex];

    // Update request with rejection details
    const updatedRequest: HostelOwnerAccessRequest = {
      ...request,
      status: 'rejected',
      reviewedBy: adminId,
      reviewedAt: new Date().toISOString(),
      reviewNotes: rejectionReason
    };

    mockHostelOwnerAccessRequests[requestIndex] = updatedRequest;

    // Send rejection email
    await sendEmailNotification('OWNER_ACCESS_REJECTED', request.applicantDetails.email, {
      name: request.applicantDetails.name,
      rejectionReason: rejectionReason
    });

    // Create in-app notification for admin
    createInAppNotification(
      'info',
      'Owner Access Rejected',
      `${request.applicantDetails.name}'s owner access request has been rejected`,
      '/admin/registration-approvals'
    );

    return { success: true };

  } catch (error) {
    console.error('Error rejecting owner access request:', error);
    return {
      success: false,
      error: 'Failed to reject access request'
    };
  }
};

// Create notification when new request is submitted
export const notifyNewOwnerAccessRequest = (request: HostelOwnerAccessRequest): void => {
  createInAppNotification(
    'info',
    'New Owner Access Request',
    `${request.applicantDetails.name} has submitted a new owner access request`,
    '/admin/registration-approvals'
  );
};

// Get owner access request statistics
export const getOwnerAccessStatistics = async () => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));

  const requests = mockHostelOwnerAccessRequests;

  return {
    total: requests.length,
    pending: requests.filter(r => r.status === 'pending').length,
    approved: requests.filter(r => r.status === 'approved').length,
    rejected: requests.filter(r => r.status === 'rejected').length,
    underReview: requests.filter(r => r.status === 'under_review').length,
    recentRequests: requests
      .sort((a, b) => new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime())
      .slice(0, 5)
      .map(r => ({
        id: r.id,
        name: r.applicantDetails.name,
        email: r.applicantDetails.email,
        status: r.status,
        submittedAt: r.submittedAt
      }))
  };
};
