import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Users, 
  DollarSign,
  Calculator,
  Send,
  CheckCircle,
  Clock,
  AlertTriangle,
  Plus,
  Minus,
  Percent,
  Equal,
  UserPlus,
  Building2,
  Calendar,
  Receipt
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/hooks/use-toast';
import { mockBookings, getHostelById } from '@/data/mockData';
import { SplitPaymentDetails } from '@/types/payment';

// Mock roommate data
interface Roommate {
  id: string;
  name: string;
  email: string;
  phone: string;
  avatar?: string;
  bedNumber: string;
  isActive: boolean;
}

const mockRoommates: Roommate[] = [
  {
    id: 'rm1',
    name: 'Alex Johnson',
    email: '<EMAIL>',
    phone: '+91 9876543210',
    bedNumber: 'A101-2',
    isActive: true
  },
  {
    id: 'rm2',
    name: 'Sarah Wilson',
    email: '<EMAIL>',
    phone: '+91 9876543211',
    bedNumber: 'A101-3',
    isActive: true
  },
  {
    id: 'rm3',
    name: 'Mike Brown',
    email: '<EMAIL>',
    phone: '+91 9876543212',
    bedNumber: 'A101-4',
    isActive: false
  }
];

export const SplitPayment: React.FC = () => {
  const { user } = useAuth();
  const [selectedBooking, setSelectedBooking] = useState<string>('');
  const [totalAmount, setTotalAmount] = useState<string>('');
  const [splitType, setSplitType] = useState<'equal' | 'custom' | 'percentage'>('equal');
  const [selectedRoommates, setSelectedRoommates] = useState<string[]>([]);
  const [customSplits, setCustomSplits] = useState<Record<string, number>>({});
  const [description, setDescription] = useState('');
  const [dueDate, setDueDate] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  // Get user's bookings
  const currentUserId = user?.id || '11'; // Fallback for demo
  const userBookings = mockBookings.filter(b => b.userId === currentUserId && b.status === 'confirmed');
  const currentBooking = userBookings.find(b => b.id === selectedBooking);
  const hostel = currentBooking ? getHostelById(currentBooking.hostelId) : null;

  // Available roommates (same hostel/room)
  const availableRoommates = mockRoommates.filter(rm => rm.isActive);

  const getUserInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const calculateSplits = (): Array<{ userId: string; userName: string; amount: number; percentage: number }> => {
    const amount = parseFloat(totalAmount) || 0;
    const totalParticipants = selectedRoommates.length + 1; // +1 for current user
    const splits = [];

    // Add current user
    if (splitType === 'equal') {
      const equalAmount = amount / totalParticipants;
      splits.push({
        userId: currentUserId,
        userName: user?.name || 'You',
        amount: equalAmount,
        percentage: (equalAmount / amount) * 100
      });
    } else if (splitType === 'custom') {
      const userAmount = customSplits[currentUserId] || 0;
      splits.push({
        userId: currentUserId,
        userName: user?.name || 'You',
        amount: userAmount,
        percentage: (userAmount / amount) * 100
      });
    } else if (splitType === 'percentage') {
      const userPercentage = customSplits[currentUserId] || 0;
      const userAmount = (amount * userPercentage) / 100;
      splits.push({
        userId: currentUserId,
        userName: user?.name || 'You',
        amount: userAmount,
        percentage: userPercentage
      });
    }

    // Add selected roommates
    selectedRoommates.forEach(roommateId => {
      const roommate = availableRoommates.find(rm => rm.id === roommateId);
      if (!roommate) return;

      if (splitType === 'equal') {
        const equalAmount = amount / totalParticipants;
        splits.push({
          userId: roommateId,
          userName: roommate.name,
          amount: equalAmount,
          percentage: (equalAmount / amount) * 100
        });
      } else if (splitType === 'custom') {
        const roommateAmount = customSplits[roommateId] || 0;
        splits.push({
          userId: roommateId,
          userName: roommate.name,
          amount: roommateAmount,
          percentage: (roommateAmount / amount) * 100
        });
      } else if (splitType === 'percentage') {
        const roommatePercentage = customSplits[roommateId] || 0;
        const roommateAmount = (amount * roommatePercentage) / 100;
        splits.push({
          userId: roommateId,
          userName: roommate.name,
          amount: roommateAmount,
          percentage: roommatePercentage
        });
      }
    });

    return splits;
  };

  const splits = calculateSplits();
  const totalSplitAmount = splits.reduce((sum, split) => sum + split.amount, 0);
  const totalSplitPercentage = splits.reduce((sum, split) => sum + split.percentage, 0);
  const isValidSplit = Math.abs(totalSplitAmount - parseFloat(totalAmount || '0')) < 0.01;

  const handleRoommateToggle = (roommateId: string) => {
    setSelectedRoommates(prev => 
      prev.includes(roommateId) 
        ? prev.filter(id => id !== roommateId)
        : [...prev, roommateId]
    );
  };

  const handleCustomSplitChange = (userId: string, value: number) => {
    setCustomSplits(prev => ({
      ...prev,
      [userId]: value
    }));
  };

  const handleCreateSplitPayment = async () => {
    if (!selectedBooking || !totalAmount || selectedRoommates.length === 0) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }

    if (!isValidSplit) {
      toast({
        title: "Error",
        description: "Split amounts don't add up to the total amount",
        variant: "destructive"
      });
      return;
    }

    setIsProcessing(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      const splitPayment: SplitPaymentDetails = {
        isEnabled: true,
        totalAmount: parseFloat(totalAmount),
        splits: splits.map(split => ({
          userId: split.userId,
          userName: split.userName,
          amount: split.amount,
          percentage: split.percentage,
          status: split.userId === currentUserId ? 'paid' : 'pending'
        })),
        initiatedBy: currentUserId,
        splitType,
        dueDate: dueDate || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      };

      console.log('Split payment created:', splitPayment);

      toast({
        title: "Split Payment Created!",
        description: `Payment requests sent to ${selectedRoommates.length} roommate(s)`,
      });

      // Reset form
      setSelectedBooking('');
      setTotalAmount('');
      setSelectedRoommates([]);
      setCustomSplits({});
      setDescription('');
      setDueDate('');

    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create split payment",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold tracking-tight">Split Payment</h1>
          <p className="text-muted-foreground">
            Share payment costs with your roommates easily
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Receipt className="mr-2 h-4 w-4" />
            View Split History
          </Button>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Split Payment Form */}
        <div className="lg:col-span-2 space-y-6">
          {/* Booking Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Building2 className="h-5 w-5" />
                <span>Select Booking</span>
              </CardTitle>
              <CardDescription>
                Choose the booking for which you want to split payment
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="booking">Active Bookings</Label>
                <Select value={selectedBooking} onValueChange={setSelectedBooking}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a booking" />
                  </SelectTrigger>
                  <SelectContent>
                    {userBookings.map((booking) => {
                      const hostelInfo = getHostelById(booking.hostelId);
                      return (
                        <SelectItem key={booking.id} value={booking.id}>
                          <div className="flex items-center space-x-2">
                            <span>{hostelInfo?.name}</span>
                            <Badge variant="outline">{booking.bedNumber}</Badge>
                          </div>
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>

              {currentBooking && hostel && (
                <div className="p-4 bg-muted rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Hostel:</span> {hostel.name}
                    </div>
                    <div>
                      <span className="font-medium">Room:</span> {currentBooking.bedNumber}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Amount and Split Type */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <DollarSign className="h-5 w-5" />
                <span>Payment Details</span>
              </CardTitle>
              <CardDescription>
                Enter the total amount and choose how to split it
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="amount">Total Amount (₹)</Label>
                  <Input
                    id="amount"
                    type="number"
                    placeholder="Enter total amount"
                    value={totalAmount}
                    onChange={(e) => setTotalAmount(e.target.value)}
                    min="1"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="splitType">Split Type</Label>
                  <Select value={splitType} onValueChange={(value: any) => setSplitType(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="equal">
                        <div className="flex items-center space-x-2">
                          <Equal className="h-4 w-4" />
                          <span>Equal Split</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="custom">
                        <div className="flex items-center space-x-2">
                          <Calculator className="h-4 w-4" />
                          <span>Custom Amount</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="percentage">
                        <div className="flex items-center space-x-2">
                          <Percent className="h-4 w-4" />
                          <span>Percentage</span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description (Optional)</Label>
                <Input
                  id="description"
                  placeholder="e.g., Monthly electricity bill"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="dueDate">Due Date (Optional)</Label>
                <Input
                  id="dueDate"
                  type="date"
                  value={dueDate}
                  onChange={(e) => setDueDate(e.target.value)}
                />
              </div>
            </CardContent>
          </Card>

          {/* Roommate Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>Select Roommates</span>
              </CardTitle>
              <CardDescription>
                Choose who to split the payment with
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4">
                {availableRoommates.map((roommate) => (
                  <div
                    key={roommate.id}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedRoommates.includes(roommate.id)
                        ? 'border-primary bg-primary/5'
                        : 'border-muted hover:border-primary/50'
                    }`}
                    onClick={() => handleRoommateToggle(roommate.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Avatar>
                          <AvatarImage src={roommate.avatar} />
                          <AvatarFallback>{getUserInitials(roommate.name)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{roommate.name}</div>
                          <div className="text-sm text-muted-foreground">{roommate.email}</div>
                          <div className="text-xs text-muted-foreground">Bed: {roommate.bedNumber}</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {selectedRoommates.includes(roommate.id) && (
                          <CheckCircle className="h-5 w-5 text-primary" />
                        )}
                      </div>
                    </div>

                    {/* Custom split input */}
                    {selectedRoommates.includes(roommate.id) && splitType !== 'equal' && (
                      <div className="mt-3 pt-3 border-t">
                        <div className="flex items-center space-x-2">
                          <Label className="text-xs">
                            {splitType === 'custom' ? 'Amount (₹)' : 'Percentage (%)'}
                          </Label>
                          <Input
                            type="number"
                            size="sm"
                            className="w-24"
                            placeholder={splitType === 'custom' ? '0' : '0%'}
                            value={customSplits[roommate.id] || ''}
                            onChange={(e) => handleCustomSplitChange(roommate.id, parseFloat(e.target.value) || 0)}
                            onClick={(e) => e.stopPropagation()}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {availableRoommates.length === 0 && (
                <div className="text-center py-8">
                  <Users className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No roommates found</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    No active roommates available for splitting payments.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Split Summary Sidebar */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Calculator className="h-5 w-5" />
                <span>Split Summary</span>
              </CardTitle>
              <CardDescription>
                Review the payment breakdown
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {!totalAmount || selectedRoommates.length === 0 ? (
                <div className="text-center py-8">
                  <Calculator className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <p className="text-muted-foreground text-sm">
                    Enter amount and select roommates to see split breakdown
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="space-y-3">
                    {splits.map((split, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-8 w-8">
                            <AvatarFallback className="text-xs">
                              {split.userId === currentUserId ? 'You' : getUserInitials(split.userName)}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium text-sm">
                              {split.userId === currentUserId ? 'You' : split.userName}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {split.percentage.toFixed(1)}%
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">₹{split.amount.toFixed(2)}</div>
                          {split.userId === currentUserId ? (
                            <Badge className="bg-green-100 text-green-800 text-xs">Your share</Badge>
                          ) : (
                            <Badge variant="outline" className="text-xs">Pending</Badge>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Total Amount</span>
                      <span>₹{parseFloat(totalAmount || '0').toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Split Total</span>
                      <span className={isValidSplit ? 'text-green-600' : 'text-red-600'}>
                        ₹{totalSplitAmount.toFixed(2)}
                      </span>
                    </div>
                    {splitType === 'percentage' && (
                      <div className="flex justify-between text-sm">
                        <span>Total Percentage</span>
                        <span className={Math.abs(totalSplitPercentage - 100) < 0.01 ? 'text-green-600' : 'text-red-600'}>
                          {totalSplitPercentage.toFixed(1)}%
                        </span>
                      </div>
                    )}
                  </div>

                  {!isValidSplit && (
                    <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <AlertTriangle className="h-4 w-4 text-red-600" />
                        <span className="text-sm text-red-800">
                          Split amounts don't match total
                        </span>
                      </div>
                    </div>
                  )}

                  <Button
                    className="w-full"
                    onClick={handleCreateSplitPayment}
                    disabled={isProcessing || !isValidSplit || !selectedBooking || selectedRoommates.length === 0}
                  >
                    {isProcessing ? (
                      <>
                        <Clock className="mr-2 h-4 w-4 animate-spin" />
                        Creating Split...
                      </>
                    ) : (
                      <>
                        <Send className="mr-2 h-4 w-4" />
                        Create Split Payment
                      </>
                    )}
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Split Payment Info */}
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <Users className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div className="space-y-1">
                    <div className="text-sm font-medium">How it works</div>
                    <div className="text-xs text-muted-foreground">
                      Selected roommates will receive payment requests via email and app notifications
                    </div>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <Clock className="h-5 w-5 text-yellow-600 mt-0.5" />
                  <div className="space-y-1">
                    <div className="text-sm font-medium">Payment Timeline</div>
                    <div className="text-xs text-muted-foreground">
                      Roommates have 7 days to complete their payment (or custom due date)
                    </div>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                  <div className="space-y-1">
                    <div className="text-sm font-medium">Automatic Tracking</div>
                    <div className="text-xs text-muted-foreground">
                      You'll be notified when roommates complete their payments
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
