import React, { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Building2,
  Bed,
  Users,
  DollarSign,
  MapPin,
  Wifi,
  Car,
  Utensils,
  Shield,
  MoreHorizontal,
  Edit,
  Settings,
  Eye,
  CheckCircle,
  AlertCircle,
  Clock,
  User
} from 'lucide-react';
import { Room, Bed as BedType } from '@/types/roomManagement';
import { updateBedStatus } from '@/services/roomManagementService';
import { toast } from '@/hooks/use-toast';

interface RoomDetailsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  room: Room | null;
}

export const RoomDetailsDialog: React.FC<RoomDetailsDialogProps> = ({
  isOpen,
  onClose,
  room
}) => {
  const [isUpdatingBed, setIsUpdatingBed] = useState<string | null>(null);

  if (!room) return null;

  const getStatusBadge = (status: Room['status']) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case 'maintenance':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Maintenance</Badge>;
      case 'inactive':
        return <Badge variant="destructive">Inactive</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getBedStatusBadge = (status: BedType['status']) => {
    switch (status) {
      case 'occupied':
        return <Badge className="bg-blue-100 text-blue-800">Occupied</Badge>;
      case 'available':
        return <Badge className="bg-green-100 text-green-800">Available</Badge>;
      case 'maintenance':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700">Maintenance</Badge>;
      case 'reserved':
        return <Badge variant="outline" className="bg-purple-50 text-purple-700">Reserved</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const handleBedStatusChange = async (bedId: string, newStatus: BedType['status']) => {
    setIsUpdatingBed(bedId);
    
    try {
      const result = await updateBedStatus(bedId, newStatus);
      
      if (result.success) {
        toast({
          title: "Bed Status Updated",
          description: "The bed status has been updated successfully.",
        });
        // In a real app, you would refresh the room data here
      } else {
        toast({
          title: "Update Failed",
          description: result.error || "Failed to update bed status",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error updating bed status:', error);
      toast({
        title: "Update Failed",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsUpdatingBed(null);
    }
  };

  const occupancyRate = room.capacity > 0 ? (room.currentOccupancy / room.capacity) * 100 : 0;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Room {room.roomNumber} Details
          </DialogTitle>
          <DialogDescription>
            Complete information about room {room.roomNumber}
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="beds">Beds</TabsTrigger>
            <TabsTrigger value="pricing">Pricing</TabsTrigger>
            <TabsTrigger value="features">Features</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {/* Basic Info */}
            <div className="grid grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Room Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Room Number:</span>
                    <span className="text-sm font-medium">{room.roomNumber}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Type:</span>
                    <Badge variant="outline">{room.roomType}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Floor:</span>
                    <span className="text-sm font-medium">{room.floor}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Area:</span>
                    <span className="text-sm font-medium">{room.details.area} sq ft</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Status:</span>
                    {getStatusBadge(room.status)}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Occupancy</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Capacity:</span>
                    <span className="text-sm font-medium">{room.capacity} beds</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Occupied:</span>
                    <span className="text-sm font-medium">{room.currentOccupancy} beds</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Available:</span>
                    <span className="text-sm font-medium">{room.capacity - room.currentOccupancy} beds</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Occupancy Rate:</span>
                    <span className="text-sm font-medium">{occupancyRate.toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${occupancyRate}%` }}
                    ></div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Amenities */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Amenities</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {room.amenities.map((amenity) => (
                    <Badge key={amenity} variant="outline">
                      {amenity}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Description */}
            {room.details.description && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Description</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">{room.details.description}</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="beds" className="space-y-4">
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Bed Number</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Size</TableHead>
                    <TableHead>Locker</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Occupant</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {room.beds.map((bed) => (
                    <TableRow key={bed.id}>
                      <TableCell className="font-medium">{bed.bedNumber}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{bed.bedType.replace('_', ' ')}</Badge>
                      </TableCell>
                      <TableCell>{bed.size}</TableCell>
                      <TableCell>
                        {bed.hasLocker ? (
                          <div className="flex items-center space-x-1">
                            <CheckCircle className="h-3 w-3 text-green-600" />
                            <span className="text-xs">{bed.lockerNumber}</span>
                          </div>
                        ) : (
                          <span className="text-xs text-muted-foreground">No</span>
                        )}
                      </TableCell>
                      <TableCell>{getBedStatusBadge(bed.status)}</TableCell>
                      <TableCell>
                        {bed.occupiedBy ? (
                          <div className="flex items-center space-x-1">
                            <User className="h-3 w-3" />
                            <span className="text-xs">{bed.occupiedBy}</span>
                          </div>
                        ) : bed.reservedBy ? (
                          <div className="flex items-center space-x-1">
                            <Clock className="h-3 w-3 text-purple-600" />
                            <span className="text-xs">Reserved</span>
                          </div>
                        ) : (
                          <span className="text-xs text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button 
                              variant="ghost" 
                              className="h-8 w-8 p-0"
                              disabled={isUpdatingBed === bed.id}
                            >
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Change Status</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              onClick={() => handleBedStatusChange(bed.id, 'available')}
                              disabled={bed.status === 'available'}
                            >
                              Mark Available
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => handleBedStatusChange(bed.id, 'maintenance')}
                              disabled={bed.status === 'maintenance'}
                            >
                              Mark Maintenance
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => handleBedStatusChange(bed.id, 'occupied')}
                              disabled={bed.status === 'occupied'}
                            >
                              Mark Occupied
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </TabsContent>

          <TabsContent value="pricing" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Room Rates</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Daily Rate:</span>
                    <span className="text-sm font-medium">₹{room.pricing.dailyRate}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Weekly Rate:</span>
                    <span className="text-sm font-medium">₹{room.pricing.weeklyRate}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Monthly Rate:</span>
                    <span className="text-sm font-medium">₹{room.pricing.monthlyRate}</span>
                  </div>
                  {room.pricing.hourlyRate && (
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Hourly Rate:</span>
                      <span className="text-sm font-medium">₹{room.pricing.hourlyRate}</span>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Additional Charges</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Security Deposit:</span>
                    <span className="text-sm font-medium">₹{room.pricing.securityDeposit}</span>
                  </div>
                  {room.pricing.cleaningFee && (
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Cleaning Fee:</span>
                      <span className="text-sm font-medium">₹{room.pricing.cleaningFee}</span>
                    </div>
                  )}
                  {room.pricing.maintenanceFee && (
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Maintenance Fee:</span>
                      <span className="text-sm font-medium">₹{room.pricing.maintenanceFee}</span>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Revenue Calculation */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Revenue Potential</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center">
                    <p className="text-sm text-muted-foreground">Daily (Full Occupancy)</p>
                    <p className="text-lg font-semibold">₹{(room.pricing.dailyRate * room.capacity).toLocaleString()}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-muted-foreground">Monthly (Full Occupancy)</p>
                    <p className="text-lg font-semibold">₹{(room.pricing.monthlyRate * room.capacity).toLocaleString()}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-muted-foreground">Current Monthly</p>
                    <p className="text-lg font-semibold">₹{(room.pricing.monthlyRate * room.currentOccupancy).toLocaleString()}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="features" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Room Features</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Window:</span>
                    <span className="text-sm">{room.details.hasWindow ? '✓' : '✗'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Balcony:</span>
                    <span className="text-sm">{room.details.hasBalcony ? '✓' : '✗'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Attached Bathroom:</span>
                    <span className="text-sm">{room.details.hasAttachedBathroom ? '✓' : '✗'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Bathroom Type:</span>
                    <span className="text-sm">{room.details.bathroomType}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Amenities</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">AC:</span>
                    <span className="text-sm">{room.details.hasAC ? '✓' : '✗'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Fan:</span>
                    <span className="text-sm">{room.details.hasFan ? '✓' : '✗'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">WiFi:</span>
                    <span className="text-sm">{room.details.hasWiFi ? '✓' : '✗'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">TV:</span>
                    <span className="text-sm">{room.details.hasTV ? '✓' : '✗'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Refrigerator:</span>
                    <span className="text-sm">{room.details.hasRefrigerator ? '✓' : '✗'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Wardrobe:</span>
                    <span className="text-sm">{room.details.hasWardrobe ? '✓' : '✗'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Study Table:</span>
                    <span className="text-sm">{room.details.hasStudyTable ? '✓' : '✗'}</span>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Special Features */}
            {room.details.specialFeatures && room.details.specialFeatures.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Special Features</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {room.details.specialFeatures.map((feature, index) => (
                      <Badge key={index} variant="outline">
                        {feature}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Restrictions */}
            {room.details.restrictions && room.details.restrictions.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Restrictions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-1">
                    {room.details.restrictions.map((restriction, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <AlertCircle className="h-3 w-3 text-yellow-600" />
                        <span className="text-sm text-muted-foreground">{restriction}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};
