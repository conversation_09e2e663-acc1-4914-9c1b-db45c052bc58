import React, { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Plus, X } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { Room, BedType, BedSize } from '@/types/roomManagement';
import { addBedsToRoom } from '@/services/roomManagementService';

interface BedConfig {
  bedNumber: string;
  bedType: BedType;
  size: BedSize;
  hasLocker: boolean;
  lockerNumber?: string;
}

interface AddBedDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onBedAdded: () => void;
  room: Room | null;
  ownerId: string;
}

export const AddBedDialog: React.FC<AddBedDialogProps> = ({
  isOpen,
  onClose,
  onBedAdded,
  room,
  ownerId
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [bedConfigs, setBedConfigs] = useState<BedConfig[]>([
    {
      bedNumber: '',
      bedType: 'single',
      size: 'single',
      hasLocker: true,
      lockerNumber: ''
    }
  ]);

  React.useEffect(() => {
    if (room && isOpen) {
      // Auto-generate bed numbers based on existing beds
      const existingBedCount = room.beds.length;
      const nextBedLetter = String.fromCharCode(65 + existingBedCount); // A, B, C, etc.
      setBedConfigs([{
        bedNumber: `${room.roomNumber}${nextBedLetter}`,
        bedType: 'single',
        size: 'single',
        hasLocker: true,
        lockerNumber: `${room.roomNumber}${nextBedLetter}`
      }]);
    }
  }, [room, isOpen]);

  const addBedConfig = () => {
    if (!room) return;
    
    const existingBedCount = room.beds.length + bedConfigs.length;
    const nextBedLetter = String.fromCharCode(65 + existingBedCount);
    
    setBedConfigs(prev => [...prev, {
      bedNumber: `${room.roomNumber}${nextBedLetter}`,
      bedType: 'single',
      size: 'single',
      hasLocker: true,
      lockerNumber: `${room.roomNumber}${nextBedLetter}`
    }]);
  };

  const removeBedConfig = (index: number) => {
    setBedConfigs(prev => prev.filter((_, i) => i !== index));
  };

  const updateBedConfig = (index: number, field: keyof BedConfig, value: any) => {
    setBedConfigs(prev => prev.map((bed, i) => 
      i === index ? { ...bed, [field]: value } : bed
    ));
  };

  const handleSubmit = async () => {
    if (!room) return;

    // Validate bed configurations
    const invalidBeds = bedConfigs.filter(bed => 
      !bed.bedNumber.trim() || (bed.hasLocker && !bed.lockerNumber?.trim())
    );

    if (invalidBeds.length > 0) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required bed details",
        variant: "destructive"
      });
      return;
    }

    // Check for duplicate bed numbers
    const bedNumbers = bedConfigs.map(bed => bed.bedNumber);
    const existingBedNumbers = room.beds.map(bed => bed.bedNumber);
    const allBedNumbers = [...bedNumbers, ...existingBedNumbers];
    const duplicates = bedNumbers.filter((num, index) => 
      allBedNumbers.indexOf(num) !== allBedNumbers.lastIndexOf(num) || 
      bedNumbers.indexOf(num) !== index
    );

    if (duplicates.length > 0) {
      toast({
        title: "Validation Error",
        description: `Duplicate bed numbers found: ${duplicates.join(', ')}`,
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const bedsData = bedConfigs.map(bed => ({
        bedNumber: bed.bedNumber,
        bedType: bed.bedType,
        status: 'available' as const,
        size: bed.size,
        hasLocker: bed.hasLocker,
        lockerNumber: bed.hasLocker ? bed.lockerNumber : undefined
      }));

      const result = await addBedsToRoom(room.id, ownerId, bedsData);

      if (result.success) {
        if (result.requestId) {
          toast({
            title: "Request Submitted",
            description: "Your bed addition request has been submitted for admin approval.",
          });
        } else {
          toast({
            title: "Beds Added Successfully",
            description: `${bedConfigs.length} bed(s) have been added to room ${room.roomNumber}.`,
          });
        }
        onBedAdded();
        handleClose();
      } else {
        toast({
          title: "Addition Failed",
          description: result.error || "Failed to add beds to room",
          variant: "destructive"
        });
      }

    } catch (error) {
      console.error('Error adding beds to room:', error);
      toast({
        title: "Addition Failed",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setBedConfigs([{
      bedNumber: '',
      bedType: 'single',
      size: 'single',
      hasLocker: true,
      lockerNumber: ''
    }]);
    onClose();
  };

  if (!room) return null;

  const totalBedsAfterAddition = room.beds.length + bedConfigs.length;
  const requiresApproval = bedConfigs.length > 2 || totalBedsAfterAddition > 6;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add Beds to Room {room.roomNumber}</DialogTitle>
          <DialogDescription>
            Add new beds to this room. {requiresApproval && 
              "Adding more than 2 beds or exceeding 6 total beds requires admin approval."
            }
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Room Info */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Current Room Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <p className="font-medium">Current Beds</p>
                  <p className="text-muted-foreground">{room.beds.length}</p>
                </div>
                <div>
                  <p className="font-medium">Capacity</p>
                  <p className="text-muted-foreground">{room.capacity}</p>
                </div>
                <div>
                  <p className="font-medium">Occupancy</p>
                  <p className="text-muted-foreground">{room.currentOccupancy}/{room.capacity}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Approval Warning */}
          {requiresApproval && (
            <Card className="border-yellow-200 bg-yellow-50">
              <CardContent className="pt-4">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                  <p className="text-sm text-yellow-800">
                    This request will require admin approval due to the number of beds being added.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Bed Configurations */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Bed Configuration</h3>
              <Button type="button" variant="outline" size="sm" onClick={addBedConfig}>
                <Plus className="mr-2 h-4 w-4" />
                Add Bed
              </Button>
            </div>

            {bedConfigs.map((bed, index) => (
              <Card key={index}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-sm">Bed {index + 1}</CardTitle>
                    {bedConfigs.length > 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeBedConfig(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </CardHeader>
                <CardContent className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Bed Number *</Label>
                    <Input
                      value={bed.bedNumber}
                      onChange={(e) => updateBedConfig(index, 'bedNumber', e.target.value)}
                      placeholder="e.g., 101A"
                    />
                  </div>

                  <div>
                    <Label>Bed Type</Label>
                    <Select 
                      value={bed.bedType} 
                      onValueChange={(value) => updateBedConfig(index, 'bedType', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="single">Single</SelectItem>
                        <SelectItem value="double">Double</SelectItem>
                        <SelectItem value="bunk_top">Bunk Top</SelectItem>
                        <SelectItem value="bunk_bottom">Bunk Bottom</SelectItem>
                        <SelectItem value="queen">Queen</SelectItem>
                        <SelectItem value="king">King</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>Bed Size</Label>
                    <Select 
                      value={bed.size} 
                      onValueChange={(value) => updateBedConfig(index, 'size', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="single">Single</SelectItem>
                        <SelectItem value="double">Double</SelectItem>
                        <SelectItem value="queen">Queen</SelectItem>
                        <SelectItem value="king">King</SelectItem>
                        <SelectItem value="super_king">Super King</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      checked={bed.hasLocker}
                      onCheckedChange={(checked) => updateBedConfig(index, 'hasLocker', !!checked)}
                    />
                    <Label>Has Locker</Label>
                  </div>

                  {bed.hasLocker && (
                    <div className="col-span-2">
                      <Label>Locker Number</Label>
                      <Input
                        value={bed.lockerNumber || ''}
                        onChange={(e) => updateBedConfig(index, 'lockerNumber', e.target.value)}
                        placeholder="e.g., 101A"
                      />
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <p className="font-medium">Beds to Add</p>
                  <p className="text-muted-foreground">{bedConfigs.length}</p>
                </div>
                <div>
                  <p className="font-medium">Total After Addition</p>
                  <p className="text-muted-foreground">{totalBedsAfterAddition}</p>
                </div>
                <div>
                  <p className="font-medium">Status</p>
                  <p className="text-muted-foreground">
                    {requiresApproval ? 'Requires Approval' : 'Immediate'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting || bedConfigs.length === 0}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {requiresApproval ? 'Submitting Request...' : 'Adding Beds...'}
              </>
            ) : (
              requiresApproval ? 'Submit for Approval' : 'Add Beds'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
