import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Building2, 
  Search, 
  Plus,
  Eye,
  Edit,
  Settings,
  MapPin,
  Users,
  Bed,
  Star,
  DollarSign,
  TrendingUp,
  Calendar
} from 'lucide-react';
import { mockHostels, mockBookings, mockPayments } from '@/data/mockData';

export const MyHostels: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  
  // In a real app, this would filter by the current owner's ID
  // For demo purposes, we'll show hostels owned by the logged-in owner
  const ownerHostels = mockHostels.filter(hostel => 
    hostel.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    hostel.city.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getOccupancyRate = (hostel: typeof mockHostels[0]) => {
    const occupied = hostel.totalBeds - hostel.availableBeds;
    return ((occupied / hostel.totalBeds) * 100).toFixed(0);
  };

  const getMonthlyRevenue = (hostelId: string) => {
    // Calculate monthly revenue for this hostel
    const hostelPayments = mockPayments.filter(payment => {
      const booking = mockBookings.find(b => b.id === payment.bookingId);
      return booking?.hostelId === hostelId;
    });
    return hostelPayments.reduce((sum, payment) => sum + payment.splitDetails.hostelOwner, 0);
  };

  const getTotalRevenue = () => {
    return ownerHostels.reduce((sum, hostel) => sum + getMonthlyRevenue(hostel.id), 0);
  };

  const getTotalBeds = () => {
    return ownerHostels.reduce((sum, hostel) => sum + hostel.totalBeds, 0);
  };

  const getAverageOccupancy = () => {
    if (ownerHostels.length === 0) return 0;
    const totalOccupancy = ownerHostels.reduce((sum, hostel) => sum + parseInt(getOccupancyRate(hostel)), 0);
    return (totalOccupancy / ownerHostels.length).toFixed(0);
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">My Hostels</h1>
          <p className="text-muted-foreground">
            Manage and monitor your hostel properties
          </p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Add New Hostel
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Hostels</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{ownerHostels.length}</div>
            <p className="text-xs text-muted-foreground">
              Active properties
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Beds</CardTitle>
            <Bed className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{getTotalBeds()}</div>
            <p className="text-xs text-muted-foreground">
              Across all hostels
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Occupancy</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{getAverageOccupancy()}%</div>
            <p className="text-xs text-muted-foreground">
              Current average
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{(getTotalRevenue() / 1000).toFixed(0)}K</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="inline h-3 w-3 mr-1" />
              +12% from last month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card>
        <CardHeader>
          <CardTitle>Hostel Portfolio</CardTitle>
          <CardDescription>
            Overview of all your hostel properties
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="relative mb-6">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search your hostels..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
          </div>

          {/* Hostels Grid */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {ownerHostels.map((hostel) => (
              <Card key={hostel.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <CardTitle className="text-lg">{hostel.name}</CardTitle>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <MapPin className="mr-1 h-3 w-3" />
                        {hostel.city}, {hostel.state}
                      </div>
                    </div>
                    <Badge className={hostel.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                      {hostel.status}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Key Metrics */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-1">
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Bed className="mr-1 h-3 w-3" />
                        Capacity
                      </div>
                      <div className="text-lg font-semibold">{hostel.totalBeds} beds</div>
                    </div>
                    <div className="space-y-1">
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Users className="mr-1 h-3 w-3" />
                        Occupancy
                      </div>
                      <div className="text-lg font-semibold">{getOccupancyRate(hostel)}%</div>
                    </div>
                  </div>

                  {/* Occupancy Bar */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Occupancy</span>
                      <span>{hostel.totalBeds - hostel.availableBeds}/{hostel.totalBeds}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                        style={{ width: `${getOccupancyRate(hostel)}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* Rating and Revenue */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <span className="text-sm font-medium">{hostel.rating}</span>
                      <span className="text-sm text-muted-foreground">rating</span>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-muted-foreground">Monthly Revenue</div>
                      <div className="text-sm font-semibold">₹{(getMonthlyRevenue(hostel.id) / 1000).toFixed(0)}K</div>
                    </div>
                  </div>

                  {/* Amenities */}
                  <div className="space-y-2">
                    <div className="text-sm text-muted-foreground">Amenities</div>
                    <div className="flex flex-wrap gap-1">
                      {hostel.amenities.slice(0, 3).map((amenity, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {amenity}
                        </Badge>
                      ))}
                      {hostel.amenities.length > 3 && (
                        <Badge variant="secondary" className="text-xs">
                          +{hostel.amenities.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex space-x-2 pt-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Eye className="mr-1 h-3 w-3" />
                      View
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      <Edit className="mr-1 h-3 w-3" />
                      Edit
                    </Button>
                    <Button variant="outline" size="sm">
                      <Settings className="h-3 w-3" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {ownerHostels.length === 0 && (
            <div className="text-center py-12">
              <Building2 className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-lg font-medium text-gray-900">No hostels found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm ? 'Try adjusting your search criteria.' : 'Get started by adding your first hostel.'}
              </p>
              {!searchTerm && (
                <Button className="mt-4">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Your First Hostel
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
