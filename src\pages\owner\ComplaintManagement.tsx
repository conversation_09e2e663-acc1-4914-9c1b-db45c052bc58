import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  MessageSquare, 
  Search, 
  MoreHorizontal,
  Eye,
  CheckCircle,
  Clock,
  AlertTriangle,
  XCircle,
  Calendar,
  User,
  Filter,
  Send,
  Phone,
  Mail,
  MapPin,
  Wrench,
  Utensils,
  Volume2,
  Trash2,
  Settings,
  Flag
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { mockComplaints, mockUsers, getUserById } from '@/data/mockData';

// Enhanced complaint interface for owner view
interface OwnerComplaint {
  id: string;
  userId: string;
  title: string;
  description: string;
  category: 'room' | 'food' | 'maintenance' | 'cleanliness' | 'noise' | 'service' | 'other';
  subcategory?: string;
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdDate: string;
  resolvedDate?: string;
  assignedTo?: string;
  ownerResponse?: string;
  memberDetails: {
    name: string;
    email: string;
    phone: string;
    roomNumber: string;
    bedNumber: string;
    membershipType: 'regular' | 'guest';
  };
  resolutionNotes?: string;
  escalatedToAdmin?: boolean;
}

// Mock enhanced complaints data
const mockOwnerComplaints: OwnerComplaint[] = [
  {
    id: '1',
    userId: '11',
    title: 'AC not working in room A101',
    description: 'The air conditioning unit has stopped working since yesterday. The room is getting very hot and uncomfortable.',
    category: 'room',
    subcategory: 'ac_heating',
    status: 'in_progress',
    priority: 'high',
    createdDate: '2024-01-22T10:30:00Z',
    assignedTo: 'Maintenance Team',
    memberDetails: {
      name: 'Rohit Verma',
      email: '<EMAIL>',
      phone: '+91 9876543220',
      roomNumber: 'A101',
      bedNumber: 'A101-1',
      membershipType: 'regular'
    },
    ownerResponse: 'We have assigned our maintenance team to check the AC unit. They will visit your room today by 3 PM.',
    escalatedToAdmin: false
  },
  {
    id: '2',
    userId: '12',
    title: 'Food quality issues in dinner',
    description: 'The food served for dinner yesterday was not fresh and had a strange taste. Multiple residents complained about stomach issues.',
    category: 'food',
    subcategory: 'quality',
    status: 'open',
    priority: 'urgent',
    createdDate: '2024-01-22T08:15:00Z',
    memberDetails: {
      name: 'Sneha Patel',
      email: '<EMAIL>',
      phone: '+91 9876543221',
      roomNumber: 'B205',
      bedNumber: 'B205-2',
      membershipType: 'regular'
    },
    escalatedToAdmin: true
  },
  {
    id: '3',
    userId: '13',
    title: 'Noisy neighbors disturbing sleep',
    description: 'The residents in the adjacent room are playing loud music and talking loudly even after 11 PM. This is affecting my sleep.',
    category: 'noise',
    subcategory: 'roommate_noise',
    status: 'resolved',
    priority: 'medium',
    createdDate: '2024-01-21T23:45:00Z',
    resolvedDate: '2024-01-22T09:00:00Z',
    memberDetails: {
      name: 'Amit Kumar',
      email: '<EMAIL>',
      phone: '+91 9876543222',
      roomNumber: 'C301',
      bedNumber: 'C301-1',
      membershipType: 'guest'
    },
    ownerResponse: 'We have spoken to the residents and reminded them about quiet hours. The issue has been resolved.',
    resolutionNotes: 'Spoke to room C302 residents about noise policy. They apologized and agreed to follow quiet hours.',
    escalatedToAdmin: false
  }
];

export const ComplaintManagement: React.FC = () => {
  const { user } = useAuth();
  const [complaints, setComplaints] = useState<OwnerComplaint[]>(mockOwnerComplaints);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | OwnerComplaint['status']>('all');
  const [categoryFilter, setCategoryFilter] = useState<'all' | OwnerComplaint['category']>('all');
  const [priorityFilter, setPriorityFilter] = useState<'all' | OwnerComplaint['priority']>('all');
  const [selectedComplaint, setSelectedComplaint] = useState<OwnerComplaint | null>(null);
  const [responseText, setResponseText] = useState('');
  const [resolutionNotes, setResolutionNotes] = useState('');

  // Filter complaints
  const filteredComplaints = complaints.filter(complaint => {
    const matchesSearch = complaint.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         complaint.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         complaint.memberDetails.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || complaint.status === statusFilter;
    const matchesCategory = categoryFilter === 'all' || complaint.category === categoryFilter;
    const matchesPriority = priorityFilter === 'all' || complaint.priority === priorityFilter;
    
    return matchesSearch && matchesStatus && matchesCategory && matchesPriority;
  });

  const getCategoryIcon = (category: OwnerComplaint['category']) => {
    switch (category) {
      case 'room': return <Settings className="h-4 w-4" />;
      case 'food': return <Utensils className="h-4 w-4" />;
      case 'maintenance': return <Wrench className="h-4 w-4" />;
      case 'noise': return <Volume2 className="h-4 w-4" />;
      case 'cleanliness': return <Trash2 className="h-4 w-4" />;
      default: return <MessageSquare className="h-4 w-4" />;
    }
  };

  const getCategoryColor = (category: OwnerComplaint['category']) => {
    switch (category) {
      case 'room': return 'bg-blue-100 text-blue-800';
      case 'food': return 'bg-green-100 text-green-800';
      case 'maintenance': return 'bg-orange-100 text-orange-800';
      case 'noise': return 'bg-purple-100 text-purple-800';
      case 'cleanliness': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: OwnerComplaint['status']) => {
    switch (status) {
      case 'open': return <AlertTriangle className="h-4 w-4" />;
      case 'in_progress': return <Clock className="h-4 w-4" />;
      case 'resolved': return <CheckCircle className="h-4 w-4" />;
      case 'closed': return <XCircle className="h-4 w-4" />;
      default: return <MessageSquare className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: OwnerComplaint['status']) => {
    switch (status) {
      case 'open': return 'bg-red-100 text-red-800';
      case 'in_progress': return 'bg-yellow-100 text-yellow-800';
      case 'resolved': return 'bg-green-100 text-green-800';
      case 'closed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: OwnerComplaint['priority']) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleStatusUpdate = (complaintId: string, newStatus: OwnerComplaint['status']) => {
    setComplaints(prev => prev.map(complaint => 
      complaint.id === complaintId 
        ? { 
            ...complaint, 
            status: newStatus,
            resolvedDate: newStatus === 'resolved' ? new Date().toISOString() : undefined
          }
        : complaint
    ));

    // Simulate notification to member about status update
    console.log(`Notifying member about complaint status update: ${newStatus}`);
    alert(`Complaint status updated to ${newStatus}. Member has been notified.`);
  };

  const handleSendResponse = () => {
    if (selectedComplaint && responseText.trim()) {
      setComplaints(prev => prev.map(complaint => 
        complaint.id === selectedComplaint.id 
          ? { ...complaint, ownerResponse: responseText }
          : complaint
      ));

      // Simulate notification to member and admin
      console.log('Sending response to member and notifying admin:', responseText);
      alert('Response sent to member successfully! Admin has been notified.');
      
      setResponseText('');
      setSelectedComplaint(null);
    }
  };

  const handleResolveComplaint = () => {
    if (selectedComplaint && resolutionNotes.trim()) {
      setComplaints(prev => prev.map(complaint => 
        complaint.id === selectedComplaint.id 
          ? { 
              ...complaint, 
              status: 'resolved',
              resolvedDate: new Date().toISOString(),
              resolutionNotes: resolutionNotes
            }
          : complaint
      ));

      // Simulate notification to member about resolution
      console.log('Notifying member about complaint resolution:', resolutionNotes);
      alert('Complaint marked as resolved! Member has been notified.');
      
      setResolutionNotes('');
      setSelectedComplaint(null);
    }
  };

  const handleEscalateToAdmin = (complaintId: string) => {
    setComplaints(prev => prev.map(complaint => 
      complaint.id === complaintId 
        ? { ...complaint, escalatedToAdmin: true }
        : complaint
    ));

    // Simulate escalation to super admin
    console.log('Escalating complaint to super admin:', complaintId);
    alert('Complaint escalated to super admin for intervention.');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getUserInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  // Statistics
  const totalComplaints = complaints.length;
  const openComplaints = complaints.filter(c => c.status === 'open').length;
  const inProgressComplaints = complaints.filter(c => c.status === 'in_progress').length;
  const resolvedComplaints = complaints.filter(c => c.status === 'resolved').length;
  const urgentComplaints = complaints.filter(c => c.priority === 'urgent').length;

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Complaint Management</h1>
          <p className="text-muted-foreground">
            Manage and resolve member complaints efficiently
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Flag className="mr-2 h-4 w-4" />
            Priority Review
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalComplaints}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Open</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{openComplaints}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            <Clock className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{inProgressComplaints}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Resolved</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{resolvedComplaints}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Urgent</CardTitle>
            <Flag className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{urgentComplaints}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search complaints by title, description, or member name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Select value={statusFilter} onValueChange={(value: any) => setStatusFilter(value)}>
              <SelectTrigger className="w-full md:w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="open">Open</SelectItem>
                <SelectItem value="in_progress">In Progress</SelectItem>
                <SelectItem value="resolved">Resolved</SelectItem>
                <SelectItem value="closed">Closed</SelectItem>
              </SelectContent>
            </Select>
            <Select value={categoryFilter} onValueChange={(value: any) => setCategoryFilter(value)}>
              <SelectTrigger className="w-full md:w-[150px]">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="room">Room</SelectItem>
                <SelectItem value="food">Food</SelectItem>
                <SelectItem value="maintenance">Maintenance</SelectItem>
                <SelectItem value="cleanliness">Cleanliness</SelectItem>
                <SelectItem value="noise">Noise</SelectItem>
                <SelectItem value="service">Service</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
            <Select value={priorityFilter} onValueChange={(value: any) => setPriorityFilter(value)}>
              <SelectTrigger className="w-full md:w-[150px]">
                <SelectValue placeholder="Priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priorities</SelectItem>
                <SelectItem value="urgent">Urgent</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="low">Low</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Complaints Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Complaints</CardTitle>
          <CardDescription>
            View and manage all complaints from your hostel members
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Complaint</TableHead>
                  <TableHead>Member</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredComplaints.map((complaint) => (
                  <TableRow key={complaint.id}>
                    <TableCell>
                      <div>
                        <div className="font-semibold">{complaint.title}</div>
                        <div className="text-sm text-muted-foreground truncate max-w-xs">
                          {complaint.description}
                        </div>
                        {complaint.escalatedToAdmin && (
                          <Badge className="bg-red-100 text-red-800 mt-1">
                            <Flag className="h-3 w-3 mr-1" />
                            Escalated
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback className="text-xs">
                            {getUserInitials(complaint.memberDetails.name)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium text-sm">{complaint.memberDetails.name}</div>
                          <div className="text-xs text-muted-foreground">
                            Room {complaint.memberDetails.roomNumber}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getCategoryColor(complaint.category)}>
                        {getCategoryIcon(complaint.category)}
                        <span className="ml-1 capitalize">{complaint.category}</span>
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={getPriorityColor(complaint.priority)}>
                        {complaint.priority.charAt(0).toUpperCase() + complaint.priority.slice(1)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(complaint.status)}>
                        {getStatusIcon(complaint.status)}
                        <span className="ml-1 capitalize">{complaint.status.replace('_', ' ')}</span>
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {formatDate(complaint.createdDate)}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <Dialog>
                            <DialogTrigger asChild>
                              <DropdownMenuItem onSelect={(e) => {
                                e.preventDefault();
                                setSelectedComplaint(complaint);
                              }}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                            </DialogTrigger>
                          </Dialog>
                          <DropdownMenuSeparator />
                          {complaint.status === 'open' && (
                            <DropdownMenuItem onClick={() => handleStatusUpdate(complaint.id, 'in_progress')}>
                              <Clock className="mr-2 h-4 w-4" />
                              Mark In Progress
                            </DropdownMenuItem>
                          )}
                          {complaint.status === 'in_progress' && (
                            <DropdownMenuItem onClick={() => handleStatusUpdate(complaint.id, 'resolved')}>
                              <CheckCircle className="mr-2 h-4 w-4" />
                              Mark Resolved
                            </DropdownMenuItem>
                          )}
                          {!complaint.escalatedToAdmin && (
                            <DropdownMenuItem onClick={() => handleEscalateToAdmin(complaint.id)}>
                              <Flag className="mr-2 h-4 w-4" />
                              Escalate to Admin
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Complaint Details Dialog */}
      {selectedComplaint && (
        <Dialog open={!!selectedComplaint} onOpenChange={() => setSelectedComplaint(null)}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                {getCategoryIcon(selectedComplaint.category)}
                <span>{selectedComplaint.title}</span>
              </DialogTitle>
              <DialogDescription>
                Complaint details and member information
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              {/* Member Details */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-semibold mb-2 flex items-center">
                  <User className="h-4 w-4 mr-2" />
                  Member Details
                </h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Name:</span>
                    <div className="font-medium">{selectedComplaint.memberDetails.name}</div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Room:</span>
                    <div className="font-medium">{selectedComplaint.memberDetails.roomNumber}</div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Phone:</span>
                    <div className="font-medium">{selectedComplaint.memberDetails.phone}</div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Email:</span>
                    <div className="font-medium">{selectedComplaint.memberDetails.email}</div>
                  </div>
                </div>
              </div>

              {/* Complaint Details */}
              <div>
                <h4 className="font-semibold mb-2">Complaint Description</h4>
                <p className="text-sm text-muted-foreground bg-gray-50 p-3 rounded-lg">
                  {selectedComplaint.description}
                </p>
              </div>

              {/* Previous Response */}
              {selectedComplaint.ownerResponse && (
                <div>
                  <h4 className="font-semibold mb-2">Previous Response</h4>
                  <p className="text-sm bg-blue-50 p-3 rounded-lg">
                    {selectedComplaint.ownerResponse}
                  </p>
                </div>
              )}

              {/* Response Section */}
              {selectedComplaint.status !== 'resolved' && (
                <div className="space-y-2">
                  <Label htmlFor="response">Send Response to Member</Label>
                  <Textarea
                    id="response"
                    value={responseText}
                    onChange={(e) => setResponseText(e.target.value)}
                    placeholder="Enter your response to the member..."
                    rows={3}
                  />
                </div>
              )}

              {/* Resolution Section */}
              {selectedComplaint.status === 'in_progress' && (
                <div className="space-y-2">
                  <Label htmlFor="resolution">Resolution Notes</Label>
                  <Textarea
                    id="resolution"
                    value={resolutionNotes}
                    onChange={(e) => setResolutionNotes(e.target.value)}
                    placeholder="Enter resolution details..."
                    rows={3}
                  />
                </div>
              )}
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setSelectedComplaint(null)}>
                Close
              </Button>
              {selectedComplaint.status !== 'resolved' && responseText.trim() && (
                <Button onClick={handleSendResponse}>
                  <Send className="mr-2 h-4 w-4" />
                  Send Response
                </Button>
              )}
              {selectedComplaint.status === 'in_progress' && resolutionNotes.trim() && (
                <Button onClick={handleResolveComplaint} className="bg-green-600 hover:bg-green-700">
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Mark Resolved
                </Button>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};
