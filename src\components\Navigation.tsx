import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { 
  Building2, 
  Users, 
  UserCheck, 
  User,
  Menu,
  X
} from 'lucide-react';

interface NavigationProps {
  currentPanel: 'landing' | 'superadmin' | 'owner' | 'employee' | 'user';
  onPanelChange: (panel: 'landing' | 'superadmin' | 'owner' | 'employee' | 'user') => void;
}

export const Navigation = ({ currentPanel, onPanelChange }: NavigationProps) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const navItems = [
    { key: 'landing', label: 'Home', icon: Building2 },
    { key: 'superadmin', label: 'Super Admin', icon: Users },
    { key: 'owner', label: 'Hostel Provider', icon: Building2 },
    { key: 'employee', label: 'Employee', icon: UserCheck },
    { key: 'user', label: 'User Panel', icon: User },
  ] as const;

  return (
    <nav className="bg-card border-b border-border sticky top-0 z-50 backdrop-blur-sm bg-background/80">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div 
            className="flex items-center space-x-2 cursor-pointer"
            onClick={() => onPanelChange('landing')}
          >
            <Building2 className="h-8 w-8 text-primary" />
            <span className="text-xl font-bold bg-gradient-primary bg-clip-text text-transparent">
              HostelHub
            </span>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-1">
            {navItems.map((item) => {
              const Icon = item.icon;
              const isActive = currentPanel === item.key;
              
              return (
                <Button
                  key={item.key}
                  variant={isActive ? "default" : "ghost"}
                  size="sm"
                  onClick={() => onPanelChange(item.key)}
                  className={`flex items-center space-x-2 ${
                    isActive ? 'bg-gradient-primary text-primary-foreground shadow-elevation' : ''
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{item.label}</span>
                </Button>
              );
            })}
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              {isMobileMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="md:hidden py-4 border-t border-border">
            <div className="space-y-2">
              {navItems.map((item) => {
                const Icon = item.icon;
                const isActive = currentPanel === item.key;
                
                return (
                  <Button
                    key={item.key}
                    variant={isActive ? "default" : "ghost"}
                    className={`w-full justify-start space-x-2 ${
                      isActive ? 'bg-gradient-primary text-primary-foreground' : ''
                    }`}
                    onClick={() => {
                      onPanelChange(item.key);
                      setIsMobileMenuOpen(false);
                    }}
                  >
                    <Icon className="h-4 w-4" />
                    <span>{item.label}</span>
                  </Button>
                );
              })}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};