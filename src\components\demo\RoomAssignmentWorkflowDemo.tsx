import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Building2,
  Layers,
  Home,
  Bed,
  ArrowRight,
  Users,
  MapPin,
  Plus,
  Edit,
  Move,
  Info
} from 'lucide-react';
import { HostelInfo, Floor, Room, Bed as BedType } from '@/types/roomManagement';
import { 
  getHostelsByOwnerId, 
  getFloorsByHostelId, 
  getRoomsByHostelId,
  getRoomsByFloorId 
} from '@/data/mockData';

interface WorkflowDemoProps {
  ownerId: string;
}

export const RoomAssignmentWorkflowDemo: React.FC<WorkflowDemoProps> = ({ ownerId }) => {
  const [hostels, setHostels] = useState<HostelInfo[]>([]);
  const [selectedHostel, setSelectedHostel] = useState<HostelInfo | null>(null);
  const [floors, setFloors] = useState<Floor[]>([]);
  const [selectedFloor, setSelectedFloor] = useState<Floor | null>(null);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);

  useEffect(() => {
    // Load owner's hostels
    const ownerHostels = getHostelsByOwnerId(ownerId);
    setHostels(ownerHostels);
    
    if (ownerHostels.length > 0) {
      setSelectedHostel(ownerHostels[0]);
    }
  }, [ownerId]);

  useEffect(() => {
    if (selectedHostel) {
      // Load floors for selected hostel
      const hostelFloors = getFloorsByHostelId(selectedHostel.id);
      setFloors(hostelFloors);
      
      // Load all rooms for selected hostel
      const hostelRooms = getRoomsByHostelId(selectedHostel.id);
      setRooms(hostelRooms);
      
      if (hostelFloors.length > 0) {
        setSelectedFloor(hostelFloors[0]);
      }
    }
  }, [selectedHostel]);

  useEffect(() => {
    if (selectedFloor) {
      // Load rooms for selected floor
      const floorRooms = getRoomsByFloorId(selectedFloor.id);
      if (floorRooms.length > 0) {
        setSelectedRoom(floorRooms[0]);
      }
    }
  }, [selectedFloor]);

  const DataRelationshipDiagram = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Info className="h-5 w-5" />
          Data Relationship Hierarchy
        </CardTitle>
        <CardDescription>
          Understanding the hierarchical structure: Hostel → Floor → Room → Bed
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Hierarchy Visualization */}
          <div className="flex items-center justify-center space-x-4 p-4 bg-gray-50 rounded-lg">
            <div className="text-center">
              <Building2 className="h-8 w-8 mx-auto text-blue-600" />
              <div className="text-sm font-medium mt-1">Hostel</div>
              <div className="text-xs text-gray-500">hostelId</div>
            </div>
            <ArrowRight className="h-4 w-4 text-gray-400" />
            <div className="text-center">
              <Layers className="h-8 w-8 mx-auto text-green-600" />
              <div className="text-sm font-medium mt-1">Floor</div>
              <div className="text-xs text-gray-500">floorId</div>
            </div>
            <ArrowRight className="h-4 w-4 text-gray-400" />
            <div className="text-center">
              <Home className="h-8 w-8 mx-auto text-purple-600" />
              <div className="text-sm font-medium mt-1">Room</div>
              <div className="text-xs text-gray-500">roomId</div>
            </div>
            <ArrowRight className="h-4 w-4 text-gray-400" />
            <div className="text-center">
              <Bed className="h-8 w-8 mx-auto text-orange-600" />
              <div className="text-sm font-medium mt-1">Bed</div>
              <div className="text-xs text-gray-500">bedId</div>
            </div>
          </div>

          {/* ID Relationships */}
          <div className="grid grid-cols-2 gap-4">
            <div className="p-3 border rounded-lg">
              <h4 className="font-medium text-sm mb-2">Foreign Key Relationships</h4>
              <div className="space-y-1 text-xs">
                <div>• Floor.hostelId → Hostel.id</div>
                <div>• Room.hostelId → Hostel.id</div>
                <div>• Room.floorId → Floor.id</div>
                <div>• Bed.roomId → Room.id</div>
              </div>
            </div>
            <div className="p-3 border rounded-lg">
              <h4 className="font-medium text-sm mb-2">Data Filtering</h4>
              <div className="space-y-1 text-xs">
                <div>• Floors filtered by hostelId</div>
                <div>• Rooms filtered by hostelId + floorId</div>
                <div>• Beds filtered by roomId</div>
                <div>• Statistics aggregated up hierarchy</div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const MultiHostelManagement = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building2 className="h-5 w-5" />
          Multi-Hostel Management
        </CardTitle>
        <CardDescription>
          How owners switch between multiple properties
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Hostel Selection */}
          <div>
            <h4 className="font-medium mb-2">Owner's Hostel Portfolio</h4>
            <div className="grid gap-3">
              {hostels.map((hostel) => (
                <div
                  key={hostel.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedHostel?.id === hostel.id 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => setSelectedHostel(hostel)}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">{hostel.name}</div>
                      <div className="text-sm text-gray-500 flex items-center gap-1">
                        <MapPin className="h-3 w-3" />
                        {hostel.city}, {hostel.state}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">{hostel.totalFloors} floors</div>
                      <div className="text-xs text-gray-500">{hostel.totalRooms} rooms</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Dynamic Data Loading */}
          {selectedHostel && (
            <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
              <h4 className="font-medium text-green-800 mb-2">Dynamic Data Loading</h4>
              <div className="text-sm text-green-700 space-y-1">
                <div>✓ Selected: {selectedHostel.name}</div>
                <div>✓ Loaded {floors.length} floors for this hostel</div>
                <div>✓ Loaded {rooms.length} rooms for this hostel</div>
                <div>✓ All data filtered by hostelId: {selectedHostel.id}</div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );

  const FloorRoomAssignment = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Layers className="h-5 w-5" />
          Room-to-Floor Assignment
        </CardTitle>
        <CardDescription>
          How rooms are assigned to specific floors
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Floor Selection */}
          <div>
            <h4 className="font-medium mb-2">Available Floors</h4>
            <div className="flex flex-wrap gap-2">
              {floors.map((floor) => (
                <Badge
                  key={floor.id}
                  variant={selectedFloor?.id === floor.id ? "default" : "outline"}
                  className="cursor-pointer"
                  onClick={() => setSelectedFloor(floor)}
                >
                  Floor {floor.floorNumber} ({floor.totalRooms} rooms)
                </Badge>
              ))}
            </div>
          </div>

          {/* Rooms on Selected Floor */}
          {selectedFloor && (
            <div>
              <h4 className="font-medium mb-2">
                Rooms on Floor {selectedFloor.floorNumber}
              </h4>
              <div className="grid gap-2">
                {getRoomsByFloorId(selectedFloor.id).map((room) => (
                  <div
                    key={room.id}
                    className={`p-3 border rounded-lg ${
                      selectedRoom?.id === room.id ? 'border-purple-500 bg-purple-50' : ''
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">Room {room.roomNumber}</div>
                        <div className="text-sm text-gray-500">
                          {room.roomType} • {room.beds.length} beds • Floor {room.floor}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">floorId: {room.floorId}</Badge>
                        <Button size="sm" variant="outline">
                          <Edit className="h-3 w-3 mr-1" />
                          Edit
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Assignment Process */}
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-medium text-blue-800 mb-2">Assignment Process</h4>
            <div className="text-sm text-blue-700 space-y-1">
              <div>1. Room creation requires floorId selection</div>
              <div>2. Room.floorId links room to specific floor</div>
              <div>3. Room.hostelId ensures hostel-level filtering</div>
              <div>4. Floor.rooms array tracks room assignments</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const BedRoomAssignment = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bed className="h-5 w-5" />
          Bed-to-Room Assignment
        </CardTitle>
        <CardDescription>
          How beds are managed within rooms
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Selected Room Details */}
          {selectedRoom && (
            <div>
              <h4 className="font-medium mb-2">
                Room {selectedRoom.roomNumber} - Bed Management
              </h4>
              <div className="p-3 border rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <div className="font-medium">Capacity: {selectedRoom.capacity} beds</div>
                    <div className="text-sm text-gray-500">
                      Current: {selectedRoom.beds.length} beds assigned
                    </div>
                  </div>
                  <Button size="sm">
                    <Plus className="h-3 w-3 mr-1" />
                    Add Bed
                  </Button>
                </div>

                {/* Bed List */}
                <div className="space-y-2">
                  {selectedRoom.beds.map((bed) => (
                    <div key={bed.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <div>
                        <div className="font-medium">Bed {bed.bedNumber}</div>
                        <div className="text-sm text-gray-500">
                          {bed.bedType} • {bed.status}
                          {bed.occupiedBy && ` • Occupied by ${bed.occupiedBy}`}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">roomId: {bed.roomId}</Badge>
                        <Badge 
                          variant={bed.status === 'occupied' ? 'destructive' : 'default'}
                        >
                          {bed.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Bed Assignment Rules */}
          <div className="p-3 bg-orange-50 border border-orange-200 rounded-lg">
            <h4 className="font-medium text-orange-800 mb-2">Bed Assignment Rules</h4>
            <div className="text-sm text-orange-700 space-y-1">
              <div>• Bed.roomId must match parent Room.id</div>
              <div>• Bed numbers should be unique within room</div>
              <div>• Total beds cannot exceed room capacity</div>
              <div>• Bed status: available, occupied, maintenance, reserved</div>
              <div>• Occupancy tracking via occupiedBy field</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h1 className="text-2xl font-bold">Room & Bed Assignment Workflow Demo</h1>
        <p className="text-gray-600 mt-2">
          Complete demonstration of hostel, floor, room, and bed management relationships
        </p>
      </div>

      <Tabs defaultValue="hierarchy" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="hierarchy">Data Hierarchy</TabsTrigger>
          <TabsTrigger value="hostels">Multi-Hostel</TabsTrigger>
          <TabsTrigger value="floors">Floor-Room</TabsTrigger>
          <TabsTrigger value="beds">Room-Bed</TabsTrigger>
        </TabsList>

        <TabsContent value="hierarchy">
          <DataRelationshipDiagram />
        </TabsContent>

        <TabsContent value="hostels">
          <MultiHostelManagement />
        </TabsContent>

        <TabsContent value="floors">
          <FloorRoomAssignment />
        </TabsContent>

        <TabsContent value="beds">
          <BedRoomAssignment />
        </TabsContent>
      </Tabs>
    </div>
  );
};
