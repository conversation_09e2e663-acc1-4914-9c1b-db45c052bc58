// Enhanced Payment System Types
// Extends existing payment interfaces with comprehensive payment processing capabilities

import { Payment, BillingItem } from '@/data/mockData';

// Payment Gateway Types
export type PaymentGateway = 'phonepe' | 'cashfree' | 'razorpay';
export type PaymentMethod = 'card' | 'upi' | 'netbanking' | 'wallet' | 'emi';

// Enhanced Payment Interface
export interface EnhancedPayment extends Payment {
  gateway: PaymentGateway;
  gatewayTransactionId?: string;
  gatewayResponse?: GatewayResponse;
  paymentMethod: PaymentMethod;
  failureReason?: string;
  refundAmount?: number;
  refundStatus?: 'none' | 'partial' | 'full' | 'processing';
  receiptUrl?: string;
  remindersSent?: number;
  dueDate?: string;
  lateFee?: number;
  discountApplied?: number;
  splitPayment?: SplitPaymentDetails;
}

// Gateway Response Interface
export interface GatewayResponse {
  transactionId: string;
  status: 'success' | 'failed' | 'pending';
  message: string;
  timestamp: string;
  gatewayFee: number;
  currency: string;
  paymentMethod: PaymentMethod;
  cardDetails?: {
    last4: string;
    brand: string;
    type: string;
  };
  upiDetails?: {
    vpa: string;
    bank: string;
  };
}

// Fee Calculation Interface
export interface FeeCalculation {
  bedCharges: {
    baseRate: number;
    duration: number;
    total: number;
  };
  foodCharges: {
    dailyRate: number;
    days: number;
    total: number;
  };
  additionalServices: Array<{
    name: string;
    amount: number;
    description: string;
  }>;
  discounts: Array<{
    name: string;
    amount: number;
    type: 'percentage' | 'fixed';
  }>;
  taxes: Array<{
    name: string;
    amount: number;
    rate: number;
  }>;
  securityDeposit: number;
  lateFees: number;
  subtotal: number;
  totalDiscount: number;
  totalTax: number;
  grandTotal: number;
}

// Split Payment Interface
export interface SplitPaymentDetails {
  isEnabled: boolean;
  totalAmount: number;
  splits: Array<{
    userId: string;
    userName: string;
    amount: number;
    percentage: number;
    status: 'pending' | 'paid' | 'failed';
    paymentId?: string;
  }>;
  initiatedBy: string;
  splitType: 'equal' | 'custom' | 'percentage';
  dueDate: string;
}

// Receipt Interface
export interface PaymentReceipt {
  id: string;
  paymentId: string;
  bookingId: string;
  receiptNumber: string;
  issueDate: string;
  hostelDetails: {
    name: string;
    address: string;
    gst?: string;
    logo?: string;
  };
  memberDetails: {
    name: string;
    email: string;
    phone: string;
    roomNumber: string;
  };
  billingPeriod: {
    from: string;
    to: string;
  };
  itemizedCharges: BillingItem[];
  feeCalculation: FeeCalculation;
  paymentDetails: {
    method: PaymentMethod;
    gateway: PaymentGateway;
    transactionId: string;
    paidDate: string;
  };
  downloadUrl?: string;
}

// Payment Reminder Interface
export interface PaymentReminder {
  id: string;
  paymentId: string;
  userId: string;
  type: 'email' | 'sms' | 'push' | 'whatsapp';
  status: 'scheduled' | 'sent' | 'delivered' | 'failed';
  scheduledDate: string;
  sentDate?: string;
  template: string;
  escalationLevel: 1 | 2 | 3; // 1: gentle, 2: firm, 3: final notice
}

// Gateway Configuration Interface
export interface PaymentGatewayConfig {
  gateway: PaymentGateway;
  isEnabled: boolean;
  priority: number; // 1 = highest priority
  credentials: {
    merchantId: string;
    apiKey: string;
    secretKey: string;
    webhookSecret: string;
  };
  supportedMethods: PaymentMethod[];
  fees: {
    percentage: number;
    fixed: number;
    maxFee?: number;
  };
  limits: {
    minAmount: number;
    maxAmount: number;
  };
  settings: {
    autoCapture: boolean;
    timeout: number; // in minutes
    retryAttempts: number;
  };
}

// Payment Analytics Interface
export interface PaymentAnalytics {
  totalRevenue: number;
  totalTransactions: number;
  successRate: number;
  averageTransactionValue: number;
  gatewayPerformance: Array<{
    gateway: PaymentGateway;
    transactions: number;
    revenue: number;
    successRate: number;
    averageProcessingTime: number;
  }>;
  methodDistribution: Array<{
    method: PaymentMethod;
    count: number;
    percentage: number;
    revenue: number;
  }>;
  monthlyTrends: Array<{
    month: string;
    revenue: number;
    transactions: number;
    successRate: number;
  }>;
  overduePayments: {
    count: number;
    totalAmount: number;
    averageDaysOverdue: number;
  };
}

// Payment Processing Request
export interface PaymentRequest {
  bookingId: string;
  amount: number;
  currency: string;
  paymentMethod: PaymentMethod;
  gateway: PaymentGateway;
  customerDetails: {
    name: string;
    email: string;
    phone: string;
  };
  billingAddress?: {
    line1: string;
    line2?: string;
    city: string;
    state: string;
    pincode: string;
    country: string;
  };
  metadata: {
    hostelId: string;
    roomNumber: string;
    membershipType: string;
  };
  returnUrl: string;
  webhookUrl: string;
}

// Payment Processing Response
export interface PaymentResponse {
  success: boolean;
  paymentId: string;
  gatewayTransactionId?: string;
  redirectUrl?: string;
  qrCode?: string;
  message: string;
  estimatedCompletionTime?: number;
  nextAction?: {
    type: 'redirect' | 'display_qr' | 'wait_for_callback';
    data: any;
  };
}

// Refund Interface
export interface RefundRequest {
  paymentId: string;
  amount: number;
  reason: string;
  refundType: 'full' | 'partial';
  initiatedBy: string;
  notes?: string;
}

export interface RefundResponse {
  refundId: string;
  status: 'initiated' | 'processing' | 'completed' | 'failed';
  amount: number;
  estimatedCompletionTime: string;
  gatewayRefundId?: string;
}
