// Room and Bed Management TypeScript interfaces

// Hostel Management
export interface HostelInfo {
  id: string;
  name: string;
  address: string;
  city: string;
  state: string;
  pincode: string;
  ownerId: string;
  totalFloors: number;
  totalRooms: number;
  totalBeds: number;
  status: 'active' | 'inactive' | 'under_construction';
  createdAt: string;
  updatedAt: string;

  // Contact Information
  contactInfo: {
    phone: string;
    email: string;
    website?: string;
  };

  // Facilities
  facilities: string[];

  // Floor Information
  floors: Floor[];
}

// Floor Management
export interface Floor {
  id: string;
  hostelId: string;
  floorNumber: number;
  floorType: FloorType;
  totalRooms: number;
  totalBeds: number;
  currentOccupancy: number;
  status: 'active' | 'inactive' | 'under_construction' | 'maintenance';
  createdAt: string;
  updatedAt: string;

  // Floor Details
  details: FloorDetails;

  // Floor-specific amenities
  amenities: string[];

  // Rooms on this floor
  rooms: string[]; // Room IDs
}

export type FloorType =
  | 'residential'
  | 'commercial'
  | 'mixed'
  | 'common_area'
  | 'parking'
  | 'storage';

export interface FloorDetails {
  area: number; // in square feet
  ceilingHeight: number; // in feet
  hasElevatorAccess: boolean;
  hasStairAccess: boolean;
  hasFireExit: boolean;
  hasCommonArea: boolean;
  commonAreaSize?: number;

  // Utilities
  hasWiFi: boolean;
  hasCCTV: boolean;
  hasIntercom: boolean;
  hasPowerBackup: boolean;
  hasWaterSupply: boolean;

  // Restrictions
  restrictions?: string[];
  specialFeatures?: string[];
  description?: string;
}

// Floor Management Requests
export interface FloorModificationRequest {
  id: string;
  hostelId: string;
  ownerId: string;
  requestType: 'add_floor' | 'modify_floor' | 'remove_floor';
  status: 'pending' | 'approved' | 'rejected' | 'under_review';
  submittedAt: string;
  reviewedAt?: string;
  reviewedBy?: string; // Admin ID
  reviewNotes?: string;

  // Request details based on type
  requestDetails: AddFloorRequest | ModifyFloorRequest | RemoveFloorRequest;

  // Supporting documents
  documents?: {
    floorPlan?: string;
    structuralDrawings?: string[];
    permits?: string[];
    photos?: string[];
  };
}

export interface AddFloorRequest {
  floorNumber: number;
  floorType: FloorType;
  plannedRooms: number;
  plannedBeds: number;
  amenities: string[];
  details: FloorDetails;

  // Construction details
  constructionTimeline: string;
  estimatedCost: number;
  contractorInfo?: string;

  // Justification
  reason: string;
  expectedOccupancyIncrease: number;
  businessJustification: string;
}

export interface ModifyFloorRequest {
  floorId: string;
  changes: {
    floorType?: FloorType;
    amenities?: string[];
    details?: Partial<FloorDetails>;
    status?: Floor['status'];
  };
  reason: string;
}

export interface RemoveFloorRequest {
  floorId: string;
  reason: string;
  alternativeArrangements: string;
  affectedResidents: number;
}

// Floor Statistics
export interface FloorStatistics {
  floorId: string;
  floorNumber: number;
  totalRooms: number;
  totalBeds: number;
  occupiedBeds: number;
  availableBeds: number;
  maintenanceBeds: number;
  reservedBeds: number;
  occupancyRate: number;

  // Revenue statistics
  revenueStats: {
    dailyRevenue: number;
    monthlyRevenue: number;
    averageRatePerBed: number;
  };

  // Room type breakdown
  roomTypeBreakdown: {
    [key in RoomType]?: {
      count: number;
      totalBeds: number;
      occupiedBeds: number;
    };
  };
}

// Hostel Statistics (enhanced)
export interface HostelStatistics {
  hostelId: string;
  totalFloors: number;
  totalRooms: number;
  totalBeds: number;
  occupiedBeds: number;
  availableBeds: number;
  maintenanceBeds: number;
  reservedBeds: number;
  occupancyRate: number;

  // Floor-wise breakdown
  floorStatistics: FloorStatistics[];

  // Revenue statistics
  revenueStats: {
    dailyRevenue: number;
    monthlyRevenue: number;
    averageRatePerBed: number;
    totalSecurityDeposits: number;
  };

  // Room type breakdown across all floors
  roomTypeBreakdown: {
    [key in RoomType]?: {
      count: number;
      totalBeds: number;
      occupiedBeds: number;
    };
  };
}

export interface Room {
  id: string;
  hostelId: string;
  floorId: string;
  roomNumber: string;
  roomType: RoomType;
  floor: number;
  capacity: number;
  currentOccupancy: number;
  amenities: string[];
  status: 'active' | 'maintenance' | 'inactive';
  createdAt: string;
  updatedAt: string;
  
  // Bed Configuration
  beds: Bed[];
  
  // Pricing
  pricing: RoomPricing;
  
  // Room Details
  details: RoomDetails;
  
  // Photos
  photos: string[];
}

export interface Bed {
  id: string;
  roomId: string;
  bedNumber: string;
  bedType: BedType;
  status: 'available' | 'occupied' | 'maintenance' | 'reserved';
  occupiedBy?: string; // Member ID
  reservedBy?: string; // Member ID
  reservedUntil?: string; // ISO timestamp
  
  // Bed-specific pricing (if different from room pricing)
  customPricing?: BedPricing;
  
  // Bed details
  size: BedSize;
  hasLocker: boolean;
  lockerNumber?: string;
  
  createdAt: string;
  updatedAt: string;
}

export type RoomType = 
  | 'single' 
  | 'double' 
  | 'triple' 
  | 'quad' 
  | 'dormitory' 
  | 'suite' 
  | 'studio' 
  | 'family';

export type BedType = 
  | 'single' 
  | 'double' 
  | 'bunk_top' 
  | 'bunk_bottom' 
  | 'queen' 
  | 'king';

export type BedSize = 
  | 'single' 
  | 'double' 
  | 'queen' 
  | 'king' 
  | 'super_king';

export interface RoomPricing {
  dailyRate: number;
  weeklyRate: number;
  monthlyRate: number;
  hourlyRate?: number;
  
  // Seasonal pricing
  seasonalRates?: {
    season: string;
    startDate: string;
    endDate: string;
    multiplier: number; // e.g., 1.2 for 20% increase
  }[];
  
  // Security deposit
  securityDeposit: number;
  
  // Additional charges
  cleaningFee?: number;
  maintenanceFee?: number;
}

export interface BedPricing {
  dailyRate: number;
  weeklyRate: number;
  monthlyRate: number;
  securityDeposit: number;
}

export interface RoomDetails {
  area: number; // in square feet
  hasWindow: boolean;
  hasBalcony: boolean;
  hasAttachedBathroom: boolean;
  bathroomType?: 'private' | 'shared' | 'common';
  hasAC: boolean;
  hasFan: boolean;
  hasWiFi: boolean;
  hasTV: boolean;
  hasRefrigerator: boolean;
  hasWardrobe: boolean;
  hasStudyTable: boolean;
  hasChair: boolean;
  
  // Additional details
  description?: string;
  specialFeatures?: string[];
  restrictions?: string[];
}

// Room Addition/Modification Requests
export interface RoomModificationRequest {
  id: string;
  hostelId: string;
  ownerId: string;
  requestType: 'add_room' | 'modify_room' | 'add_beds' | 'remove_beds' | 'modify_pricing';
  status: 'pending' | 'approved' | 'rejected' | 'under_review';
  submittedAt: string;
  reviewedAt?: string;
  reviewedBy?: string; // Admin ID
  reviewNotes?: string;
  
  // Request details based on type
  requestDetails: AddRoomRequest | ModifyRoomRequest | AddBedsRequest | RemoveBedsRequest | ModifyPricingRequest;
  
  // Supporting documents
  documents?: {
    floorPlan?: string;
    photos?: string[];
    permits?: string[];
  };
}

export interface AddRoomRequest {
  roomNumber: string;
  roomType: RoomType;
  floor: number;
  capacity: number;
  amenities: string[];
  beds: Omit<Bed, 'id' | 'roomId' | 'createdAt' | 'updatedAt'>[];
  pricing: RoomPricing;
  details: RoomDetails;
  photos: string[];
  
  // Justification
  reason: string;
  expectedOccupancyIncrease: number;
}

export interface ModifyRoomRequest {
  roomId: string;
  changes: {
    roomNumber?: string;
    roomType?: RoomType;
    capacity?: number;
    amenities?: string[];
    pricing?: Partial<RoomPricing>;
    details?: Partial<RoomDetails>;
    status?: Room['status'];
  };
  reason: string;
}

export interface AddBedsRequest {
  roomId: string;
  newBeds: Omit<Bed, 'id' | 'roomId' | 'createdAt' | 'updatedAt'>[];
  reason: string;
  capacityIncrease: number;
}

export interface RemoveBedsRequest {
  roomId: string;
  bedIdsToRemove: string[];
  reason: string;
  capacityDecrease: number;
}

export interface ModifyPricingRequest {
  roomId?: string;
  bedId?: string;
  newPricing: RoomPricing | BedPricing;
  effectiveDate: string;
  reason: string;
}

// Room Management Statistics
export interface RoomStatistics {
  totalRooms: number;
  totalBeds: number;
  occupiedBeds: number;
  availableBeds: number;
  maintenanceBeds: number;
  reservedBeds: number;
  occupancyRate: number;
  
  // By room type
  roomTypeBreakdown: {
    [key in RoomType]?: {
      count: number;
      totalBeds: number;
      occupiedBeds: number;
    };
  };
  
  // Revenue statistics
  revenueStats: {
    dailyRevenue: number;
    monthlyRevenue: number;
    averageRatePerBed: number;
    totalSecurityDeposits: number;
  };
}

// Room Search and Filter Options
export interface RoomSearchFilters {
  hostelId?: string;
  floorId?: string;
  roomType?: RoomType[];
  bedType?: BedType[];
  status?: Room['status'][];
  floor?: number[];
  floorType?: FloorType[];
  hasAC?: boolean;
  hasAttachedBathroom?: boolean;
  priceRange?: {
    min: number;
    max: number;
    period: 'daily' | 'weekly' | 'monthly';
  };
  capacity?: {
    min: number;
    max: number;
  };
  amenities?: string[];
  floorAmenities?: string[];
}

// Bulk Operations
export interface BulkRoomOperation {
  operation: 'update_pricing' | 'change_status' | 'add_amenity' | 'remove_amenity';
  roomIds: string[];
  parameters: {
    pricing?: Partial<RoomPricing>;
    status?: Room['status'];
    amenity?: string;
  };
  reason: string;
}

// Room Availability
export interface RoomAvailability {
  roomId: string;
  date: string;
  availableBeds: number;
  totalBeds: number;
  pricing: {
    dailyRate: number;
    weeklyRate: number;
    monthlyRate: number;
  };
  restrictions?: string[];
}

// Room Booking Integration
export interface RoomBookingInfo {
  roomId: string;
  bedId?: string;
  checkIn: string;
  checkOut: string;
  guestCount: number;
  totalAmount: number;
  securityDeposit: number;
  specialRequests?: string[];
}

export interface RoomMaintenanceLog {
  id: string;
  roomId: string;
  bedId?: string;
  type: 'cleaning' | 'repair' | 'inspection' | 'upgrade';
  description: string;
  scheduledDate: string;
  completedDate?: string;
  assignedTo: string;
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
  cost?: number;
  notes?: string;
  photos?: string[];
  createdAt: string;
  updatedAt: string;
}
