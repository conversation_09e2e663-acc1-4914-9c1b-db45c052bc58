import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  BarChart3, 
  Download,
  TrendingUp,
  TrendingDown,
  PieChart,
  Calendar,
  Users,
  Building2,
  DollarSign,
  Activity,
  ArrowUpRight,
  ArrowDownRight,
  Eye,
  FileText,
  Target,
  Zap
} from 'lucide-react';
import { mockHostels, mockUsers, mockPayments, mockBookings, mockComplaints } from '@/data/mockData';

export const AnalyticsReports: React.FC = () => {
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [reportType, setReportType] = useState<'overview' | 'financial' | 'operational' | 'user'>('overview');

  // Calculate analytics data
  const totalRevenue = mockPayments.reduce((sum, p) => sum + p.amount, 0);
  const platformRevenue = mockPayments.reduce((sum, p) => sum + p.splitDetails.platform, 0);
  const totalBookings = mockBookings.length;
  const activeUsers = mockUsers.filter(u => u.status === 'active').length;
  const totalBeds = mockHostels.reduce((sum, h) => sum + h.totalBeds, 0);
  const occupiedBeds = mockHostels.reduce((sum, h) => sum + (h.totalBeds - h.availableBeds), 0);
  const occupancyRate = ((occupiedBeds / totalBeds) * 100).toFixed(1);
  const averageBookingValue = totalRevenue / totalBookings;

  // Growth metrics (mock data for demonstration)
  const growthMetrics = [
    {
      title: 'Revenue Growth',
      value: '+24.5%',
      trend: 'up',
      icon: DollarSign,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      description: 'vs last period'
    },
    {
      title: 'User Growth',
      value: '+18.2%',
      trend: 'up',
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      description: 'new registrations'
    },
    {
      title: 'Booking Growth',
      value: '+12.8%',
      trend: 'up',
      icon: Calendar,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      description: 'booking volume'
    },
    {
      title: 'Occupancy Rate',
      value: `${occupancyRate}%`,
      trend: 'up',
      icon: Building2,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      description: 'current occupancy'
    },
  ];

  // Key Performance Indicators
  const kpiData = [
    {
      title: 'Total Revenue',
      value: `₹${(totalRevenue / 1000).toFixed(0)}K`,
      change: '+15.3%',
      trend: 'up',
      target: '₹150K',
      progress: 80
    },
    {
      title: 'Active Users',
      value: activeUsers.toString(),
      change: '****%',
      trend: 'up',
      target: '500',
      progress: 65
    },
    {
      title: 'Avg Booking Value',
      value: `₹${(averageBookingValue / 1000).toFixed(0)}K`,
      change: '****%',
      trend: 'up',
      target: '₹65K',
      progress: 92
    },
    {
      title: 'Platform Commission',
      value: `₹${(platformRevenue / 1000).toFixed(0)}K`,
      change: '+12.1%',
      trend: 'up',
      target: '₹15K',
      progress: 75
    },
  ];

  // Top performing hostels
  const topHostels = mockHostels
    .map(hostel => {
      const hostelBookings = mockBookings.filter(b => b.hostelId === hostel.id);
      const hostelRevenue = hostelBookings.reduce((sum, booking) => {
        const payment = mockPayments.find(p => p.bookingId === booking.id);
        return sum + (payment?.amount || 0);
      }, 0);
      const occupancyRate = ((hostel.totalBeds - hostel.availableBeds) / hostel.totalBeds) * 100;
      
      return {
        ...hostel,
        revenue: hostelRevenue,
        bookings: hostelBookings.length,
        occupancyRate: occupancyRate.toFixed(1)
      };
    })
    .sort((a, b) => b.revenue - a.revenue)
    .slice(0, 5);

  const getTimeRangeLabel = (range: string) => {
    const labels = {
      '7d': 'Last 7 days',
      '30d': 'Last 30 days',
      '90d': 'Last 90 days',
      '1y': 'Last year'
    };
    return labels[range as keyof typeof labels];
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Analytics & Reports</h1>
          <p className="text-muted-foreground">
            Comprehensive business insights and performance metrics
          </p>
        </div>
        <div className="flex space-x-2">
          <Select value={timeRange} onValueChange={(value: any) => setTimeRange(value)}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export Report
          </Button>
          <Button>
            <FileText className="mr-2 h-4 w-4" />
            Generate Report
          </Button>
        </div>
      </div>

      {/* Growth Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {growthMetrics.map((metric, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {metric.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${metric.bgColor}`}>
                <metric.icon className={`h-4 w-4 ${metric.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metric.value}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                {metric.trend === 'up' ? (
                  <ArrowUpRight className="mr-1 h-3 w-3 text-green-500" />
                ) : (
                  <ArrowDownRight className="mr-1 h-3 w-3 text-red-500" />
                )}
                <span className={metric.trend === 'up' ? 'text-green-600' : 'text-red-600'}>
                  {metric.description}
                </span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* KPI Dashboard */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Key Performance Indicators
          </CardTitle>
          <CardDescription>
            Track progress against targets for {getTimeRangeLabel(timeRange)}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            {kpiData.map((kpi, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">{kpi.title}</span>
                  <Badge variant="outline" className="text-xs">
                    Target: {kpi.target}
                  </Badge>
                </div>
                <div className="text-2xl font-bold">{kpi.value}</div>
                <div className="flex items-center space-x-2">
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-primary h-2 rounded-full transition-all duration-300"
                      style={{ width: `${kpi.progress}%` }}
                    ></div>
                  </div>
                  <span className="text-xs text-muted-foreground">{kpi.progress}%</span>
                </div>
                <div className="flex items-center text-xs">
                  <ArrowUpRight className="mr-1 h-3 w-3 text-green-500" />
                  <span className="text-green-600">{kpi.change}</span>
                  <span className="ml-1 text-muted-foreground">vs last period</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Performance Analysis */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Top Performing Hostels
            </CardTitle>
            <CardDescription>
              Hostels ranked by revenue generation
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topHostels.map((hostel, index) => (
                <div key={hostel.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center justify-center w-8 h-8 bg-primary/10 rounded-full">
                      <span className="text-sm font-semibold text-primary">#{index + 1}</span>
                    </div>
                    <div>
                      <div className="font-medium">{hostel.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {hostel.city}, {hostel.state}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold">₹{(hostel.revenue / 1000).toFixed(0)}K</div>
                    <div className="text-sm text-muted-foreground">
                      {hostel.occupancyRate}% occupied
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              System Health
            </CardTitle>
            <CardDescription>
              Platform performance and health metrics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm font-medium">Payment Success Rate</span>
                </div>
                <div className="text-right">
                  <div className="font-semibold">98.5%</div>
                  <div className="text-xs text-green-600">+0.3%</div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-sm font-medium">User Satisfaction</span>
                </div>
                <div className="text-right">
                  <div className="font-semibold">4.6/5.0</div>
                  <div className="text-xs text-blue-600">+0.1</div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                  <span className="text-sm font-medium">Avg Response Time</span>
                </div>
                <div className="text-right">
                  <div className="font-semibold">2.3h</div>
                  <div className="text-xs text-orange-600">-0.5h</div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                  <span className="text-sm font-medium">Platform Uptime</span>
                </div>
                <div className="text-right">
                  <div className="font-semibold">99.9%</div>
                  <div className="text-xs text-purple-600">Stable</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
