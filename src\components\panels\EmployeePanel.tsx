import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Users, 
  Bed, 
  AlertTriangle, 
  CheckCircle,
  Clock,
  UserPlus,
  MessageSquare,
  Calendar
} from 'lucide-react';
import { mockHostels, mockUsers, mockBookings, mockComplaints, getUserById, getComplaintsByHostelId } from '@/data/mockData';

export const EmployeePanel = () => {
  // Assuming current user is employee with ID '3' (<PERSON><PERSON>)
  const currentEmployeeId = '3';
  const currentEmployee = getUserById(currentEmployeeId);
  const currentHostel = mockHostels.find(h => h.id === currentEmployee?.hostelId);
  
  // Get data for current hostel
  const hostelBookings = mockBookings.filter(b => b.hostelId === currentEmployee?.hostelId);
  const hostelComplaints = getComplaintsByHostelId(currentEmployee?.hostelId || '');
  const hostelMembers = mockUsers.filter(u => 
    u.role === 'member' && 
    hostelBookings.some(b => b.userId === u.id)
  );

  const stats = [
    {
      title: 'Total Members',
      value: hostelMembers.length,
      icon: Users,
      color: 'text-blue-600'
    },
    {
      title: 'Occupied Beds',
      value: currentHostel ? currentHostel.totalBeds - currentHostel.availableBeds : 0,
      icon: Bed,
      color: 'text-green-600'
    },
    {
      title: 'Open Complaints',
      value: hostelComplaints.filter(c => c.status === 'open').length,
      icon: AlertTriangle,
      color: 'text-red-600'
    },
    {
      title: 'Resolved Today',
      value: '3',
      icon: CheckCircle,
      color: 'text-purple-600'
    }
  ];

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Employee Dashboard</h1>
          <p className="text-muted-foreground">
            {currentHostel?.name} • Daily operations and member management
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Calendar className="mr-2 h-4 w-4" />
            Schedule
          </Button>
          <Button className="bg-gradient-primary text-primary-foreground shadow-elevation">
            <UserPlus className="mr-2 h-4 w-4" />
            Add Member
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index} className="hover:shadow-elevation transition-all duration-300">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                <Icon className={`h-4 w-4 ${stat.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Main Content */}
      <div className="grid lg:grid-cols-2 gap-6">
        {/* Current Members */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Current Members
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {hostelMembers.map((member) => {
                const booking = hostelBookings.find(b => b.userId === member.id);
                return (
                  <div key={member.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium">{member.name}</div>
                      <div className="text-sm text-muted-foreground">{member.email}</div>
                      <div className="text-sm text-muted-foreground">
                        Bed: {booking?.bedNumber}
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge variant="default">Active</Badge>
                      <div className="text-sm text-muted-foreground mt-1">
                        {booking?.checkIn}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Recent Complaints */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Recent Complaints
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {hostelComplaints.map((complaint) => {
                const member = getUserById(complaint.userId);
                return (
                  <div key={complaint.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div className="font-medium">{complaint.title}</div>
                      <Badge 
                        variant={
                          complaint.status === 'open' ? 'destructive' : 
                          complaint.status === 'in_progress' ? 'default' : 'secondary'
                        }
                      >
                        {complaint.status.replace('_', ' ')}
                      </Badge>
                    </div>
                    <div className="text-sm text-muted-foreground mb-2">
                      By: {member?.name} • {complaint.createdDate}
                    </div>
                    <div className="text-sm mb-3">{complaint.description}</div>
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline">
                        <MessageSquare className="h-4 w-4 mr-1" />
                        Respond
                      </Button>
                      <Button size="sm" variant="outline">
                        <CheckCircle className="h-4 w-4 mr-1" />
                        Mark Resolved
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Today's Tasks */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Today's Tasks
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center gap-3 p-3 border rounded-lg">
              <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
              <div className="flex-1">
                <div className="font-medium">Room inspection - Floor 2</div>
                <div className="text-sm text-muted-foreground">Due: 2:00 PM</div>
              </div>
              <Button size="sm" variant="outline">Mark Complete</Button>
            </div>
            
            <div className="flex items-center gap-3 p-3 border rounded-lg">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <div className="flex-1">
                <div className="font-medium">New member check-in</div>
                <div className="text-sm text-muted-foreground">Rahul Sharma - Bed A205</div>
              </div>
              <Button size="sm" variant="outline">Process</Button>
            </div>
            
            <div className="flex items-center gap-3 p-3 border rounded-lg">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <div className="flex-1">
                <div className="font-medium">Monthly fee collection</div>
                <div className="text-sm text-muted-foreground">Follow up with 5 members</div>
              </div>
              <Button size="sm" variant="outline">Review</Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};