// Approval service for handling registration request approvals and rejections

import { HostelRegistrationRequest, AccountCreationRequest, RegistrationApprovalAction } from '@/types/admin';
import { User } from '@/data/mockData';
import { 
  mockHostelRegistrationRequests, 
  mockAccountCreationRequests,
  mockUsers 
} from '@/data/mockData';

export interface ApprovalResult {
  success: boolean;
  error?: string;
}

export interface ApprovalActionData {
  requestId: string;
  action: 'approve' | 'reject' | 'request_more_info';
  notes?: string;
  adminId: string;
}

// Approve or reject a hostel registration request
export const processHostelRegistrationApproval = async (
  actionData: ApprovalActionData
): Promise<ApprovalResult> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    const requestIndex = mockHostelRegistrationRequests.findIndex(
      request => request.id === actionData.requestId
    );

    if (requestIndex === -1) {
      return {
        success: false,
        error: 'Registration request not found'
      };
    }

    const request = mockHostelRegistrationRequests[requestIndex];

    // Update request status
    const updatedRequest: HostelRegistrationRequest = {
      ...request,
      status: actionData.action === 'approve' ? 'approved' : 
              actionData.action === 'reject' ? 'rejected' : 'under_review',
      reviewedBy: actionData.adminId,
      reviewedAt: new Date().toISOString(),
      reviewNotes: actionData.notes
    };

    mockHostelRegistrationRequests[requestIndex] = updatedRequest;

    // If approved, create the hostel and user account
    if (actionData.action === 'approve') {
      await createHostelAndOwnerAccount(request);
    }

    return { success: true };

  } catch (error) {
    console.error('Error processing hostel registration approval:', error);
    return {
      success: false,
      error: 'Failed to process approval'
    };
  }
};

// Approve or reject an account creation request
export const processAccountCreationApproval = async (
  actionData: ApprovalActionData
): Promise<ApprovalResult> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    const requestIndex = mockAccountCreationRequests.findIndex(
      request => request.id === actionData.requestId
    );

    if (requestIndex === -1) {
      return {
        success: false,
        error: 'Account creation request not found'
      };
    }

    const request = mockAccountCreationRequests[requestIndex];

    // Update request status
    const updatedRequest: AccountCreationRequest = {
      ...request,
      status: actionData.action === 'approve' ? 'approved' : 'rejected',
      reviewedBy: actionData.adminId,
      reviewedAt: new Date().toISOString(),
      reviewNotes: actionData.notes
    };

    mockAccountCreationRequests[requestIndex] = updatedRequest;

    // If approved, create the user account
    if (actionData.action === 'approve') {
      await createUserAccount(request);
    }

    return { success: true };

  } catch (error) {
    console.error('Error processing account creation approval:', error);
    return {
      success: false,
      error: 'Failed to process approval'
    };
  }
};

// Create hostel and owner account after approval
const createHostelAndOwnerAccount = async (request: HostelRegistrationRequest): Promise<void> => {
  // Create owner user account
  const newOwner: User = {
    id: `owner_${Date.now()}`,
    name: request.ownerDetails.name,
    email: request.ownerDetails.email,
    phone: request.ownerDetails.phone,
    role: 'owner',
    joinedDate: new Date().toISOString(),
    status: 'active',
    emailVerified: true,
    registrationStatus: 'approved'
  };

  // Add to mock users
  mockUsers.push(newOwner);

  // Create hostel entry (would be added to mockHostels in real implementation)
  const newHostel = {
    id: `hostel_${Date.now()}`,
    name: request.hostelDetails.name,
    address: request.hostelDetails.address,
    city: request.hostelDetails.city,
    state: request.hostelDetails.state,
    pincode: request.hostelDetails.pincode,
    areaId: request.hostelDetails.areaId,
    totalBeds: request.hostelDetails.totalBeds,
    availableBeds: request.hostelDetails.totalBeds, // Initially all beds are available
    pricePerBed: request.hostelDetails.pricePerBed,
    amenities: request.hostelDetails.amenities,
    rating: 0, // Initial rating
    images: Object.values(request.photos).flat(),
    ownerId: newOwner.id,
    employees: [],
    status: 'active' as const,
    bedTypes: request.hostelDetails.bedTypes,
    foodPackages: request.hostelDetails.foodPackages,
    securityDeposit: request.hostelDetails.securityDeposit,
    minimumStayHours: request.hostelDetails.minimumStayHours,
    hourlyRate: request.hostelDetails.hourlyRate
  };

  // In a real application, this would be added to the hostels database
  console.log('New hostel created:', newHostel);

  // Send notification email to owner (simulated)
  await sendApprovalNotification(request.ownerDetails.email, 'hostel_approved', {
    hostelName: request.hostelDetails.name,
    ownerName: request.ownerDetails.name
  });
};

// Create user account after approval
const createUserAccount = async (request: AccountCreationRequest): Promise<void> => {
  const newUser: User = {
    id: `user_${Date.now()}`,
    name: request.requestedUserDetails.name,
    email: request.requestedUserDetails.email,
    phone: request.requestedUserDetails.phone,
    role: request.requestedUserDetails.role,
    joinedDate: new Date().toISOString(),
    status: 'active',
    emailVerified: true,
    registrationStatus: 'approved'
  };

  // Add to mock users
  mockUsers.push(newUser);

  // Send notification email to user (simulated)
  await sendApprovalNotification(request.requestedUserDetails.email, 'account_approved', {
    userName: request.requestedUserDetails.name,
    role: request.requestedUserDetails.role
  });
};

// Send notification emails (simulated)
const sendApprovalNotification = async (
  email: string, 
  type: 'hostel_approved' | 'hostel_rejected' | 'account_approved' | 'account_rejected',
  data: any
): Promise<void> => {
  // Simulate email sending delay
  await new Promise(resolve => setTimeout(resolve, 500));

  console.log(`Email notification sent to ${email}:`, {
    type,
    data,
    timestamp: new Date().toISOString()
  });
};

// Get approval statistics
export const getApprovalStatistics = async () => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));

  const hostelRequests = mockHostelRegistrationRequests;
  const accountRequests = mockAccountCreationRequests;

  return {
    hostelRegistrations: {
      total: hostelRequests.length,
      pending: hostelRequests.filter(r => r.status === 'pending').length,
      approved: hostelRequests.filter(r => r.status === 'approved').length,
      rejected: hostelRequests.filter(r => r.status === 'rejected').length,
      underReview: hostelRequests.filter(r => r.status === 'under_review').length
    },
    accountCreations: {
      total: accountRequests.length,
      pending: accountRequests.filter(r => r.status === 'pending').length,
      approved: accountRequests.filter(r => r.status === 'approved').length,
      rejected: accountRequests.filter(r => r.status === 'rejected').length
    },
    recentActivity: [
      ...hostelRequests.slice(-5).map(r => ({
        id: r.id,
        type: 'hostel_registration' as const,
        name: r.hostelDetails.name,
        status: r.status,
        submittedAt: r.submittedAt
      })),
      ...accountRequests.slice(-5).map(r => ({
        id: r.id,
        type: 'account_creation' as const,
        name: r.requestedUserDetails.name,
        status: r.status,
        submittedAt: r.submittedAt
      }))
    ].sort((a, b) => new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime()).slice(0, 10)
  };
};

// Bulk approval actions
export const processBulkApproval = async (
  requestIds: string[],
  action: 'approve' | 'reject',
  notes: string,
  adminId: string
): Promise<ApprovalResult> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    const results = await Promise.all(
      requestIds.map(async (requestId) => {
        // Check if it's a hostel registration or account creation request
        const hostelRequest = mockHostelRegistrationRequests.find(r => r.id === requestId);
        const accountRequest = mockAccountCreationRequests.find(r => r.id === requestId);

        if (hostelRequest) {
          return processHostelRegistrationApproval({
            requestId,
            action,
            notes,
            adminId
          });
        } else if (accountRequest) {
          return processAccountCreationApproval({
            requestId,
            action,
            notes,
            adminId
          });
        } else {
          return { success: false, error: `Request ${requestId} not found` };
        }
      })
    );

    const failedResults = results.filter(r => !r.success);
    
    if (failedResults.length > 0) {
      return {
        success: false,
        error: `${failedResults.length} out of ${requestIds.length} requests failed to process`
      };
    }

    return { success: true };

  } catch (error) {
    console.error('Error processing bulk approval:', error);
    return {
      success: false,
      error: 'Failed to process bulk approval'
    };
  }
};
