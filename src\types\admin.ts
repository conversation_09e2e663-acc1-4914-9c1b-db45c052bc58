// Admin-specific TypeScript interfaces for the room-buddy-hub application

import { SharingType, RoomType } from './owner';

// Registration Request Types
export interface HostelRegistrationRequest {
  id: string;
  requestType: 'hostel_registration';
  submittedBy: string; // User ID of the hostel owner
  submittedAt: string; // ISO timestamp
  status: 'pending' | 'approved' | 'rejected' | 'under_review';
  reviewedBy?: string; // Super admin user ID
  reviewedAt?: string; // ISO timestamp
  reviewNotes?: string;

  // Hostel Information
  hostelDetails: {
    name: string;
    address: string;
    city: string;
    state: string;
    pincode: string;
    areaId: string;
    totalBeds: number;
    amenities: string[];
    pricePerBed: number;
    bedTypes: {
      standard: {
        count: number;
        monthlyRate: number;
        dailyRate: number;
      };
      premium: {
        count: number;
        monthlyRate: number;
        dailyRate: number;
      };
    };
    foodPackages: {
      withMeals: {
        dailyRate: number;
        description: string;
      };
      withoutMeals: {
        dailyRate: number;
        description: string;
      };
    };
    securityDeposit: number;
    minimumStayHours: number;
    hourlyRate?: number;
  };

  // Owner Information
  ownerDetails: {
    name: string;
    email: string;
    phone: string;
    alternatePhone?: string;
    businessExperience: number;
    previousHostelExperience: boolean;
  };

  // Documents
  documents: {
    ownerIdProof: {
      type: 'aadhar' | 'pan' | 'passport' | 'driving_license';
      number: string;
      fileUrl?: string;
      verified: boolean;
    };
    businessRegistration?: {
      type: 'gst' | 'shop_act' | 'trade_license';
      number: string;
      fileUrl?: string;
      verified: boolean;
    };
    propertyDocuments: {
      type: 'ownership' | 'rental_agreement' | 'lease_deed';
      fileUrl?: string;
      verified: boolean;
    };
    bankDetails: {
      accountNumber: string;
      ifscCode: string;
      bankName: string;
      branchName: string;
      accountHolderName: string;
      accountType: 'savings' | 'current';
    };
  };

  // Photos
  photos: {
    exterior: string[];
    rooms: string[];
    commonAreas: string[];
    amenities: string[];
    kitchen?: string[];
  };
}

export interface AccountCreationRequest {
  id: string;
  requestType: 'account_creation';
  submittedAt: string; // ISO timestamp
  status: 'pending' | 'approved' | 'rejected';
  reviewedBy?: string; // Super admin user ID
  reviewedAt?: string; // ISO timestamp
  reviewNotes?: string;

  // Requested User Details
  requestedUserDetails: {
    name: string;
    email: string;
    phone: string;
    role: 'owner' | 'employee';
    businessType?: string;
    experienceYears?: number;
    referenceContact?: string;
  };

  // Verification Documents
  documents: {
    idProof: {
      type: 'aadhar' | 'pan' | 'passport' | 'driving_license';
      number: string;
      fileUrl?: string;
    };
    businessProof?: {
      type: 'gst' | 'shop_act' | 'trade_license';
      number: string;
      fileUrl?: string;
    };
  };
}

export type RegistrationRequest = HostelRegistrationRequest | AccountCreationRequest;

export interface HostelOwnerAccessRequest {
  id: string;
  requestType: 'owner_access';
  submittedAt: string; // ISO timestamp
  status: 'pending' | 'approved' | 'rejected' | 'under_review';
  reviewedBy?: string; // Super admin user ID
  reviewedAt?: string; // ISO timestamp
  reviewNotes?: string;

  // Applicant Information
  applicantDetails: {
    name: string;
    email: string;
    phone: string;
    alternatePhone?: string;
    address: string;
    city: string;
    state: string;
    pincode: string;
  };

  // Business Information
  businessDetails: {
    businessName?: string;
    businessType: string;
    experienceYears: number;
    previousHostelExperience: boolean;
    numberOfPropertiesOwned?: number;
    estimatedInvestment?: number;
    targetLocation: string;
    expectedLaunchDate?: string;
  };

  // Documents
  documents: {
    idProof: {
      type: 'aadhar' | 'pan' | 'passport' | 'driving_license';
      number: string;
      fileUrl?: string;
      verified: boolean;
    };
    addressProof?: {
      type: 'utility_bill' | 'bank_statement' | 'rental_agreement';
      fileUrl?: string;
      verified: boolean;
    };
    businessProof?: {
      type: 'gst' | 'shop_act' | 'trade_license' | 'incorporation_certificate';
      number?: string;
      fileUrl?: string;
      verified: boolean;
    };
    financialProof?: {
      type: 'bank_statement' | 'income_tax_return' | 'financial_statement';
      fileUrl?: string;
      verified: boolean;
    };
  };

  // Additional Information
  additionalInfo?: {
    motivation: string;
    businessPlan?: string;
    references?: Array<{
      name: string;
      relationship: string;
      phone: string;
      email?: string;
    }>;
  };

  // Generated Credentials (after approval)
  generatedCredentials?: {
    userId: string;
    temporaryPassword: string;
    passwordResetRequired: boolean;
  };
}

export interface RegistrationApprovalAction {
  requestId: string;
  action: 'approve' | 'reject' | 'request_more_info';
  notes?: string;
  adminId: string;
  timestamp: string;
}

export interface AdminDashboardMetrics {
  platform: {
    totalHostels: number;
    activeHostels: number;
    totalUsers: number;
    activeUsers: number;
    totalRevenue: number;
    platformRevenue: number;
    occupancyRate: number;
    growthRate: number;
  };
  
  hostels: {
    registrationTrend: Array<{
      month: string;
      count: number;
      revenue: number;
    }>;
    topPerforming: Array<{
      id: string;
      name: string;
      city: string;
      area: string;
      occupancyRate: number;
      revenue: number;
      rating: number;
    }>;
    byLocation: Array<{
      city: string;
      areas: Array<{
        name: string;
        hostelCount: number;
        totalBeds: number;
        occupiedBeds: number;
        revenue: number;
      }>;
    }>;
  };

  users: {
    growthTrend: Array<{
      month: string;
      members: number;
      owners: number;
      employees: number;
    }>;
    registrationsByRole: {
      members: number;
      owners: number;
      employees: number;
    };
    activeUsers: {
      daily: number;
      weekly: number;
      monthly: number;
    };
  };

  payments: {
    transactionTrend: Array<{
      month: string;
      totalAmount: number;
      transactionCount: number;
      successRate: number;
    }>;
    methodDistribution: Array<{
      method: string;
      count: number;
      amount: number;
    }>;
    failureAnalysis: {
      totalFailures: number;
      failureRate: number;
      commonReasons: Array<{
        reason: string;
        count: number;
      }>;
    };
  };
}

export interface AdminHostelManagement {
  id: string;
  name: string;
  ownerId: string;
  ownerName: string;
  ownerEmail: string;
  location: {
    city: string;
    area: string;
    address: string;
    pincode: string;
  };
  
  // Enhanced room configuration with new sharing types
  roomConfiguration: {
    [key in SharingType]: {
      ac: {
        available: boolean;
        count: number;
        monthlyRate: number;
        dailyRate: number;
        occupied: number;
      };
      nonAc: {
        available: boolean;
        count: number;
        monthlyRate: number;
        dailyRate: number;
        occupied: number;
      };
    };
  };

  amenities: string[];
  status: 'active' | 'inactive' | 'pending' | 'suspended';
  registrationDate: string;
  lastUpdated: string;
  verificationStatus: {
    documents: boolean;
    inspection: boolean;
    compliance: boolean;
  };
  
  metrics: {
    totalBeds: number;
    occupiedBeds: number;
    occupancyRate: number;
    monthlyRevenue: number;
    averageRating: number;
    totalReviews: number;
  };

  compliance: {
    fireNoc: boolean;
    buildingPermit: boolean;
    businessLicense: boolean;
    taxCompliance: boolean;
    lastInspection: string;
  };
}

export interface AdminReportConfig {
  type: 'revenue' | 'occupancy' | 'user_growth' | 'payment_analysis' | 'hostel_performance';
  timeRange: {
    start: string;
    end: string;
  };
  filters: {
    cities?: string[];
    areas?: string[];
    hostels?: string[];
    userRoles?: string[];
    paymentMethods?: string[];
  };
  format: 'csv' | 'pdf' | 'excel';
  includeCharts: boolean;
  groupBy?: 'daily' | 'weekly' | 'monthly' | 'yearly';
}

export interface AdminBulkOperation {
  type: 'activate' | 'deactivate' | 'suspend' | 'verify' | 'update_pricing';
  targetIds: string[];
  targetType: 'hostels' | 'users' | 'bookings';
  parameters?: {
    status?: string;
    reason?: string;
    priceAdjustment?: number;
    notifyUsers?: boolean;
  };
  scheduledFor?: string;
  executedBy: string;
  executedAt?: string;
  results?: {
    successful: number;
    failed: number;
    errors: Array<{
      id: string;
      error: string;
    }>;
  };
}

export interface AdminAnalyticsFilter {
  dateRange: {
    start: string;
    end: string;
    preset?: '7d' | '30d' | '90d' | '1y' | 'custom';
  };
  location: {
    cities: string[];
    areas: string[];
  };
  hostelStatus: ('active' | 'inactive' | 'pending' | 'suspended')[];
  userRoles: ('member' | 'owner' | 'employee')[];
  paymentStatus: ('success' | 'failed' | 'pending')[];
  roomTypes: {
    sharingTypes: SharingType[];
    roomTypes: RoomType[];
  };
}

export interface AdminNotification {
  id: string;
  type: 'system' | 'hostel' | 'user' | 'payment' | 'compliance';
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  data?: any;
  createdAt: string;
  readAt?: string;
  actionRequired: boolean;
  actionUrl?: string;
}

export interface AdminAuditLog {
  id: string;
  userId: string;
  userName: string;
  action: string;
  resource: string;
  resourceId: string;
  changes?: {
    before: any;
    after: any;
  };
  timestamp: string;
  ipAddress: string;
  userAgent: string;
  success: boolean;
  errorMessage?: string;
}

export type AdminRole = 'superadmin' | 'admin' | 'moderator';
export type AdminPermission = 
  | 'view_all_hostels' 
  | 'manage_hostels' 
  | 'view_all_users' 
  | 'manage_users'
  | 'view_financial_data' 
  | 'manage_payments' 
  | 'view_analytics' 
  | 'export_data'
  | 'system_settings' 
  | 'audit_logs' 
  | 'bulk_operations';
