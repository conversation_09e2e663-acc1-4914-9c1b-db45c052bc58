// Admin-specific TypeScript interfaces for the room-buddy-hub application

import { SharingType, RoomType } from './owner';

export interface AdminDashboardMetrics {
  platform: {
    totalHostels: number;
    activeHostels: number;
    totalUsers: number;
    activeUsers: number;
    totalRevenue: number;
    platformRevenue: number;
    occupancyRate: number;
    growthRate: number;
  };
  
  hostels: {
    registrationTrend: Array<{
      month: string;
      count: number;
      revenue: number;
    }>;
    topPerforming: Array<{
      id: string;
      name: string;
      city: string;
      area: string;
      occupancyRate: number;
      revenue: number;
      rating: number;
    }>;
    byLocation: Array<{
      city: string;
      areas: Array<{
        name: string;
        hostelCount: number;
        totalBeds: number;
        occupiedBeds: number;
        revenue: number;
      }>;
    }>;
  };

  users: {
    growthTrend: Array<{
      month: string;
      members: number;
      owners: number;
      employees: number;
    }>;
    registrationsByRole: {
      members: number;
      owners: number;
      employees: number;
    };
    activeUsers: {
      daily: number;
      weekly: number;
      monthly: number;
    };
  };

  payments: {
    transactionTrend: Array<{
      month: string;
      totalAmount: number;
      transactionCount: number;
      successRate: number;
    }>;
    methodDistribution: Array<{
      method: string;
      count: number;
      amount: number;
    }>;
    failureAnalysis: {
      totalFailures: number;
      failureRate: number;
      commonReasons: Array<{
        reason: string;
        count: number;
      }>;
    };
  };
}

export interface AdminHostelManagement {
  id: string;
  name: string;
  ownerId: string;
  ownerName: string;
  ownerEmail: string;
  location: {
    city: string;
    area: string;
    address: string;
    pincode: string;
  };
  
  // Enhanced room configuration with new sharing types
  roomConfiguration: {
    [key in SharingType]: {
      ac: {
        available: boolean;
        count: number;
        monthlyRate: number;
        dailyRate: number;
        occupied: number;
      };
      nonAc: {
        available: boolean;
        count: number;
        monthlyRate: number;
        dailyRate: number;
        occupied: number;
      };
    };
  };

  amenities: string[];
  status: 'active' | 'inactive' | 'pending' | 'suspended';
  registrationDate: string;
  lastUpdated: string;
  verificationStatus: {
    documents: boolean;
    inspection: boolean;
    compliance: boolean;
  };
  
  metrics: {
    totalBeds: number;
    occupiedBeds: number;
    occupancyRate: number;
    monthlyRevenue: number;
    averageRating: number;
    totalReviews: number;
  };

  compliance: {
    fireNoc: boolean;
    buildingPermit: boolean;
    businessLicense: boolean;
    taxCompliance: boolean;
    lastInspection: string;
  };
}

export interface AdminReportConfig {
  type: 'revenue' | 'occupancy' | 'user_growth' | 'payment_analysis' | 'hostel_performance';
  timeRange: {
    start: string;
    end: string;
  };
  filters: {
    cities?: string[];
    areas?: string[];
    hostels?: string[];
    userRoles?: string[];
    paymentMethods?: string[];
  };
  format: 'csv' | 'pdf' | 'excel';
  includeCharts: boolean;
  groupBy?: 'daily' | 'weekly' | 'monthly' | 'yearly';
}

export interface AdminBulkOperation {
  type: 'activate' | 'deactivate' | 'suspend' | 'verify' | 'update_pricing';
  targetIds: string[];
  targetType: 'hostels' | 'users' | 'bookings';
  parameters?: {
    status?: string;
    reason?: string;
    priceAdjustment?: number;
    notifyUsers?: boolean;
  };
  scheduledFor?: string;
  executedBy: string;
  executedAt?: string;
  results?: {
    successful: number;
    failed: number;
    errors: Array<{
      id: string;
      error: string;
    }>;
  };
}

export interface AdminAnalyticsFilter {
  dateRange: {
    start: string;
    end: string;
    preset?: '7d' | '30d' | '90d' | '1y' | 'custom';
  };
  location: {
    cities: string[];
    areas: string[];
  };
  hostelStatus: ('active' | 'inactive' | 'pending' | 'suspended')[];
  userRoles: ('member' | 'owner' | 'employee')[];
  paymentStatus: ('success' | 'failed' | 'pending')[];
  roomTypes: {
    sharingTypes: SharingType[];
    roomTypes: RoomType[];
  };
}

export interface AdminNotification {
  id: string;
  type: 'system' | 'hostel' | 'user' | 'payment' | 'compliance';
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  data?: any;
  createdAt: string;
  readAt?: string;
  actionRequired: boolean;
  actionUrl?: string;
}

export interface AdminAuditLog {
  id: string;
  userId: string;
  userName: string;
  action: string;
  resource: string;
  resourceId: string;
  changes?: {
    before: any;
    after: any;
  };
  timestamp: string;
  ipAddress: string;
  userAgent: string;
  success: boolean;
  errorMessage?: string;
}

export type AdminRole = 'superadmin' | 'admin' | 'moderator';
export type AdminPermission = 
  | 'view_all_hostels' 
  | 'manage_hostels' 
  | 'view_all_users' 
  | 'manage_users'
  | 'view_financial_data' 
  | 'manage_payments' 
  | 'view_analytics' 
  | 'export_data'
  | 'system_settings' 
  | 'audit_logs' 
  | 'bulk_operations';
