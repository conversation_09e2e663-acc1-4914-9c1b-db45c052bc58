import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { 
  Clock, 
  MapPin,
  Bed,
  User,
  Calendar,
  CheckCircle,
  XCircle,
  AlertTriangle,
  QrCode,
  Camera,
  FileText,
  CreditCard,
  Timer
} from 'lucide-react';
import { type Booking, type Hostel, type User as UserType } from '@/data/mockData';
import { BillingCalculator } from './BillingCalculator';

interface CheckInOutManagerProps {
  booking: Booking;
  hostel: Hostel;
  user: UserType;
  mode: 'checkin' | 'checkout';
  onComplete: (result: {
    success: boolean;
    timestamp: string;
    bedNumber?: string;
    finalBill?: any;
    notes?: string;
  }) => void;
  className?: string;
}

export const CheckInOutManager: React.FC<CheckInOutManagerProps> = ({
  booking,
  hostel,
  user,
  mode,
  onComplete,
  className
}) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [assignedBed, setAssignedBed] = useState(booking.bedNumber || '');
  const [notes, setNotes] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [stayDuration, setStayDuration] = useState<{
    days: number;
    hours: number;
    minutes: number;
  } | null>(null);

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Calculate stay duration for checkout
  useEffect(() => {
    if (mode === 'checkout' && booking.actualCheckIn) {
      const checkInTime = new Date(booking.actualCheckIn);
      const now = new Date();
      const diffMs = now.getTime() - checkInTime.getTime();
      
      const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
      
      setStayDuration({ days, hours, minutes });
    }
  }, [mode, booking.actualCheckIn]);

  const handleProcess = async () => {
    setIsProcessing(true);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      const timestamp = currentTime.toISOString();
      
      if (mode === 'checkin') {
        // Check-in process
        onComplete({
          success: true,
          timestamp,
          bedNumber: assignedBed,
          notes
        });
      } else {
        // Check-out process
        onComplete({
          success: true,
          timestamp,
          notes,
          finalBill: {
            // This would be calculated by the billing system
            subtotal: booking.amount,
            securityDeposit: booking.securityDeposit || 0,
            total: booking.amount + (booking.securityDeposit || 0)
          }
        });
      }
    } catch (error) {
      onComplete({
        success: false,
        timestamp: currentTime.toISOString(),
        notes: 'Process failed: ' + (error as Error).message
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const formatDuration = (duration: { days: number; hours: number; minutes: number }) => {
    const parts = [];
    if (duration.days > 0) parts.push(`${duration.days} day${duration.days > 1 ? 's' : ''}`);
    if (duration.hours > 0) parts.push(`${duration.hours} hour${duration.hours > 1 ? 's' : ''}`);
    if (duration.minutes > 0) parts.push(`${duration.minutes} minute${duration.minutes > 1 ? 's' : ''}`);
    return parts.join(', ') || '0 minutes';
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            {mode === 'checkin' ? (
              <>
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span>Guest Check-In</span>
              </>
            ) : (
              <>
                <XCircle className="h-5 w-5 text-blue-600" />
                <span>Guest Check-Out</span>
              </>
            )}
          </CardTitle>
          <CardDescription>
            {mode === 'checkin' 
              ? 'Complete the check-in process and assign bed'
              : 'Process check-out and generate final bill'
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Current Time */}
          <div className="flex items-center justify-center p-4 bg-muted/50 rounded-lg">
            <div className="text-center">
              <div className="flex items-center justify-center space-x-2 text-2xl font-mono font-bold">
                <Clock className="h-6 w-6" />
                <span>{currentTime.toLocaleTimeString()}</span>
              </div>
              <p className="text-sm text-muted-foreground mt-1">
                {currentTime.toLocaleDateString('en-IN', { 
                  weekday: 'long', 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Booking Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Booking Details</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <User className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="font-medium">{user.name}</p>
                  <p className="text-sm text-muted-foreground">{user.email}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="font-medium">{hostel.name}</p>
                  <p className="text-sm text-muted-foreground">{hostel.address}</p>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm">Booking ID:</span>
                <span className="font-medium">{booking.id}</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-sm">Booking Type:</span>
                <Badge variant="secondary">
                  {booking.bookingType === 'monthly' ? 'Monthly' : 'Daily'}
                </Badge>
              </div>
              
              <div className="flex justify-between">
                <span className="text-sm">Bed Type:</span>
                <Badge variant="outline">
                  {booking.bedType === 'standard' ? 'Standard' : 'Premium'}
                </Badge>
              </div>
              
              <div className="flex justify-between">
                <span className="text-sm">Food Package:</span>
                <Badge variant={booking.foodPackage === 'with_meals' ? 'default' : 'secondary'}>
                  {booking.foodPackage === 'with_meals' ? 'With Meals' : 'Without Meals'}
                </Badge>
              </div>
            </div>
          </div>

          {/* Stay Duration for Checkout */}
          {mode === 'checkout' && stayDuration && (
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <Timer className="h-4 w-4 text-blue-600" />
                <span className="font-medium text-blue-800">Total Stay Duration</span>
              </div>
              <p className="text-lg font-bold text-blue-900">
                {formatDuration(stayDuration)}
              </p>
              {booking.actualCheckIn && (
                <p className="text-sm text-blue-700 mt-1">
                  From: {new Date(booking.actualCheckIn).toLocaleString()}
                </p>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Check-in Specific Fields */}
      {mode === 'checkin' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Bed className="h-5 w-5" />
              <span>Bed Assignment</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="bedNumber">Assign Bed Number</Label>
              <Input
                id="bedNumber"
                value={assignedBed}
                onChange={(e) => setAssignedBed(e.target.value)}
                placeholder="e.g., A101, B205"
              />
              <p className="text-xs text-muted-foreground">
                Available {booking.bedType} beds: {hostel.bedTypes[booking.bedType].available}
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="verificationCode">Verification Code (Optional)</Label>
              <Input
                id="verificationCode"
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value)}
                placeholder="Enter verification code"
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Check-out Billing */}
      {mode === 'checkout' && stayDuration && (
        <BillingCalculator
          hostel={hostel}
          membershipType={user.membershipType || 'guest'}
          bedType={booking.bedType}
          foodPackage={booking.foodPackage}
          stayDuration={stayDuration}
          checkInTime={booking.actualCheckIn ? new Date(booking.actualCheckIn) : undefined}
          checkOutTime={currentTime}
        />
      )}

      {/* Notes */}
      <Card>
        <CardHeader>
          <CardTitle>Additional Notes</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Input
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder={mode === 'checkin' 
                ? "Any special instructions or observations..."
                : "Checkout notes, damages, or feedback..."
              }
            />
          </div>
        </CardContent>
      </Card>

      {/* Action Button */}
      <Card>
        <CardContent className="pt-6">
          <Button 
            onClick={handleProcess}
            disabled={isProcessing || (mode === 'checkin' && !assignedBed.trim())}
            className="w-full"
            size="lg"
          >
            {isProcessing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Processing...
              </>
            ) : (
              <>
                {mode === 'checkin' ? (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Complete Check-In
                  </>
                ) : (
                  <>
                    <CreditCard className="h-4 w-4 mr-2" />
                    Process Check-Out & Generate Bill
                  </>
                )}
              </>
            )}
          </Button>
          
          {mode === 'checkin' && !assignedBed.trim() && (
            <p className="text-sm text-red-600 mt-2 text-center">
              Please assign a bed number to proceed
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
