import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Building2,
  Users,
  DollarSign,
  TrendingUp,
  TrendingDown,
  ArrowUpRight,
  ArrowDownRight,
  Calendar,
  Clock,
  Star,
  AlertTriangle,
  CheckCircle,
  Eye,
  Plus,
  Settings,
  RefreshCw,
  BarChart3,
  PieChart,
  Activity,
  Target,
  Zap,
  CreditCard,
  UserCheck,
  Bed,
  Receipt
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { mockHostels, mockBookings, mockPayments, mockUsers } from '@/data/mockData';
import { OwnerDashboardMetrics } from '@/types/owner';

// Mock owner dashboard data
const mockOwnerMetrics: OwnerDashboardMetrics = {
  occupancy: {
    current: 85,
    target: 90,
    trend: 'up',
    lastMonth: 82
  },
  revenue: {
    thisMonth: 245000,
    lastMonth: 230000,
    projected: 260000,
    trend: 'up'
  },
  applications: {
    pending: 12,
    thisWeek: 8,
    conversionRate: 78,
    averageProcessingTime: 2.5
  },
  memberSatisfaction: {
    averageRating: 4.3,
    totalReviews: 156,
    responseRate: 92,
    issueResolutionTime: 4.2
  },
  payments: {
    collected: 220000,
    pending: 25000,
    overdue: 8000,
    successRate: 96.5
  }
};

export const OwnerDashboard: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [refreshing, setRefreshing] = useState(false);

  // Get owner's hostel data (assuming owner manages one hostel for demo)
  const ownerHostel = mockHostels[0]; // In real app, filter by owner ID
  const hostelBookings = mockBookings.filter(b => b.hostelId === ownerHostel.id);
  const hostelPayments = mockPayments.filter(p => hostelBookings.some(b => b.id === p.bookingId));

  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  const metrics = mockOwnerMetrics;

  // Quick action cards
  const quickActions = [
    {
      title: 'Member Applications',
      description: `${metrics.applications.pending} pending review`,
      icon: UserCheck,
      color: 'bg-blue-50 text-blue-600',
      action: () => navigate('/owner/member-applications')
    },
    {
      title: 'Room Allocation',
      description: 'Manage bed assignments',
      icon: Bed,
      color: 'bg-green-50 text-green-600',
      action: () => navigate('/owner/room-allocation')
    },
    {
      title: 'Payment Management',
      description: `₹${metrics.payments.pending.toLocaleString()} pending`,
      icon: CreditCard,
      color: 'bg-purple-50 text-purple-600',
      action: () => navigate('/owner/payments')
    },
    {
      title: 'Hostel Settings',
      description: 'Update hostel information',
      icon: Settings,
      color: 'bg-orange-50 text-orange-600',
      action: () => navigate('/owner/settings')
    }
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold tracking-tight">Owner Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome back! Here's what's happening with {ownerHostel.name}
          </p>
        </div>
        <div className="flex flex-col space-y-2 md:flex-row md:space-x-2 md:space-y-0">
          <Button variant="outline" onClick={handleRefresh} disabled={refreshing} className="w-full md:w-auto">
            <RefreshCw className={`mr-2 h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button className="w-full md:w-auto">
            <BarChart3 className="mr-2 h-4 w-4" />
            View Reports
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Occupancy Rate</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.occupancy.current}%</div>
            <div className="flex items-center text-xs text-muted-foreground">
              {metrics.occupancy.trend === 'up' ? (
                <ArrowUpRight className="mr-1 h-3 w-3 text-green-500" />
              ) : (
                <ArrowDownRight className="mr-1 h-3 w-3 text-red-500" />
              )}
              <span className={metrics.occupancy.trend === 'up' ? 'text-green-600' : 'text-red-600'}>
                +{metrics.occupancy.current - metrics.occupancy.lastMonth}%
              </span>
              <span className="ml-1">from last month</span>
            </div>
            <div className="mt-2">
              <Progress value={metrics.occupancy.current} className="h-2" />
              <div className="flex justify-between text-xs text-muted-foreground mt-1">
                <span>Current</span>
                <span>Target: {metrics.occupancy.target}%</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{metrics.revenue.thisMonth.toLocaleString()}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              {metrics.revenue.trend === 'up' ? (
                <ArrowUpRight className="mr-1 h-3 w-3 text-green-500" />
              ) : (
                <ArrowDownRight className="mr-1 h-3 w-3 text-red-500" />
              )}
              <span className={metrics.revenue.trend === 'up' ? 'text-green-600' : 'text-red-600'}>
                +{Math.round(((metrics.revenue.thisMonth - metrics.revenue.lastMonth) / metrics.revenue.lastMonth) * 100)}%
              </span>
              <span className="ml-1">from last month</span>
            </div>
            <div className="text-xs text-muted-foreground mt-1">
              Projected: ₹{metrics.revenue.projected.toLocaleString()}
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Applications</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.applications.pending}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <Clock className="mr-1 h-3 w-3" />
              <span>{metrics.applications.thisWeek} this week</span>
            </div>
            <div className="text-xs text-muted-foreground mt-1">
              Conversion: {metrics.applications.conversionRate}%
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Member Satisfaction</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.memberSatisfaction.averageRating}/5</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <Star className="mr-1 h-3 w-3 fill-yellow-400 text-yellow-400" />
              <span>{metrics.memberSatisfaction.totalReviews} reviews</span>
            </div>
            <div className="text-xs text-muted-foreground mt-1">
              Response rate: {metrics.memberSatisfaction.responseRate}%
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="h-5 w-5" />
            <span>Quick Actions</span>
          </CardTitle>
          <CardDescription>
            Common tasks and important actions that need your attention
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
            {quickActions.map((action, index) => (
              <div
                key={index}
                className="p-4 border rounded-lg cursor-pointer hover:shadow-md transition-shadow"
                onClick={action.action}
              >
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${action.color}`}>
                    <action.icon className="h-5 w-5" />
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-sm">{action.title}</div>
                    <div className="text-xs text-muted-foreground">{action.description}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Payment Overview */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <CreditCard className="h-5 w-5" />
                <span>Payment Overview</span>
              </CardTitle>
              <CardDescription>
                Current month payment status and trends
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 bg-green-50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium text-green-800">Collected</div>
                      <div className="text-lg font-bold text-green-900">
                        ₹{metrics.payments.collected.toLocaleString()}
                      </div>
                    </div>
                    <CheckCircle className="h-8 w-8 text-green-600" />
                  </div>
                </div>

                <div className="p-4 bg-yellow-50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium text-yellow-800">Pending</div>
                      <div className="text-lg font-bold text-yellow-900">
                        ₹{metrics.payments.pending.toLocaleString()}
                      </div>
                    </div>
                    <Clock className="h-8 w-8 text-yellow-600" />
                  </div>
                </div>

                <div className="p-4 bg-red-50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium text-red-800">Overdue</div>
                      <div className="text-lg font-bold text-red-900">
                        ₹{metrics.payments.overdue.toLocaleString()}
                      </div>
                    </div>
                    <AlertTriangle className="h-8 w-8 text-red-600" />
                  </div>
                </div>
              </div>

              <div className="pt-4 border-t">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm font-medium">Payment Success Rate</div>
                    <div className="text-2xl font-bold">{metrics.payments.successRate}%</div>
                  </div>
                  <Button variant="outline" size="sm">
                    <Receipt className="mr-2 h-4 w-4" />
                    View Details
                  </Button>
                </div>
                <Progress value={metrics.payments.successRate} className="mt-2" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="h-5 w-5" />
                <span>Recent Activity</span>
              </CardTitle>
              <CardDescription>
                Latest updates and notifications
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <div className="p-1 bg-green-100 rounded-full">
                    <CheckCircle className="h-3 w-3 text-green-600" />
                  </div>
                  <div className="flex-1 text-sm">
                    <div className="font-medium">New member approved</div>
                    <div className="text-muted-foreground">Rohit Verma - Bed A101-1</div>
                    <div className="text-xs text-muted-foreground">2 hours ago</div>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="p-1 bg-blue-100 rounded-full">
                    <DollarSign className="h-3 w-3 text-blue-600" />
                  </div>
                  <div className="flex-1 text-sm">
                    <div className="font-medium">Payment received</div>
                    <div className="text-muted-foreground">₹15,000 from Priya Sharma</div>
                    <div className="text-xs text-muted-foreground">4 hours ago</div>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="p-1 bg-yellow-100 rounded-full">
                    <AlertTriangle className="h-3 w-3 text-yellow-600" />
                  </div>
                  <div className="flex-1 text-sm">
                    <div className="font-medium">Maintenance required</div>
                    <div className="text-muted-foreground">Room A102 - AC repair</div>
                    <div className="text-xs text-muted-foreground">6 hours ago</div>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="p-1 bg-purple-100 rounded-full">
                    <Star className="h-3 w-3 text-purple-600" />
                  </div>
                  <div className="flex-1 text-sm">
                    <div className="font-medium">New review received</div>
                    <div className="text-muted-foreground">5 stars from Amit Kumar</div>
                    <div className="text-xs text-muted-foreground">1 day ago</div>
                  </div>
                </div>
              </div>

              <Button variant="outline" size="sm" className="w-full">
                <Eye className="mr-2 h-4 w-4" />
                View All Activity
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
