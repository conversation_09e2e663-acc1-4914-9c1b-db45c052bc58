// Mock data for the hostel management system

import { HostelRegistrationRequest, AccountCreationRequest, HostelOwnerAccessRequest } from '@/types/admin';
import {
  Room,
  Bed,
  RoomModificationRequest,
  RoomStatistics,
  HostelInfo,
  Floor,
  FloorModificationRequest,
  FloorStatistics,
  HostelStatistics
} from '@/types/roomManagement';

export interface Hostel {
  id: string;
  name: string;
  address: string;
  city: string;
  state: string;
  pincode: string;
  areaId: string;
  totalBeds: number;
  availableBeds: number;
  pricePerBed: number;
  amenities: string[];
  rating: number;
  images: string[];
  ownerId: string;
  employees: string[];
  status: 'active' | 'inactive';
  bedTypes: {
    standard: {
      count: number;
      available: number;
      monthlyRate: number;
      dailyRate: number;
    };
    premium: {
      count: number;
      available: number;
      monthlyRate: number;
      dailyRate: number;
    };
  };
  foodPackages: {
    withMeals: {
      dailyRate: number;
      description: string;
    };
    withoutMeals: {
      dailyRate: number;
      description: string;
    };
  };
  securityDeposit: number;
  minimumStayHours: number;
  hourlyRate?: number;
}

export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  role: 'superadmin' | 'owner' | 'employee' | 'member';
  hostelId?: string;
  avatar?: string;
  joinedDate: string;
  status: 'active' | 'inactive';
  membershipType?: 'regular' | 'guest';
  registrationStatus?: 'pending' | 'approved' | 'rejected';
  idProof?: {
    type: 'aadhar' | 'pan' | 'passport' | 'driving_license';
    number: string;
    verified: boolean;
  };
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
  emailVerified?: boolean;
}

export interface Booking {
  id: string;
  userId: string;
  hostelId: string;
  bedNumber: string;
  checkIn: string;
  checkOut: string;
  amount: number;
  status: 'confirmed' | 'pending' | 'cancelled' | 'checked_in' | 'checked_out';
  paymentStatus: 'paid' | 'pending' | 'failed';
  bookingType: 'monthly' | 'daily';
  actualCheckIn?: string; // ISO timestamp
  actualCheckOut?: string; // ISO timestamp
  securityDeposit?: number;
  bedType: 'standard' | 'premium';
  foodPackage: 'with_meals' | 'without_meals';
  dailyFoodRate?: number;
  billingItems?: BillingItem[];
  foodConsumption?: FoodConsumption[];
}

export interface Payment {
  id: string;
  bookingId: string;
  userId: string;
  amount: number;
  date: string;
  method: 'card' | 'upi' | 'bank_transfer';
  status: 'success' | 'pending' | 'failed';
  splitDetails: {
    hostelOwner: number;
    platform: number;
  };
}

export interface Complaint {
  id: string;
  userId: string;
  hostelId: string;
  title: string;
  description: string;
  category: 'maintenance' | 'cleanliness' | 'noise' | 'service' | 'other';
  status: 'open' | 'in_progress' | 'resolved';
  priority: 'low' | 'medium' | 'high';
  createdDate: string;
  resolvedDate?: string;
}

export interface BillingItem {
  id: string;
  bookingId: string;
  type: 'bed_charge' | 'food_charge' | 'additional_service' | 'security_deposit' | 'refund';
  description: string;
  amount: number;
  quantity: number;
  date: string;
  category?: string;
}

export interface FoodConsumption {
  id: string;
  bookingId: string;
  date: string;
  mealType: 'breakfast' | 'lunch' | 'dinner';
  consumed: boolean;
  cost: number;
  notes?: string;
}

export interface Area {
  id: string;
  name: string;
  cityId: string;
}

export interface City {
  id: string;
  name: string;
  state: string;
  areas: Area[];
}

// Mock Data
export const mockCities: City[] = [
  {
    id: 'mumbai',
    name: 'Mumbai',
    state: 'Maharashtra',
    areas: [
      { id: 'andheri', name: 'Andheri', cityId: 'mumbai' },
      { id: 'bandra', name: 'Bandra', cityId: 'mumbai' },
      { id: 'powai', name: 'Powai', cityId: 'mumbai' },
      { id: 'malad', name: 'Malad', cityId: 'mumbai' }
    ]
  },
  {
    id: 'pune',
    name: 'Pune',
    state: 'Maharashtra',
    areas: [
      { id: 'koregaon-park', name: 'Koregaon Park', cityId: 'pune' },
      { id: 'hinjewadi', name: 'Hinjewadi', cityId: 'pune' },
      { id: 'kothrud', name: 'Kothrud', cityId: 'pune' },
      { id: 'viman-nagar', name: 'Viman Nagar', cityId: 'pune' }
    ]
  },
  {
    id: 'bangalore',
    name: 'Bangalore',
    state: 'Karnataka',
    areas: [
      { id: 'koramangala', name: 'Koramangala', cityId: 'bangalore' },
      { id: 'whitefield', name: 'Whitefield', cityId: 'bangalore' },
      { id: 'indiranagar', name: 'Indiranagar', cityId: 'bangalore' },
      { id: 'electronic-city', name: 'Electronic City', cityId: 'bangalore' }
    ]
  }
];

export const mockUsers: User[] = [
  {
    id: '1',
    name: 'Admin User',
    email: '<EMAIL>',
    phone: '+91 **********',
    role: 'superadmin',
    joinedDate: '2023-01-01',
    status: 'active'
  },
  {
    id: '2',
    name: 'Rajesh Kumar',
    email: '<EMAIL>',
    phone: '+91 9876543211',
    role: 'owner',
    hostelId: '1',
    joinedDate: '2023-02-15',
    status: 'active'
  },
  {
    id: '3',
    name: 'Priya Sharma',
    email: '<EMAIL>',
    phone: '+91 9876543212',
    role: 'employee',
    hostelId: '1',
    joinedDate: '2023-03-10',
    status: 'active'
  },
  {
    id: '4',
    name: 'Amit Singh',
    email: '<EMAIL>',
    phone: '+91 9876543213',
    role: 'employee',
    hostelId: '1',
    joinedDate: '2023-03-15',
    status: 'active'
  },
  {
    id: '11',
    name: 'Rohit Verma',
    email: '<EMAIL>',
    phone: '+91 9876543220',
    role: 'member',
    joinedDate: '2023-06-01',
    status: 'active'
  },
  {
    id: '12',
    name: 'Sneha Patel',
    email: '<EMAIL>',
    phone: '+91 9876543221',
    role: 'member',
    joinedDate: '2023-06-15',
    status: 'active'
  }
];

export const mockBookings: Booking[] = [
  {
    id: '1',
    userId: '11',
    hostelId: '1',
    bedNumber: 'A101',
    checkIn: '2024-01-01',
    checkOut: '2024-06-30',
    amount: 48000,
    status: 'confirmed',
    paymentStatus: 'paid',
    bookingType: 'monthly',
    bedType: 'standard',
    foodPackage: 'with_meals',
    securityDeposit: 5000,
    dailyFoodRate: 200,
    actualCheckIn: '2024-01-01T10:30:00Z',
    billingItems: [
      {
        id: 'bill_1_1',
        bookingId: '1',
        type: 'bed_charge',
        description: 'Monthly bed charge (6 months)',
        amount: 42000,
        quantity: 6,
        date: '2024-01-01',
        category: 'accommodation'
      },
      {
        id: 'bill_1_2',
        bookingId: '1',
        type: 'food_charge',
        description: 'Food charges (6 months)',
        amount: 36000,
        quantity: 180,
        date: '2024-01-01',
        category: 'food'
      }
    ]
  },
  {
    id: '2',
    userId: '12',
    hostelId: '3',
    bedNumber: 'B205',
    checkIn: '2024-02-01',
    checkOut: '2024-07-31',
    amount: 72000,
    status: 'confirmed',
    paymentStatus: 'paid',
    bookingType: 'monthly',
    bedType: 'premium',
    foodPackage: 'with_meals',
    securityDeposit: 8000,
    dailyFoodRate: 250,
    actualCheckIn: '2024-02-01T14:15:00Z'
  }
];

export const mockPayments: Payment[] = [
  {
    id: '1',
    bookingId: '1',
    userId: '11',
    amount: 48000,
    date: '2023-12-25',
    method: 'upi',
    status: 'success',
    splitDetails: {
      hostelOwner: 45600,
      platform: 2400
    }
  },
  {
    id: '2',
    bookingId: '2',
    userId: '12',
    amount: 72000,
    date: '2024-01-25',
    method: 'card',
    status: 'success',
    splitDetails: {
      hostelOwner: 68400,
      platform: 3600
    }
  }
];

export const mockComplaints: Complaint[] = [
  {
    id: '1',
    userId: '11',
    hostelId: '1',
    title: 'AC not working in room A101',
    description: 'The air conditioning unit has stopped working since yesterday. The room is getting very hot.',
    category: 'maintenance',
    status: 'in_progress',
    priority: 'high',
    createdDate: '2024-01-15'
  },
  {
    id: '2',
    userId: '12',
    hostelId: '3',
    title: 'Noisy neighbors',
    description: 'There is excessive noise from the room next door during late night hours.',
    category: 'noise',
    status: 'open',
    priority: 'medium',
    createdDate: '2024-01-20'
  }
];

// Helper functions
export const getUserById = (id: string) => mockUsers.find(user => user.id === id);
export const getBookingsByUserId = (userId: string) => mockBookings.filter(booking => booking.userId === userId);
export const getComplaintsByHostelId = (hostelId: string) => mockComplaints.filter(complaint => complaint.hostelId === hostelId);
export const getCityById = (id: string) => mockCities.find(city => city.id === id);
export const getAreaById = (cityId: string, areaId: string) => {
  const city = getCityById(cityId);
  return city?.areas.find(area => area.id === areaId);
};
export const getHostelsByArea = (areaId: string) => mockHostels.filter(hostel => hostel.areaId === areaId);
export const getAvailableBeds = (hostelId: string, bedType: 'standard' | 'premium') => {
  const hostel = getHostelById(hostelId);
  return hostel?.bedTypes[bedType]?.available || 0;
};

// Mock Registration Requests Data
export const mockHostelRegistrationRequests: HostelRegistrationRequest[] = [
  {
    id: 'hr_001',
    requestType: 'hostel_registration',
    submittedBy: 'pending_owner_001',
    submittedAt: '2024-01-15T10:30:00Z',
    status: 'pending',
    hostelDetails: {
      name: 'Green Valley Hostel',
      address: '456 University Road',
      city: 'Pune',
      state: 'Maharashtra',
      pincode: '411001',
      areaId: 'kothrud',
      totalBeds: 80,
      amenities: ['WiFi', 'AC', 'Laundry', 'Mess', 'Security', 'Parking'],
      pricePerBed: 7500,
      bedTypes: {
        standard: {
          count: 50,
          monthlyRate: 6500,
          dailyRate: 280
        },
        premium: {
          count: 30,
          monthlyRate: 8500,
          dailyRate: 380
        }
      },
      foodPackages: {
        withMeals: {
          dailyRate: 180,
          description: 'Includes breakfast, lunch, and dinner'
        },
        withoutMeals: {
          dailyRate: 0,
          description: 'No meals included'
        }
      },
      securityDeposit: 7000,
      minimumStayHours: 6,
      hourlyRate: 60
    },
    ownerDetails: {
      name: 'Suresh Patil',
      email: '<EMAIL>',
      phone: '+91 9876543220',
      alternatePhone: '+91 9876543221',
      businessExperience: 5,
      previousHostelExperience: true
    },
    documents: {
      ownerIdProof: {
        type: 'aadhar',
        number: '1234-5678-9012',
        fileUrl: '/documents/aadhar_suresh.pdf',
        verified: false
      },
      businessRegistration: {
        type: 'gst',
        number: 'GST123456789',
        fileUrl: '/documents/gst_certificate.pdf',
        verified: false
      },
      propertyDocuments: {
        type: 'ownership',
        fileUrl: '/documents/property_deed.pdf',
        verified: false
      },
      bankDetails: {
        accountNumber: '**********',
        ifscCode: 'HDFC0001234',
        bankName: 'HDFC Bank',
        branchName: 'Kothrud Branch',
        accountHolderName: 'Suresh Patil',
        accountType: 'current'
      }
    },
    photos: {
      exterior: ['/photos/green_valley_exterior_1.jpg', '/photos/green_valley_exterior_2.jpg'],
      rooms: ['/photos/green_valley_room_1.jpg', '/photos/green_valley_room_2.jpg'],
      commonAreas: ['/photos/green_valley_common_1.jpg'],
      amenities: ['/photos/green_valley_amenities_1.jpg'],
      kitchen: ['/photos/green_valley_kitchen_1.jpg']
    }
  },
  {
    id: 'hr_002',
    requestType: 'hostel_registration',
    submittedBy: 'pending_owner_002',
    submittedAt: '2024-01-18T14:45:00Z',
    status: 'under_review',
    reviewedBy: '1',
    hostelDetails: {
      name: 'Tech Hub Residency',
      address: '789 IT Park Road',
      city: 'Bangalore',
      state: 'Karnataka',
      pincode: '560001',
      areaId: 'koramangala',
      totalBeds: 120,
      amenities: ['WiFi', 'AC', 'Laundry', 'Gym', 'Security', 'Parking', 'Conference Room'],
      pricePerBed: 9000,
      bedTypes: {
        standard: {
          count: 70,
          monthlyRate: 8000,
          dailyRate: 350
        },
        premium: {
          count: 50,
          monthlyRate: 10000,
          dailyRate: 450
        }
      },
      foodPackages: {
        withMeals: {
          dailyRate: 220,
          description: 'Includes breakfast, lunch, and dinner'
        },
        withoutMeals: {
          dailyRate: 0,
          description: 'No meals included'
        }
      },
      securityDeposit: 9000,
      minimumStayHours: 8,
      hourlyRate: 80
    },
    ownerDetails: {
      name: 'Priya Sharma',
      email: '<EMAIL>',
      phone: '+91 9876543230',
      businessExperience: 8,
      previousHostelExperience: false
    },
    documents: {
      ownerIdProof: {
        type: 'pan',
        number: '**********',
        fileUrl: '/documents/pan_priya.pdf',
        verified: true
      },
      businessRegistration: {
        type: 'gst',
        number: 'GST987654321',
        fileUrl: '/documents/gst_techhub.pdf',
        verified: true
      },
      propertyDocuments: {
        type: 'lease_deed',
        fileUrl: '/documents/lease_techhub.pdf',
        verified: false
      },
      bankDetails: {
        accountNumber: '**********',
        ifscCode: 'ICICI0001234',
        bankName: 'ICICI Bank',
        branchName: 'Koramangala Branch',
        accountHolderName: 'Priya Sharma',
        accountType: 'current'
      }
    },
    photos: {
      exterior: ['/photos/techhub_exterior_1.jpg'],
      rooms: ['/photos/techhub_room_1.jpg', '/photos/techhub_room_2.jpg', '/photos/techhub_room_3.jpg'],
      commonAreas: ['/photos/techhub_common_1.jpg', '/photos/techhub_common_2.jpg'],
      amenities: ['/photos/techhub_gym_1.jpg', '/photos/techhub_conference_1.jpg']
    }
  }
];

export const mockAccountCreationRequests: AccountCreationRequest[] = [
  {
    id: 'ac_001',
    requestType: 'account_creation',
    submittedAt: '2024-01-20T09:15:00Z',
    status: 'pending',
    requestedUserDetails: {
      name: 'Rajesh Kumar',
      email: '<EMAIL>',
      phone: '+91 **********',
      role: 'owner',
      businessType: 'Hospitality',
      experienceYears: 3,
      referenceContact: '+91 **********'
    },
    documents: {
      idProof: {
        type: 'aadhar',
        number: '**************',
        fileUrl: '/documents/aadhar_rajesh.pdf'
      },
      businessProof: {
        type: 'shop_act',
        number: 'SA123456789',
        fileUrl: '/documents/shop_act_rajesh.pdf'
      }
    }
  },
  {
    id: 'ac_002',
    requestType: 'account_creation',
    submittedAt: '2024-01-22T16:30:00Z',
    status: 'pending',
    requestedUserDetails: {
      name: 'Anita Desai',
      email: '<EMAIL>',
      phone: '+91 **********',
      role: 'owner',
      businessType: 'Real Estate',
      experienceYears: 7,
      referenceContact: '+91 **********'
    },
    documents: {
      idProof: {
        type: 'pan',
        number: '**********',
        fileUrl: '/documents/pan_anita.pdf'
      },
      businessProof: {
        type: 'gst',
        number: 'GST555666777',
        fileUrl: '/documents/gst_anita.pdf'
      }
    }
  }
];

// Helper functions for registration requests
export const getHostelRegistrationRequestById = (id: string) =>
  mockHostelRegistrationRequests.find(request => request.id === id);

export const getAccountCreationRequestById = (id: string) =>
  mockAccountCreationRequests.find(request => request.id === id);

export const getPendingHostelRegistrations = () =>
  mockHostelRegistrationRequests.filter(request => request.status === 'pending');

export const getPendingAccountCreations = () =>
  mockAccountCreationRequests.filter(request => request.status === 'pending');

export const getAllPendingRequests = () => [
  ...mockHostelRegistrationRequests.filter(request => request.status === 'pending'),
  ...mockAccountCreationRequests.filter(request => request.status === 'pending')
];

export const getRegistrationRequestsByStatus = (status: 'pending' | 'approved' | 'rejected' | 'under_review') => [
  ...mockHostelRegistrationRequests.filter(request => request.status === status),
  ...mockAccountCreationRequests.filter(request => request.status === status)
];

// Mock Hostel Owner Access Requests Data
export const mockHostelOwnerAccessRequests: HostelOwnerAccessRequest[] = [
  {
    id: 'oar_001',
    requestType: 'owner_access',
    submittedAt: '2024-01-25T11:30:00Z',
    status: 'pending',
    applicantDetails: {
      name: 'Vikram Singh',
      email: '<EMAIL>',
      phone: '+91 **********',
      alternatePhone: '+91 **********',
      address: '123 Business District',
      city: 'Delhi',
      state: 'Delhi',
      pincode: '110001'
    },
    businessDetails: {
      businessName: 'Singh Hospitality Ventures',
      businessType: 'Hospitality & Real Estate',
      experienceYears: 8,
      previousHostelExperience: true,
      numberOfPropertiesOwned: 3,
      estimatedInvestment: 5000000,
      targetLocation: 'Delhi NCR',
      expectedLaunchDate: '2024-06-01'
    },
    documents: {
      idProof: {
        type: 'aadhar',
        number: '**************',
        fileUrl: '/documents/vikram_aadhar.pdf',
        verified: false
      },
      addressProof: {
        type: 'utility_bill',
        fileUrl: '/documents/vikram_address.pdf',
        verified: false
      },
      businessProof: {
        type: 'gst',
        number: 'GST789012345',
        fileUrl: '/documents/vikram_gst.pdf',
        verified: false
      },
      financialProof: {
        type: 'bank_statement',
        fileUrl: '/documents/vikram_bank.pdf',
        verified: false
      }
    },
    additionalInfo: {
      motivation: 'I want to expand my hospitality business and provide quality accommodation for students and working professionals.',
      businessPlan: 'Planning to establish a premium hostel facility with modern amenities in the heart of Delhi.',
      references: [
        {
          name: 'Rajesh Kumar',
          relationship: 'Business Partner',
          phone: '+91 **********',
          email: '<EMAIL>'
        }
      ]
    }
  },
  {
    id: 'oar_002',
    requestType: 'owner_access',
    submittedAt: '2024-01-23T14:15:00Z',
    status: 'under_review',
    reviewedBy: '1',
    applicantDetails: {
      name: 'Meera Patel',
      email: '<EMAIL>',
      phone: '+91 **********',
      address: '456 Commercial Street',
      city: 'Mumbai',
      state: 'Maharashtra',
      pincode: '400001'
    },
    businessDetails: {
      businessName: 'Patel Properties',
      businessType: 'Real Estate Development',
      experienceYears: 12,
      previousHostelExperience: false,
      numberOfPropertiesOwned: 7,
      estimatedInvestment: 8000000,
      targetLocation: 'Mumbai Suburbs',
      expectedLaunchDate: '2024-08-15'
    },
    documents: {
      idProof: {
        type: 'pan',
        number: '**********',
        fileUrl: '/documents/meera_pan.pdf',
        verified: true
      },
      businessProof: {
        type: 'incorporation_certificate',
        fileUrl: '/documents/meera_incorporation.pdf',
        verified: true
      },
      financialProof: {
        type: 'financial_statement',
        fileUrl: '/documents/meera_financial.pdf',
        verified: false
      }
    },
    additionalInfo: {
      motivation: 'Looking to diversify into the student accommodation sector with a focus on safety and community building.',
      references: [
        {
          name: 'Amit Shah',
          relationship: 'CA',
          phone: '+91 9876543271',
          email: '<EMAIL>'
        },
        {
          name: 'Priya Sharma',
          relationship: 'Legal Advisor',
          phone: '+91 9876543272'
        }
      ]
    }
  },
  {
    id: 'oar_003',
    requestType: 'owner_access',
    submittedAt: '2024-01-20T09:45:00Z',
    status: 'approved',
    reviewedBy: '1',
    reviewedAt: '2024-01-22T16:30:00Z',
    reviewNotes: 'Excellent credentials and business plan. Approved for owner access.',
    applicantDetails: {
      name: 'Arjun Reddy',
      email: '<EMAIL>',
      phone: '+91 9876543280',
      address: '789 Tech Park Road',
      city: 'Bangalore',
      state: 'Karnataka',
      pincode: '560001'
    },
    businessDetails: {
      businessName: 'Reddy Hospitality Group',
      businessType: 'Hospitality Management',
      experienceYears: 15,
      previousHostelExperience: true,
      numberOfPropertiesOwned: 5,
      estimatedInvestment: 12000000,
      targetLocation: 'Bangalore IT Corridor',
      expectedLaunchDate: '2024-05-01'
    },
    documents: {
      idProof: {
        type: 'aadhar',
        number: '**************',
        fileUrl: '/documents/arjun_aadhar.pdf',
        verified: true
      },
      businessProof: {
        type: 'gst',
        number: 'GST890123456',
        fileUrl: '/documents/arjun_gst.pdf',
        verified: true
      },
      financialProof: {
        type: 'income_tax_return',
        fileUrl: '/documents/arjun_itr.pdf',
        verified: true
      }
    },
    generatedCredentials: {
      userId: 'owner_approved_001',
      temporaryPassword: 'TempPass@123',
      passwordResetRequired: true
    },
    additionalInfo: {
      motivation: 'Expanding successful hostel business to serve the growing tech community in Bangalore.',
      businessPlan: 'Establishing a tech-focused co-living space with modern amenities and networking opportunities.'
    }
  }
];

// Helper functions for hostel owner access requests
export const getHostelOwnerAccessRequestById = (id: string) =>
  mockHostelOwnerAccessRequests.find(request => request.id === id);

export const getPendingOwnerAccessRequests = () =>
  mockHostelOwnerAccessRequests.filter(request => request.status === 'pending');

export const getOwnerAccessRequestsByStatus = (status: 'pending' | 'approved' | 'rejected' | 'under_review') =>
  mockHostelOwnerAccessRequests.filter(request => request.status === status);






// Mock Room and Bed Management Data
export const mockRooms: Room[] = [
  {
    id: 'room_001',
    hostelId: '1',
    floorId: 'floor_001',
    roomNumber: '101',
    roomType: 'quad',
    floor: 1,
    capacity: 4,
    currentOccupancy: 3,
    amenities: ['AC', 'WiFi', 'Attached Bathroom', 'Study Table', 'Wardrobe'],
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
    beds: [
      {
        id: 'bed_001',
        roomId: 'room_001',
        bedNumber: '101A',
        bedType: 'bunk_bottom',
        status: 'occupied',
        occupiedBy: 'member_001',
        size: 'single',
        hasLocker: true,
        lockerNumber: '101A',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-10T08:00:00Z'
      },
      {
        id: 'bed_002',
        roomId: 'room_001',
        bedNumber: '101B',
        bedType: 'bunk_top',
        status: 'occupied',
        occupiedBy: 'member_002',
        size: 'single',
        hasLocker: true,
        lockerNumber: '101B',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-12T14:30:00Z'
      },
      {
        id: 'bed_003',
        roomId: 'room_001',
        bedNumber: '101C',
        bedType: 'bunk_bottom',
        status: 'occupied',
        occupiedBy: 'member_003',
        size: 'single',
        hasLocker: true,
        lockerNumber: '101C',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-08T16:45:00Z'
      },
      {
        id: 'bed_004',
        roomId: 'room_001',
        bedNumber: '101D',
        bedType: 'bunk_top',
        status: 'available',
        size: 'single',
        hasLocker: true,
        lockerNumber: '101D',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }
    ],
    pricing: {
      dailyRate: 350,
      weeklyRate: 2200,
      monthlyRate: 8500,
      securityDeposit: 5000,
      cleaningFee: 200
    },
    details: {
      area: 200,
      hasWindow: true,
      hasBalcony: false,
      hasAttachedBathroom: true,
      bathroomType: 'private',
      hasAC: true,
      hasFan: true,
      hasWiFi: true,
      hasTV: false,
      hasRefrigerator: false,
      hasWardrobe: true,
      hasStudyTable: true,
      hasChair: true,
      description: 'Spacious quad room with modern amenities',
      specialFeatures: ['High-speed WiFi', 'Individual study spaces', 'Ample storage']
    },
    photos: ['/photos/room_101_1.jpg', '/photos/room_101_2.jpg']
  },
  {
    id: 'room_002',
    hostelId: '1',
    floorId: 'floor_001',
    roomNumber: '102',
    roomType: 'double',
    floor: 1,
    capacity: 2,
    currentOccupancy: 1,
    amenities: ['AC', 'WiFi', 'Shared Bathroom', 'Study Table'],
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-20T12:00:00Z',
    beds: [
      {
        id: 'bed_005',
        roomId: 'room_002',
        bedNumber: '102A',
        bedType: 'single',
        status: 'occupied',
        occupiedBy: 'member_004',
        size: 'single',
        hasLocker: true,
        lockerNumber: '102A',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-15T09:30:00Z'
      },
      {
        id: 'bed_006',
        roomId: 'room_002',
        bedNumber: '102B',
        bedType: 'single',
        status: 'available',
        size: 'single',
        hasLocker: true,
        lockerNumber: '102B',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }
    ],
    pricing: {
      dailyRate: 400,
      weeklyRate: 2600,
      monthlyRate: 10000,
      securityDeposit: 6000,
      cleaningFee: 150
    },
    details: {
      area: 150,
      hasWindow: true,
      hasBalcony: false,
      hasAttachedBathroom: false,
      bathroomType: 'shared',
      hasAC: true,
      hasFan: true,
      hasWiFi: true,
      hasTV: false,
      hasRefrigerator: false,
      hasWardrobe: true,
      hasStudyTable: true,
      hasChair: true,
      description: 'Comfortable double room with shared facilities'
    },
    photos: ['/photos/room_102_1.jpg']
  },
  {
    id: 'room_003',
    hostelId: '1',
    floorId: 'floor_002',
    roomNumber: '201',
    roomType: 'triple',
    floor: 2,
    capacity: 3,
    currentOccupancy: 2,
    amenities: ['AC', 'WiFi', 'Attached Bathroom', 'Study Table'],
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-22T11:15:00Z',
    beds: [
      {
        id: 'bed_007',
        roomId: 'room_003',
        bedNumber: '201A',
        bedType: 'single',
        status: 'occupied',
        occupiedBy: 'member_005',
        size: 'single',
        hasLocker: true,
        lockerNumber: '201A',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-18T10:00:00Z'
      },
      {
        id: 'bed_008',
        roomId: 'room_003',
        bedNumber: '201B',
        bedType: 'single',
        status: 'occupied',
        occupiedBy: 'member_006',
        size: 'single',
        hasLocker: true,
        lockerNumber: '201B',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-20T14:30:00Z'
      },
      {
        id: 'bed_009',
        roomId: 'room_003',
        bedNumber: '201C',
        bedType: 'single',
        status: 'available',
        size: 'single',
        hasLocker: true,
        lockerNumber: '201C',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }
    ],
    pricing: {
      dailyRate: 380,
      weeklyRate: 2400,
      monthlyRate: 9200,
      securityDeposit: 5500,
      cleaningFee: 180
    },
    details: {
      area: 180,
      hasWindow: true,
      hasBalcony: false,
      hasAttachedBathroom: true,
      bathroomType: 'private',
      hasAC: true,
      hasFan: true,
      hasWiFi: true,
      hasTV: false,
      hasRefrigerator: false,
      hasWardrobe: true,
      hasStudyTable: true,
      hasChair: true,
      description: 'Spacious triple room on second floor'
    },
    photos: ['/photos/room_201_1.jpg']
  },
  {
    id: 'room_004',
    hostelId: '1',
    floorId: 'floor_003',
    roomNumber: '301',
    roomType: 'single',
    floor: 3,
    capacity: 1,
    currentOccupancy: 1,
    amenities: ['AC', 'WiFi', 'Attached Bathroom', 'Study Table', 'Balcony'],
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-25T09:45:00Z',
    beds: [
      {
        id: 'bed_010',
        roomId: 'room_004',
        bedNumber: '301A',
        bedType: 'single',
        status: 'occupied',
        occupiedBy: 'member_007',
        size: 'single',
        hasLocker: true,
        lockerNumber: '301A',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-23T16:20:00Z'
      }
    ],
    pricing: {
      dailyRate: 450,
      weeklyRate: 2800,
      monthlyRate: 11000,
      securityDeposit: 6500,
      cleaningFee: 200
    },
    details: {
      area: 120,
      hasWindow: true,
      hasBalcony: true,
      hasAttachedBathroom: true,
      bathroomType: 'private',
      hasAC: true,
      hasFan: true,
      hasWiFi: true,
      hasTV: false,
      hasRefrigerator: false,
      hasWardrobe: true,
      hasStudyTable: true,
      hasChair: true,
      description: 'Premium single room with balcony on top floor'
    },
    photos: ['/photos/room_301_1.jpg', '/photos/room_301_balcony.jpg']
  }
];

export const mockRoomModificationRequests: RoomModificationRequest[] = [
  {
    id: 'rmr_001',
    hostelId: '1',
    ownerId: '2',
    requestType: 'add_room',
    status: 'pending',
    submittedAt: '2024-01-25T14:30:00Z',
    requestDetails: {
      roomNumber: '201',
      roomType: 'triple',
      floor: 2,
      capacity: 3,
      amenities: ['AC', 'WiFi', 'Attached Bathroom', 'Study Table'],
      beds: [
        {
          bedNumber: '201A',
          bedType: 'single',
          status: 'available',
          size: 'single',
          hasLocker: true,
          lockerNumber: '201A'
        },
        {
          bedNumber: '201B',
          bedType: 'single',
          status: 'available',
          size: 'single',
          hasLocker: true,
          lockerNumber: '201B'
        },
        {
          bedNumber: '201C',
          bedType: 'single',
          status: 'available',
          size: 'single',
          hasLocker: true,
          lockerNumber: '201C'
        }
      ],
      pricing: {
        dailyRate: 380,
        weeklyRate: 2400,
        monthlyRate: 9200,
        securityDeposit: 5500
      },
      details: {
        area: 180,
        hasWindow: true,
        hasBalcony: true,
        hasAttachedBathroom: true,
        bathroomType: 'private',
        hasAC: true,
        hasFan: true,
        hasWiFi: true,
        hasTV: false,
        hasRefrigerator: false,
        hasWardrobe: true,
        hasStudyTable: true,
        hasChair: true,
        description: 'New triple room with balcony on second floor'
      },
      photos: ['/photos/proposed_room_201.jpg'],
      reason: 'Increasing demand for accommodation, need to expand capacity',
      expectedOccupancyIncrease: 3
    },
    documents: {
      floorPlan: '/documents/floor_plan_second_floor.pdf',
      photos: ['/photos/construction_area.jpg'],
      permits: ['/documents/building_permit.pdf']
    }
  }
];

// Helper functions for room management
export const getRoomsByHostelId = (hostelId: string): Room[] =>
  mockRooms.filter(room => room.hostelId === hostelId);

export const getRoomById = (roomId: string): Room | undefined =>
  mockRooms.find(room => room.id === roomId);

export const getBedsByRoomId = (roomId: string): Bed[] => {
  const room = getRoomById(roomId);
  return room ? room.beds : [];
};

export const getBedById = (bedId: string): Bed | undefined => {
  for (const room of mockRooms) {
    const bed = room.beds.find(b => b.id === bedId);
    if (bed) return bed;
  }
  return undefined;
};

export const getRoomModificationRequestsByHostelId = (hostelId: string): RoomModificationRequest[] =>
  mockRoomModificationRequests.filter(request => request.hostelId === hostelId);

export const getRoomModificationRequestById = (requestId: string): RoomModificationRequest | undefined =>
  mockRoomModificationRequests.find(request => request.id === requestId);

// Helper functions for hostel management
export const getHostelsByOwnerId = (ownerId: string): HostelInfo[] =>
  mockHostels.filter(hostel => hostel.ownerId === ownerId);

export const getHostelById = (hostelId: string): HostelInfo | undefined =>
  mockHostels.find(hostel => hostel.id === hostelId);

// Helper functions for floor management
export const getFloorsByHostelId = (hostelId: string): Floor[] =>
  mockFloors.filter(floor => floor.hostelId === hostelId);

export const getFloorById = (floorId: string): Floor | undefined =>
  mockFloors.find(floor => floor.id === floorId);

export const getFloorsByHostelAndFloorNumber = (hostelId: string, floorNumber: number): Floor | undefined =>
  mockFloors.find(floor => floor.hostelId === hostelId && floor.floorNumber === floorNumber);

// Enhanced room helper functions
export const getRoomsByFloorId = (floorId: string): Room[] =>
  mockRooms.filter(room => room.floorId === floorId);

export const getRoomsByHostelAndFloor = (hostelId: string, floorNumber: number): Room[] =>
  mockRooms.filter(room => room.hostelId === hostelId && room.floor === floorNumber);

// Floor modification requests
export const getFloorModificationRequestsByHostelId = (hostelId: string): FloorModificationRequest[] =>
  mockFloorModificationRequests.filter(request => request.hostelId === hostelId);

export const getFloorModificationRequestById = (requestId: string): FloorModificationRequest | undefined =>
  mockFloorModificationRequests.find(request => request.id === requestId);

// Mock Hostel Data
export const mockHostels: HostelInfo[] = [
  {
    id: '1',
    name: 'Sunrise Hostel',
    address: '123 College Street',
    city: 'Bangalore',
    state: 'Karnataka',
    pincode: '560001',
    ownerId: '2',
    totalFloors: 3,
    totalRooms: 12,
    totalBeds: 36,
    status: 'active',
    createdAt: '2023-01-15T00:00:00Z',
    updatedAt: '2024-01-20T10:30:00Z',
    contactInfo: {
      phone: '+91-**********',
      email: '<EMAIL>',
      website: 'www.sunrisehostel.com'
    },
    facilities: ['WiFi', 'Laundry', 'Kitchen', 'Study Room', 'Parking', 'Security'],
    floors: ['floor_001', 'floor_002', 'floor_003']
  },
  {
    id: '2',
    name: 'Green Valley Hostel',
    address: '456 University Road',
    city: 'Pune',
    state: 'Maharashtra',
    pincode: '411001',
    ownerId: '2',
    totalFloors: 4,
    totalRooms: 20,
    totalBeds: 60,
    status: 'active',
    createdAt: '2023-03-10T00:00:00Z',
    updatedAt: '2024-01-18T14:15:00Z',
    contactInfo: {
      phone: '+91-9876543211',
      email: '<EMAIL>'
    },
    facilities: ['WiFi', 'Gym', 'Cafeteria', 'Library', 'Parking', 'CCTV'],
    floors: ['floor_004', 'floor_005', 'floor_006', 'floor_007']
  },
  {
    id: '3',
    name: 'Tech Hub Hostel',
    address: '789 IT Park',
    city: 'Hyderabad',
    state: 'Telangana',
    pincode: '500001',
    ownerId: '2',
    totalFloors: 2,
    totalRooms: 8,
    totalBeds: 24,
    status: 'active',
    createdAt: '2023-06-01T00:00:00Z',
    updatedAt: '2024-01-22T09:45:00Z',
    contactInfo: {
      phone: '+91-9876543212',
      email: '<EMAIL>'
    },
    facilities: ['High-Speed WiFi', 'Co-working Space', 'Gaming Room', 'Parking'],
    floors: ['floor_008', 'floor_009']
  }
];

// Mock Floor Data
export const mockFloors: Floor[] = [
  // Sunrise Hostel Floors
  {
    id: 'floor_001',
    hostelId: '1',
    floorNumber: 1,
    floorType: 'residential',
    totalRooms: 4,
    totalBeds: 12,
    currentOccupancy: 9,
    status: 'active',
    createdAt: '2023-01-15T00:00:00Z',
    updatedAt: '2024-01-20T10:30:00Z',
    details: {
      area: 1200,
      ceilingHeight: 10,
      hasElevatorAccess: false,
      hasStairAccess: true,
      hasFireExit: true,
      hasCommonArea: true,
      commonAreaSize: 200,
      hasWiFi: true,
      hasCCTV: true,
      hasIntercom: true,
      hasPowerBackup: true,
      hasWaterSupply: true,
      description: 'Ground floor with common areas and reception'
    },
    amenities: ['Reception', 'Common Room', 'WiFi', 'CCTV'],
    rooms: ['room_001', 'room_002', 'room_003', 'room_004']
  },
  {
    id: 'floor_002',
    hostelId: '1',
    floorNumber: 2,
    floorType: 'residential',
    totalRooms: 4,
    totalBeds: 12,
    currentOccupancy: 8,
    status: 'active',
    createdAt: '2023-01-15T00:00:00Z',
    updatedAt: '2024-01-20T10:30:00Z',
    details: {
      area: 1000,
      ceilingHeight: 9,
      hasElevatorAccess: false,
      hasStairAccess: true,
      hasFireExit: true,
      hasCommonArea: true,
      commonAreaSize: 150,
      hasWiFi: true,
      hasCCTV: true,
      hasIntercom: true,
      hasPowerBackup: true,
      hasWaterSupply: true,
      description: 'Second floor residential area'
    },
    amenities: ['Study Area', 'WiFi', 'CCTV'],
    rooms: ['room_005', 'room_006', 'room_007', 'room_008']
  },
  {
    id: 'floor_003',
    hostelId: '1',
    floorNumber: 3,
    floorType: 'residential',
    totalRooms: 4,
    totalBeds: 12,
    currentOccupancy: 6,
    status: 'active',
    createdAt: '2023-01-15T00:00:00Z',
    updatedAt: '2024-01-20T10:30:00Z',
    details: {
      area: 1000,
      ceilingHeight: 9,
      hasElevatorAccess: false,
      hasStairAccess: true,
      hasFireExit: true,
      hasCommonArea: false,
      hasWiFi: true,
      hasCCTV: true,
      hasIntercom: true,
      hasPowerBackup: true,
      hasWaterSupply: true,
      description: 'Top floor residential area'
    },
    amenities: ['WiFi', 'CCTV', 'Terrace Access'],
    rooms: ['room_009', 'room_010', 'room_011', 'room_012']
  }
];

// Mock Floor Modification Requests
export const mockFloorModificationRequests: FloorModificationRequest[] = [
  {
    id: 'fmr_001',
    hostelId: '1',
    ownerId: '2',
    requestType: 'add_floor',
    status: 'pending',
    submittedAt: '2024-01-25T16:00:00Z',
    requestDetails: {
      floorNumber: 4,
      floorType: 'residential',
      plannedRooms: 6,
      plannedBeds: 18,
      amenities: ['WiFi', 'CCTV', 'Study Area'],
      details: {
        area: 1200,
        ceilingHeight: 9,
        hasElevatorAccess: true,
        hasStairAccess: true,
        hasFireExit: true,
        hasCommonArea: true,
        commonAreaSize: 180,
        hasWiFi: true,
        hasCCTV: true,
        hasIntercom: true,
        hasPowerBackup: true,
        hasWaterSupply: true,
        description: 'New fourth floor with elevator access'
      },
      constructionTimeline: '3 months',
      estimatedCost: 500000,
      contractorInfo: 'ABC Construction Ltd.',
      reason: 'High demand for accommodation, need to expand capacity',
      expectedOccupancyIncrease: 18,
      businessJustification: 'Current occupancy rate is 85%, adding floor will increase revenue by 50%'
    },
    documents: {
      floorPlan: '/documents/floor_4_plan.pdf',
      structuralDrawings: ['/documents/structural_drawing_1.pdf'],
      permits: ['/documents/construction_permit.pdf'],
      photos: ['/photos/construction_site.jpg']
    }
  }
];

// Statistics helper functions
export const getFloorStatistics = (floorId: string): FloorStatistics => {
  const floor = getFloorById(floorId);
  if (!floor) {
    throw new Error(`Floor with ID ${floorId} not found`);
  }

  const rooms = getRoomsByFloorId(floorId);
  const totalRooms = rooms.length;
  const totalBeds = rooms.reduce((sum, room) => sum + room.beds.length, 0);
  const occupiedBeds = rooms.reduce((sum, room) =>
    sum + room.beds.filter(bed => bed.status === 'occupied').length, 0);
  const availableBeds = rooms.reduce((sum, room) =>
    sum + room.beds.filter(bed => bed.status === 'available').length, 0);
  const maintenanceBeds = rooms.reduce((sum, room) =>
    sum + room.beds.filter(bed => bed.status === 'maintenance').length, 0);
  const reservedBeds = rooms.reduce((sum, room) =>
    sum + room.beds.filter(bed => bed.status === 'reserved').length, 0);

  const occupancyRate = totalBeds > 0 ? (occupiedBeds / totalBeds) * 100 : 0;

  // Calculate revenue for this floor
  const dailyRevenue = rooms.reduce((sum, room) => {
    const occupiedBedsInRoom = room.beds.filter(bed => bed.status === 'occupied').length;
    return sum + (occupiedBedsInRoom * room.pricing.dailyRate / room.capacity);
  }, 0);

  const monthlyRevenue = dailyRevenue * 30;
  const averageRatePerBed = rooms.length > 0 ?
    rooms.reduce((sum, room) => sum + room.pricing.dailyRate, 0) / rooms.length : 0;

  return {
    floorId,
    floorNumber: floor.floorNumber,
    totalRooms,
    totalBeds,
    occupiedBeds,
    availableBeds,
    maintenanceBeds,
    reservedBeds,
    occupancyRate,
    revenueStats: {
      dailyRevenue,
      monthlyRevenue,
      averageRatePerBed
    },
    roomTypeBreakdown: {}
  };
};

export const getHostelStatistics = (hostelId: string): HostelStatistics => {
  const hostel = getHostelById(hostelId);
  if (!hostel) {
    throw new Error(`Hostel with ID ${hostelId} not found`);
  }

  const floors = getFloorsByHostelId(hostelId);
  const floorStatistics = floors.map(floor => getFloorStatistics(floor.id));

  const totalFloors = floors.length;
  const totalRooms = floorStatistics.reduce((sum, stat) => sum + stat.totalRooms, 0);
  const totalBeds = floorStatistics.reduce((sum, stat) => sum + stat.totalBeds, 0);
  const occupiedBeds = floorStatistics.reduce((sum, stat) => sum + stat.occupiedBeds, 0);
  const availableBeds = floorStatistics.reduce((sum, stat) => sum + stat.availableBeds, 0);
  const maintenanceBeds = floorStatistics.reduce((sum, stat) => sum + stat.maintenanceBeds, 0);
  const reservedBeds = floorStatistics.reduce((sum, stat) => sum + stat.reservedBeds, 0);

  const occupancyRate = totalBeds > 0 ? (occupiedBeds / totalBeds) * 100 : 0;

  const dailyRevenue = floorStatistics.reduce((sum, stat) => sum + stat.revenueStats.dailyRevenue, 0);
  const monthlyRevenue = dailyRevenue * 30;
  const averageRatePerBed = floorStatistics.length > 0 ?
    floorStatistics.reduce((sum, stat) => sum + stat.revenueStats.averageRatePerBed, 0) / floorStatistics.length : 0;

  return {
    hostelId,
    totalFloors,
    totalRooms,
    totalBeds,
    occupiedBeds,
    availableBeds,
    maintenanceBeds,
    reservedBeds,
    occupancyRate,
    floorStatistics,
    revenueStats: {
      dailyRevenue,
      monthlyRevenue,
      averageRatePerBed,
      totalSecurityDeposits: occupiedBeds * 5500
    },
    roomTypeBreakdown: {}
  };
};

// Simple room statistics function for quick loading
export const getRoomStatistics = (hostelId: string): RoomStatistics => {
  const rooms = getRoomsByHostelId(hostelId);
  const totalRooms = rooms.length;
  const totalBeds = rooms.reduce((sum, room) => sum + room.beds.length, 0);
  const occupiedBeds = rooms.reduce((sum, room) =>
    sum + room.beds.filter(bed => bed.status === 'occupied').length, 0);
  const availableBeds = rooms.reduce((sum, room) =>
    sum + room.beds.filter(bed => bed.status === 'available').length, 0);
  const maintenanceBeds = rooms.reduce((sum, room) =>
    sum + room.beds.filter(bed => bed.status === 'maintenance').length, 0);
  const reservedBeds = rooms.reduce((sum, room) =>
    sum + room.beds.filter(bed => bed.status === 'reserved').length, 0);

  const occupancyRate = totalBeds > 0 ? (occupiedBeds / totalBeds) * 100 : 0;

  // Calculate revenue (simplified)
  const dailyRevenue = occupiedBeds * 350; // Average rate
  const monthlyRevenue = dailyRevenue * 30;
  const averageRatePerBed = rooms.length > 0 ?
    rooms.reduce((sum, room) => sum + room.pricing.dailyRate, 0) / rooms.length : 0;

  return {
    totalRooms,
    totalBeds,
    occupiedBeds,
    availableBeds,
    maintenanceBeds,
    reservedBeds,
    occupancyRate,
    roomTypeBreakdown: {
      quad: { count: 1, totalBeds: 4, occupiedBeds: 3 },
      double: { count: 1, totalBeds: 2, occupiedBeds: 1 }
    },
    revenueStats: {
      dailyRevenue,
      monthlyRevenue,
      averageRatePerBed,
      totalSecurityDeposits: occupiedBeds * 5500
    }
  };
};

