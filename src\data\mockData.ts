// Mock data for the hostel management system

import { HostelRegistrationRequest, AccountCreationRequest, HostelOwnerAccessRequest } from '@/types/admin';

export interface Hostel {
  id: string;
  name: string;
  address: string;
  city: string;
  state: string;
  pincode: string;
  areaId: string;
  totalBeds: number;
  availableBeds: number;
  pricePerBed: number;
  amenities: string[];
  rating: number;
  images: string[];
  ownerId: string;
  employees: string[];
  status: 'active' | 'inactive';
  bedTypes: {
    standard: {
      count: number;
      available: number;
      monthlyRate: number;
      dailyRate: number;
    };
    premium: {
      count: number;
      available: number;
      monthlyRate: number;
      dailyRate: number;
    };
  };
  foodPackages: {
    withMeals: {
      dailyRate: number;
      description: string;
    };
    withoutMeals: {
      dailyRate: number;
      description: string;
    };
  };
  securityDeposit: number;
  minimumStayHours: number;
  hourlyRate?: number;
}

export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  role: 'superadmin' | 'owner' | 'employee' | 'member';
  hostelId?: string;
  avatar?: string;
  joinedDate: string;
  status: 'active' | 'inactive';
  membershipType?: 'regular' | 'guest';
  registrationStatus?: 'pending' | 'approved' | 'rejected';
  idProof?: {
    type: 'aadhar' | 'pan' | 'passport' | 'driving_license';
    number: string;
    verified: boolean;
  };
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
  emailVerified?: boolean;
}

export interface Booking {
  id: string;
  userId: string;
  hostelId: string;
  bedNumber: string;
  checkIn: string;
  checkOut: string;
  amount: number;
  status: 'confirmed' | 'pending' | 'cancelled' | 'checked_in' | 'checked_out';
  paymentStatus: 'paid' | 'pending' | 'failed';
  bookingType: 'monthly' | 'daily';
  actualCheckIn?: string; // ISO timestamp
  actualCheckOut?: string; // ISO timestamp
  securityDeposit?: number;
  bedType: 'standard' | 'premium';
  foodPackage: 'with_meals' | 'without_meals';
  dailyFoodRate?: number;
  billingItems?: BillingItem[];
  foodConsumption?: FoodConsumption[];
}

export interface Payment {
  id: string;
  bookingId: string;
  userId: string;
  amount: number;
  date: string;
  method: 'card' | 'upi' | 'bank_transfer';
  status: 'success' | 'pending' | 'failed';
  splitDetails: {
    hostelOwner: number;
    platform: number;
  };
}

export interface Complaint {
  id: string;
  userId: string;
  hostelId: string;
  title: string;
  description: string;
  category: 'maintenance' | 'cleanliness' | 'noise' | 'service' | 'other';
  status: 'open' | 'in_progress' | 'resolved';
  priority: 'low' | 'medium' | 'high';
  createdDate: string;
  resolvedDate?: string;
}

export interface BillingItem {
  id: string;
  bookingId: string;
  type: 'bed_charge' | 'food_charge' | 'additional_service' | 'security_deposit' | 'refund';
  description: string;
  amount: number;
  quantity: number;
  date: string;
  category?: string;
}

export interface FoodConsumption {
  id: string;
  bookingId: string;
  date: string;
  mealType: 'breakfast' | 'lunch' | 'dinner';
  consumed: boolean;
  cost: number;
  notes?: string;
}

export interface Area {
  id: string;
  name: string;
  cityId: string;
}

export interface City {
  id: string;
  name: string;
  state: string;
  areas: Area[];
}

// Mock Data
export const mockCities: City[] = [
  {
    id: 'mumbai',
    name: 'Mumbai',
    state: 'Maharashtra',
    areas: [
      { id: 'andheri', name: 'Andheri', cityId: 'mumbai' },
      { id: 'bandra', name: 'Bandra', cityId: 'mumbai' },
      { id: 'powai', name: 'Powai', cityId: 'mumbai' },
      { id: 'malad', name: 'Malad', cityId: 'mumbai' }
    ]
  },
  {
    id: 'pune',
    name: 'Pune',
    state: 'Maharashtra',
    areas: [
      { id: 'koregaon-park', name: 'Koregaon Park', cityId: 'pune' },
      { id: 'hinjewadi', name: 'Hinjewadi', cityId: 'pune' },
      { id: 'kothrud', name: 'Kothrud', cityId: 'pune' },
      { id: 'viman-nagar', name: 'Viman Nagar', cityId: 'pune' }
    ]
  },
  {
    id: 'bangalore',
    name: 'Bangalore',
    state: 'Karnataka',
    areas: [
      { id: 'koramangala', name: 'Koramangala', cityId: 'bangalore' },
      { id: 'whitefield', name: 'Whitefield', cityId: 'bangalore' },
      { id: 'indiranagar', name: 'Indiranagar', cityId: 'bangalore' },
      { id: 'electronic-city', name: 'Electronic City', cityId: 'bangalore' }
    ]
  }
];

export const mockHostels: Hostel[] = [
  {
    id: '1',
    name: 'Urban Stay Hostel',
    address: '123 College Road',
    city: 'Mumbai',
    state: 'Maharashtra',
    pincode: '400001',
    areaId: 'andheri',
    totalBeds: 100,
    availableBeds: 25,
    pricePerBed: 8000,
    amenities: ['WiFi', 'AC', 'Laundry', 'Mess', 'Security'],
    rating: 4.5,
    images: ['/placeholder.svg'],
    ownerId: '2',
    employees: ['3', '4'],
    status: 'active',
    bedTypes: {
      standard: {
        count: 60,
        available: 15,
        monthlyRate: 7000,
        dailyRate: 300
      },
      premium: {
        count: 40,
        available: 10,
        monthlyRate: 9000,
        dailyRate: 400
      }
    },
    foodPackages: {
      withMeals: {
        dailyRate: 200,
        description: 'Includes breakfast, lunch, and dinner'
      },
      withoutMeals: {
        dailyRate: 0,
        description: 'No meals included'
      }
    },
    securityDeposit: 5000,
    minimumStayHours: 4,
    hourlyRate: 50
  },
  {
    id: '2',
    name: 'Campus Lodge',
    address: '456 University Lane',
    city: 'Pune',
    state: 'Maharashtra',
    pincode: '411001',
    areaId: 'koregaon-park',
    totalBeds: 80,
    availableBeds: 15,
    pricePerBed: 7500,
    amenities: ['WiFi', 'Gym', 'Mess', 'Study Room', 'Parking'],
    rating: 4.2,
    images: ['/placeholder.svg'],
    ownerId: '5',
    employees: ['6'],
    status: 'active',
    bedTypes: {
      standard: {
        count: 50,
        available: 10,
        monthlyRate: 6500,
        dailyRate: 280
      },
      premium: {
        count: 30,
        available: 5,
        monthlyRate: 8500,
        dailyRate: 380
      }
    },
    foodPackages: {
      withMeals: {
        dailyRate: 180,
        description: 'Includes breakfast, lunch, and dinner'
      },
      withoutMeals: {
        dailyRate: 0,
        description: 'No meals included'
      }
    },
    securityDeposit: 4000,
    minimumStayHours: 4,
    hourlyRate: 45
  },
  {
    id: '3',
    name: 'Elite Residency',
    address: '789 Tech Park',
    city: 'Bangalore',
    state: 'Karnataka',
    pincode: '560001',
    areaId: 'koramangala',
    totalBeds: 150,
    availableBeds: 45,
    pricePerBed: 12000,
    amenities: ['WiFi', 'AC', 'Gym', 'Swimming Pool', 'Cafeteria', 'Security'],
    rating: 4.8,
    images: ['/placeholder.svg'],
    ownerId: '7',
    employees: ['8', '9', '10'],
    status: 'active',
    bedTypes: {
      standard: {
        count: 80,
        available: 25,
        monthlyRate: 10000,
        dailyRate: 450
      },
      premium: {
        count: 70,
        available: 20,
        monthlyRate: 14000,
        dailyRate: 600
      }
    },
    foodPackages: {
      withMeals: {
        dailyRate: 250,
        description: 'Premium meals with breakfast, lunch, and dinner'
      },
      withoutMeals: {
        dailyRate: 0,
        description: 'No meals included'
      }
    },
    securityDeposit: 8000,
    minimumStayHours: 4,
    hourlyRate: 75
  }
];

export const mockUsers: User[] = [
  {
    id: '1',
    name: 'Admin User',
    email: '<EMAIL>',
    phone: '+91 **********',
    role: 'superadmin',
    joinedDate: '2023-01-01',
    status: 'active'
  },
  {
    id: '2',
    name: 'Rajesh Kumar',
    email: '<EMAIL>',
    phone: '+91 9876543211',
    role: 'owner',
    hostelId: '1',
    joinedDate: '2023-02-15',
    status: 'active'
  },
  {
    id: '3',
    name: 'Priya Sharma',
    email: '<EMAIL>',
    phone: '+91 9876543212',
    role: 'employee',
    hostelId: '1',
    joinedDate: '2023-03-10',
    status: 'active'
  },
  {
    id: '4',
    name: 'Amit Singh',
    email: '<EMAIL>',
    phone: '+91 9876543213',
    role: 'employee',
    hostelId: '1',
    joinedDate: '2023-03-15',
    status: 'active'
  },
  {
    id: '11',
    name: 'Rohit Verma',
    email: '<EMAIL>',
    phone: '+91 9876543220',
    role: 'member',
    joinedDate: '2023-06-01',
    status: 'active'
  },
  {
    id: '12',
    name: 'Sneha Patel',
    email: '<EMAIL>',
    phone: '+91 9876543221',
    role: 'member',
    joinedDate: '2023-06-15',
    status: 'active'
  }
];

export const mockBookings: Booking[] = [
  {
    id: '1',
    userId: '11',
    hostelId: '1',
    bedNumber: 'A101',
    checkIn: '2024-01-01',
    checkOut: '2024-06-30',
    amount: 48000,
    status: 'confirmed',
    paymentStatus: 'paid',
    bookingType: 'monthly',
    bedType: 'standard',
    foodPackage: 'with_meals',
    securityDeposit: 5000,
    dailyFoodRate: 200,
    actualCheckIn: '2024-01-01T10:30:00Z',
    billingItems: [
      {
        id: 'bill_1_1',
        bookingId: '1',
        type: 'bed_charge',
        description: 'Monthly bed charge (6 months)',
        amount: 42000,
        quantity: 6,
        date: '2024-01-01',
        category: 'accommodation'
      },
      {
        id: 'bill_1_2',
        bookingId: '1',
        type: 'food_charge',
        description: 'Food charges (6 months)',
        amount: 36000,
        quantity: 180,
        date: '2024-01-01',
        category: 'food'
      }
    ]
  },
  {
    id: '2',
    userId: '12',
    hostelId: '3',
    bedNumber: 'B205',
    checkIn: '2024-02-01',
    checkOut: '2024-07-31',
    amount: 72000,
    status: 'confirmed',
    paymentStatus: 'paid',
    bookingType: 'monthly',
    bedType: 'premium',
    foodPackage: 'with_meals',
    securityDeposit: 8000,
    dailyFoodRate: 250,
    actualCheckIn: '2024-02-01T14:15:00Z'
  }
];

export const mockPayments: Payment[] = [
  {
    id: '1',
    bookingId: '1',
    userId: '11',
    amount: 48000,
    date: '2023-12-25',
    method: 'upi',
    status: 'success',
    splitDetails: {
      hostelOwner: 45600,
      platform: 2400
    }
  },
  {
    id: '2',
    bookingId: '2',
    userId: '12',
    amount: 72000,
    date: '2024-01-25',
    method: 'card',
    status: 'success',
    splitDetails: {
      hostelOwner: 68400,
      platform: 3600
    }
  }
];

export const mockComplaints: Complaint[] = [
  {
    id: '1',
    userId: '11',
    hostelId: '1',
    title: 'AC not working in room A101',
    description: 'The air conditioning unit has stopped working since yesterday. The room is getting very hot.',
    category: 'maintenance',
    status: 'in_progress',
    priority: 'high',
    createdDate: '2024-01-15'
  },
  {
    id: '2',
    userId: '12',
    hostelId: '3',
    title: 'Noisy neighbors',
    description: 'There is excessive noise from the room next door during late night hours.',
    category: 'noise',
    status: 'open',
    priority: 'medium',
    createdDate: '2024-01-20'
  }
];

// Helper functions
export const getUserById = (id: string) => mockUsers.find(user => user.id === id);
export const getHostelById = (id: string) => mockHostels.find(hostel => hostel.id === id);
export const getBookingsByUserId = (userId: string) => mockBookings.filter(booking => booking.userId === userId);
export const getComplaintsByHostelId = (hostelId: string) => mockComplaints.filter(complaint => complaint.hostelId === hostelId);
export const getCityById = (id: string) => mockCities.find(city => city.id === id);
export const getAreaById = (cityId: string, areaId: string) => {
  const city = getCityById(cityId);
  return city?.areas.find(area => area.id === areaId);
};
export const getHostelsByArea = (areaId: string) => mockHostels.filter(hostel => hostel.areaId === areaId);
export const getAvailableBeds = (hostelId: string, bedType: 'standard' | 'premium') => {
  const hostel = getHostelById(hostelId);
  return hostel?.bedTypes[bedType]?.available || 0;
};

// Mock Registration Requests Data
export const mockHostelRegistrationRequests: HostelRegistrationRequest[] = [
  {
    id: 'hr_001',
    requestType: 'hostel_registration',
    submittedBy: 'pending_owner_001',
    submittedAt: '2024-01-15T10:30:00Z',
    status: 'pending',
    hostelDetails: {
      name: 'Green Valley Hostel',
      address: '456 University Road',
      city: 'Pune',
      state: 'Maharashtra',
      pincode: '411001',
      areaId: 'kothrud',
      totalBeds: 80,
      amenities: ['WiFi', 'AC', 'Laundry', 'Mess', 'Security', 'Parking'],
      pricePerBed: 7500,
      bedTypes: {
        standard: {
          count: 50,
          monthlyRate: 6500,
          dailyRate: 280
        },
        premium: {
          count: 30,
          monthlyRate: 8500,
          dailyRate: 380
        }
      },
      foodPackages: {
        withMeals: {
          dailyRate: 180,
          description: 'Includes breakfast, lunch, and dinner'
        },
        withoutMeals: {
          dailyRate: 0,
          description: 'No meals included'
        }
      },
      securityDeposit: 7000,
      minimumStayHours: 6,
      hourlyRate: 60
    },
    ownerDetails: {
      name: 'Suresh Patil',
      email: '<EMAIL>',
      phone: '+91 9876543220',
      alternatePhone: '+91 9876543221',
      businessExperience: 5,
      previousHostelExperience: true
    },
    documents: {
      ownerIdProof: {
        type: 'aadhar',
        number: '1234-5678-9012',
        fileUrl: '/documents/aadhar_suresh.pdf',
        verified: false
      },
      businessRegistration: {
        type: 'gst',
        number: 'GST123456789',
        fileUrl: '/documents/gst_certificate.pdf',
        verified: false
      },
      propertyDocuments: {
        type: 'ownership',
        fileUrl: '/documents/property_deed.pdf',
        verified: false
      },
      bankDetails: {
        accountNumber: '**********',
        ifscCode: 'HDFC0001234',
        bankName: 'HDFC Bank',
        branchName: 'Kothrud Branch',
        accountHolderName: 'Suresh Patil',
        accountType: 'current'
      }
    },
    photos: {
      exterior: ['/photos/green_valley_exterior_1.jpg', '/photos/green_valley_exterior_2.jpg'],
      rooms: ['/photos/green_valley_room_1.jpg', '/photos/green_valley_room_2.jpg'],
      commonAreas: ['/photos/green_valley_common_1.jpg'],
      amenities: ['/photos/green_valley_amenities_1.jpg'],
      kitchen: ['/photos/green_valley_kitchen_1.jpg']
    }
  },
  {
    id: 'hr_002',
    requestType: 'hostel_registration',
    submittedBy: 'pending_owner_002',
    submittedAt: '2024-01-18T14:45:00Z',
    status: 'under_review',
    reviewedBy: '1',
    hostelDetails: {
      name: 'Tech Hub Residency',
      address: '789 IT Park Road',
      city: 'Bangalore',
      state: 'Karnataka',
      pincode: '560001',
      areaId: 'koramangala',
      totalBeds: 120,
      amenities: ['WiFi', 'AC', 'Laundry', 'Gym', 'Security', 'Parking', 'Conference Room'],
      pricePerBed: 9000,
      bedTypes: {
        standard: {
          count: 70,
          monthlyRate: 8000,
          dailyRate: 350
        },
        premium: {
          count: 50,
          monthlyRate: 10000,
          dailyRate: 450
        }
      },
      foodPackages: {
        withMeals: {
          dailyRate: 220,
          description: 'Includes breakfast, lunch, and dinner'
        },
        withoutMeals: {
          dailyRate: 0,
          description: 'No meals included'
        }
      },
      securityDeposit: 9000,
      minimumStayHours: 8,
      hourlyRate: 80
    },
    ownerDetails: {
      name: 'Priya Sharma',
      email: '<EMAIL>',
      phone: '+91 9876543230',
      businessExperience: 8,
      previousHostelExperience: false
    },
    documents: {
      ownerIdProof: {
        type: 'pan',
        number: '**********',
        fileUrl: '/documents/pan_priya.pdf',
        verified: true
      },
      businessRegistration: {
        type: 'gst',
        number: 'GST987654321',
        fileUrl: '/documents/gst_techhub.pdf',
        verified: true
      },
      propertyDocuments: {
        type: 'lease_deed',
        fileUrl: '/documents/lease_techhub.pdf',
        verified: false
      },
      bankDetails: {
        accountNumber: '**********',
        ifscCode: 'ICICI0001234',
        bankName: 'ICICI Bank',
        branchName: 'Koramangala Branch',
        accountHolderName: 'Priya Sharma',
        accountType: 'current'
      }
    },
    photos: {
      exterior: ['/photos/techhub_exterior_1.jpg'],
      rooms: ['/photos/techhub_room_1.jpg', '/photos/techhub_room_2.jpg', '/photos/techhub_room_3.jpg'],
      commonAreas: ['/photos/techhub_common_1.jpg', '/photos/techhub_common_2.jpg'],
      amenities: ['/photos/techhub_gym_1.jpg', '/photos/techhub_conference_1.jpg']
    }
  }
];

export const mockAccountCreationRequests: AccountCreationRequest[] = [
  {
    id: 'ac_001',
    requestType: 'account_creation',
    submittedAt: '2024-01-20T09:15:00Z',
    status: 'pending',
    requestedUserDetails: {
      name: 'Rajesh Kumar',
      email: '<EMAIL>',
      phone: '+91 **********',
      role: 'owner',
      businessType: 'Hospitality',
      experienceYears: 3,
      referenceContact: '+91 **********'
    },
    documents: {
      idProof: {
        type: 'aadhar',
        number: '**************',
        fileUrl: '/documents/aadhar_rajesh.pdf'
      },
      businessProof: {
        type: 'shop_act',
        number: 'SA123456789',
        fileUrl: '/documents/shop_act_rajesh.pdf'
      }
    }
  },
  {
    id: 'ac_002',
    requestType: 'account_creation',
    submittedAt: '2024-01-22T16:30:00Z',
    status: 'pending',
    requestedUserDetails: {
      name: 'Anita Desai',
      email: '<EMAIL>',
      phone: '+91 **********',
      role: 'owner',
      businessType: 'Real Estate',
      experienceYears: 7,
      referenceContact: '+91 **********'
    },
    documents: {
      idProof: {
        type: 'pan',
        number: '**********',
        fileUrl: '/documents/pan_anita.pdf'
      },
      businessProof: {
        type: 'gst',
        number: 'GST555666777',
        fileUrl: '/documents/gst_anita.pdf'
      }
    }
  }
];

// Helper functions for registration requests
export const getHostelRegistrationRequestById = (id: string) =>
  mockHostelRegistrationRequests.find(request => request.id === id);

export const getAccountCreationRequestById = (id: string) =>
  mockAccountCreationRequests.find(request => request.id === id);

export const getPendingHostelRegistrations = () =>
  mockHostelRegistrationRequests.filter(request => request.status === 'pending');

export const getPendingAccountCreations = () =>
  mockAccountCreationRequests.filter(request => request.status === 'pending');

export const getAllPendingRequests = () => [
  ...mockHostelRegistrationRequests.filter(request => request.status === 'pending'),
  ...mockAccountCreationRequests.filter(request => request.status === 'pending')
];

export const getRegistrationRequestsByStatus = (status: 'pending' | 'approved' | 'rejected' | 'under_review') => [
  ...mockHostelRegistrationRequests.filter(request => request.status === status),
  ...mockAccountCreationRequests.filter(request => request.status === status)
];

// Mock Hostel Owner Access Requests Data
export const mockHostelOwnerAccessRequests: HostelOwnerAccessRequest[] = [
  {
    id: 'oar_001',
    requestType: 'owner_access',
    submittedAt: '2024-01-25T11:30:00Z',
    status: 'pending',
    applicantDetails: {
      name: 'Vikram Singh',
      email: '<EMAIL>',
      phone: '+91 **********',
      alternatePhone: '+91 **********',
      address: '123 Business District',
      city: 'Delhi',
      state: 'Delhi',
      pincode: '110001'
    },
    businessDetails: {
      businessName: 'Singh Hospitality Ventures',
      businessType: 'Hospitality & Real Estate',
      experienceYears: 8,
      previousHostelExperience: true,
      numberOfPropertiesOwned: 3,
      estimatedInvestment: 5000000,
      targetLocation: 'Delhi NCR',
      expectedLaunchDate: '2024-06-01'
    },
    documents: {
      idProof: {
        type: 'aadhar',
        number: '**************',
        fileUrl: '/documents/vikram_aadhar.pdf',
        verified: false
      },
      addressProof: {
        type: 'utility_bill',
        fileUrl: '/documents/vikram_address.pdf',
        verified: false
      },
      businessProof: {
        type: 'gst',
        number: 'GST789012345',
        fileUrl: '/documents/vikram_gst.pdf',
        verified: false
      },
      financialProof: {
        type: 'bank_statement',
        fileUrl: '/documents/vikram_bank.pdf',
        verified: false
      }
    },
    additionalInfo: {
      motivation: 'I want to expand my hospitality business and provide quality accommodation for students and working professionals.',
      businessPlan: 'Planning to establish a premium hostel facility with modern amenities in the heart of Delhi.',
      references: [
        {
          name: 'Rajesh Kumar',
          relationship: 'Business Partner',
          phone: '+91 **********',
          email: '<EMAIL>'
        }
      ]
    }
  },
  {
    id: 'oar_002',
    requestType: 'owner_access',
    submittedAt: '2024-01-23T14:15:00Z',
    status: 'under_review',
    reviewedBy: '1',
    applicantDetails: {
      name: 'Meera Patel',
      email: '<EMAIL>',
      phone: '+91 **********',
      address: '456 Commercial Street',
      city: 'Mumbai',
      state: 'Maharashtra',
      pincode: '400001'
    },
    businessDetails: {
      businessName: 'Patel Properties',
      businessType: 'Real Estate Development',
      experienceYears: 12,
      previousHostelExperience: false,
      numberOfPropertiesOwned: 7,
      estimatedInvestment: 8000000,
      targetLocation: 'Mumbai Suburbs',
      expectedLaunchDate: '2024-08-15'
    },
    documents: {
      idProof: {
        type: 'pan',
        number: '**********',
        fileUrl: '/documents/meera_pan.pdf',
        verified: true
      },
      businessProof: {
        type: 'incorporation_certificate',
        fileUrl: '/documents/meera_incorporation.pdf',
        verified: true
      },
      financialProof: {
        type: 'financial_statement',
        fileUrl: '/documents/meera_financial.pdf',
        verified: false
      }
    },
    additionalInfo: {
      motivation: 'Looking to diversify into the student accommodation sector with a focus on safety and community building.',
      references: [
        {
          name: 'Amit Shah',
          relationship: 'CA',
          phone: '+91 9876543271',
          email: '<EMAIL>'
        },
        {
          name: 'Priya Sharma',
          relationship: 'Legal Advisor',
          phone: '+91 9876543272'
        }
      ]
    }
  },
  {
    id: 'oar_003',
    requestType: 'owner_access',
    submittedAt: '2024-01-20T09:45:00Z',
    status: 'approved',
    reviewedBy: '1',
    reviewedAt: '2024-01-22T16:30:00Z',
    reviewNotes: 'Excellent credentials and business plan. Approved for owner access.',
    applicantDetails: {
      name: 'Arjun Reddy',
      email: '<EMAIL>',
      phone: '+91 9876543280',
      address: '789 Tech Park Road',
      city: 'Bangalore',
      state: 'Karnataka',
      pincode: '560001'
    },
    businessDetails: {
      businessName: 'Reddy Hospitality Group',
      businessType: 'Hospitality Management',
      experienceYears: 15,
      previousHostelExperience: true,
      numberOfPropertiesOwned: 5,
      estimatedInvestment: 12000000,
      targetLocation: 'Bangalore IT Corridor',
      expectedLaunchDate: '2024-05-01'
    },
    documents: {
      idProof: {
        type: 'aadhar',
        number: '**************',
        fileUrl: '/documents/arjun_aadhar.pdf',
        verified: true
      },
      businessProof: {
        type: 'gst',
        number: 'GST890123456',
        fileUrl: '/documents/arjun_gst.pdf',
        verified: true
      },
      financialProof: {
        type: 'income_tax_return',
        fileUrl: '/documents/arjun_itr.pdf',
        verified: true
      }
    },
    generatedCredentials: {
      userId: 'owner_approved_001',
      temporaryPassword: 'TempPass@123',
      passwordResetRequired: true
    },
    additionalInfo: {
      motivation: 'Expanding successful hostel business to serve the growing tech community in Bangalore.',
      businessPlan: 'Establishing a tech-focused co-living space with modern amenities and networking opportunities.'
    }
  }
];

// Helper functions for hostel owner access requests
export const getHostelOwnerAccessRequestById = (id: string) =>
  mockHostelOwnerAccessRequests.find(request => request.id === id);

export const getPendingOwnerAccessRequests = () =>
  mockHostelOwnerAccessRequests.filter(request => request.status === 'pending');

export const getOwnerAccessRequestsByStatus = (status: 'pending' | 'approved' | 'rejected' | 'under_review') =>
  mockHostelOwnerAccessRequests.filter(request => request.status === status);