import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  CreditCard, 
  DollarSign,
  Calendar,
  Clock,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  TrendingDown,
  ArrowUpRight,
  ArrowDownRight,
  Plus,
  Download,
  Receipt,
  Bell,
  Wallet,
  Building2,
  Users,
  Target,
  History,
  Eye,
  RefreshCw
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { mockPayments, mockBookings, getHostelById } from '@/data/mockData';
import { EnhancedPayment } from '@/types/payment';

// Mock enhanced payment data
const mockEnhancedPayments: EnhancedPayment[] = [
  {
    ...mockPayments[0],
    gateway: 'phonepe',
    gatewayTransactionId: 'TXN_PH_123456789',
    paymentMethod: 'upi',
    receiptUrl: '/receipts/PAY_001.pdf',
    remindersSent: 0,
    dueDate: '2024-02-15',
    discountApplied: 500
  },
  {
    ...mockPayments[1],
    gateway: 'cashfree',
    gatewayTransactionId: 'TXN_CF_987654321',
    paymentMethod: 'card',
    receiptUrl: '/receipts/PAY_002.pdf',
    remindersSent: 1,
    dueDate: '2024-03-15',
    lateFee: 200
  }
];

// Mock pending payments
const mockPendingPayments = [
  {
    id: 'PENDING_001',
    bookingId: '1',
    amount: 30000,
    dueDate: '2024-02-15',
    description: 'Monthly hostel fee - February 2024',
    type: 'monthly_fee',
    isOverdue: false,
    daysUntilDue: 5
  },
  {
    id: 'PENDING_002',
    bookingId: '1',
    amount: 1500,
    dueDate: '2024-01-30',
    description: 'Late fee for January payment',
    type: 'late_fee',
    isOverdue: true,
    daysOverdue: 10
  }
];

export const PaymentDashboard: React.FC = () => {
  const { user } = useAuth();
  const [refreshing, setRefreshing] = useState(false);

  // Get user's data
  const currentUserId = user?.id || '11'; // Fallback for demo
  const userBookings = mockBookings.filter(b => b.userId === currentUserId);
  const userPayments = mockEnhancedPayments.filter(p => userBookings.some(b => b.id === p.bookingId));
  const pendingPayments = mockPendingPayments;

  // Calculate statistics
  const totalPaid = userPayments.reduce((sum, p) => sum + p.amount, 0);
  const totalPending = pendingPayments.reduce((sum, p) => sum + p.amount, 0);
  const overduePayments = pendingPayments.filter(p => p.isOverdue);
  const totalOverdue = overduePayments.reduce((sum, p) => sum + p.amount, 0);
  const recentPayments = userPayments.slice(0, 5);

  // Payment statistics for cards
  const paymentStats = [
    {
      title: 'Total Paid',
      value: `₹${totalPaid.toLocaleString()}`,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      trend: 'up',
      change: '+12%',
      description: 'This month'
    },
    {
      title: 'Pending Dues',
      value: `₹${totalPending.toLocaleString()}`,
      icon: Clock,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      trend: 'down',
      change: '-5%',
      description: 'Due soon'
    },
    {
      title: 'Overdue Amount',
      value: `₹${totalOverdue.toLocaleString()}`,
      icon: AlertTriangle,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      trend: 'up',
      change: '+2%',
      description: 'Immediate action required'
    },
    {
      title: 'This Month',
      value: `₹${(totalPaid * 0.3).toLocaleString()}`,
      icon: TrendingUp,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      trend: 'up',
      change: '+8%',
      description: 'February payments'
    }
  ];

  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      success: 'bg-green-100 text-green-800',
      pending: 'bg-yellow-100 text-yellow-800',
      failed: 'bg-red-100 text-red-800'
    };
    return (
      <Badge className={variants[status as keyof typeof variants] || 'bg-gray-100 text-gray-800'}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getPriorityBadge = (isOverdue: boolean, daysUntilDue?: number) => {
    if (isOverdue) {
      return <Badge className="bg-red-100 text-red-800">Overdue</Badge>;
    }
    if (daysUntilDue && daysUntilDue <= 3) {
      return <Badge className="bg-orange-100 text-orange-800">Due Soon</Badge>;
    }
    return <Badge className="bg-green-100 text-green-800">On Time</Badge>;
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold tracking-tight">Payment Dashboard</h1>
          <p className="text-muted-foreground">
            Manage your payments, view dues, and track transaction history
          </p>
        </div>
        <div className="flex flex-col space-y-2 md:flex-row md:space-x-2 md:space-y-0">
          <Button variant="outline" onClick={handleRefresh} disabled={refreshing} className="w-full md:w-auto">
            <RefreshCw className={`mr-2 h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button className="w-full md:w-auto">
            <Plus className="mr-2 h-4 w-4" />
            Make Payment
          </Button>
        </div>
      </div>

      {/* Overdue Alert */}
      {overduePayments.length > 0 && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            You have {overduePayments.length} overdue payment(s) totaling ₹{totalOverdue.toLocaleString()}. 
            Please pay immediately to avoid additional charges.
          </AlertDescription>
        </Alert>
      )}

      {/* Payment Statistics */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
        {paymentStats.map((stat, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                {stat.trend === 'up' ? (
                  <ArrowUpRight className="mr-1 h-3 w-3 text-green-500" />
                ) : (
                  <ArrowDownRight className="mr-1 h-3 w-3 text-red-500" />
                )}
                <span className={stat.trend === 'up' ? 'text-green-600' : 'text-red-600'}>
                  {stat.change}
                </span>
                <span className="ml-1">{stat.description}</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Pending Payments */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Clock className="h-5 w-5" />
                <span>Pending Payments</span>
                {pendingPayments.length > 0 && (
                  <Badge variant="outline">{pendingPayments.length}</Badge>
                )}
              </CardTitle>
              <CardDescription>
                Payments that require your attention
              </CardDescription>
            </CardHeader>
            <CardContent>
              {pendingPayments.length === 0 ? (
                <div className="text-center py-8">
                  <CheckCircle className="mx-auto h-12 w-12 text-green-500 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">All caught up!</h3>
                  <p className="text-gray-500">You have no pending payments at this time.</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {pendingPayments.map((payment) => {
                    const booking = userBookings.find(b => b.id === payment.bookingId);
                    const hostel = booking ? getHostelById(booking.hostelId) : null;
                    
                    return (
                      <div key={payment.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center space-x-4">
                          <div className={`p-2 rounded-lg ${payment.isOverdue ? 'bg-red-50' : 'bg-yellow-50'}`}>
                            {payment.isOverdue ? (
                              <AlertTriangle className="h-5 w-5 text-red-600" />
                            ) : (
                              <Clock className="h-5 w-5 text-yellow-600" />
                            )}
                          </div>
                          <div>
                            <div className="font-medium">{payment.description}</div>
                            <div className="text-sm text-muted-foreground">
                              {hostel?.name} • Due: {new Date(payment.dueDate).toLocaleDateString()}
                            </div>
                            <div className="flex items-center space-x-2 mt-1">
                              {getPriorityBadge(payment.isOverdue, payment.daysUntilDue)}
                              {payment.isOverdue && (
                                <span className="text-xs text-red-600">
                                  {payment.daysOverdue} days overdue
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold">₹{payment.amount.toLocaleString()}</div>
                          <Button size="sm" className="mt-2">
                            Pay Now
                          </Button>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Recent Payments */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <History className="h-5 w-5" />
                <span>Recent Payments</span>
              </CardTitle>
              <CardDescription>
                Your latest payment transactions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border overflow-hidden">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="min-w-[120px]">Date</TableHead>
                        <TableHead className="min-w-[150px]">Description</TableHead>
                        <TableHead className="min-w-[100px]">Method</TableHead>
                        <TableHead className="min-w-[100px]">Amount</TableHead>
                        <TableHead className="min-w-[100px]">Status</TableHead>
                        <TableHead className="text-right min-w-[80px]">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {recentPayments.map((payment) => {
                        const booking = userBookings.find(b => b.id === payment.bookingId);
                        const hostel = booking ? getHostelById(booking.hostelId) : null;
                        return (
                          <TableRow key={payment.id}>
                            <TableCell>
                              <div className="flex items-center space-x-1">
                                <Calendar className="h-3 w-3 text-muted-foreground" />
                                <span className="text-sm">
                                  {new Date(payment.date).toLocaleDateString()}
                                </span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div>
                                <div className="font-medium text-sm">{hostel?.name}</div>
                                <div className="text-xs text-muted-foreground">
                                  Monthly fee • {booking?.bedNumber}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center space-x-1">
                                <CreditCard className="h-3 w-3 text-muted-foreground" />
                                <span className="text-sm capitalize">{payment.paymentMethod}</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <span className="font-medium">₹{payment.amount.toLocaleString()}</span>
                            </TableCell>
                            <TableCell>
                              {getStatusBadge(payment.status)}
                            </TableCell>
                            <TableCell className="text-right">
                              <Button variant="ghost" size="sm">
                                <Eye className="h-3 w-3" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              </div>

              {recentPayments.length === 0 && (
                <div className="text-center py-8">
                  <Receipt className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No payments yet</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Your payment history will appear here once you make your first payment.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Target className="h-5 w-5" />
                <span>Quick Actions</span>
              </CardTitle>
              <CardDescription>
                Common payment tasks
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full justify-start">
                <Plus className="mr-2 h-4 w-4" />
                Make Payment
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Download className="mr-2 h-4 w-4" />
                Download Receipts
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Users className="mr-2 h-4 w-4" />
                Split Payment
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Bell className="mr-2 h-4 w-4" />
                Payment Reminders
              </Button>
            </CardContent>
          </Card>

          {/* Payment Methods */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Wallet className="h-5 w-5" />
                <span>Payment Methods</span>
              </CardTitle>
              <CardDescription>
                Saved payment options
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <CreditCard className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <div className="text-sm font-medium">•••• 4242</div>
                    <div className="text-xs text-muted-foreground">Expires 12/26</div>
                  </div>
                </div>
                <Badge variant="outline">Default</Badge>
              </div>

              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <Building2 className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <div className="text-sm font-medium">user@okaxis</div>
                    <div className="text-xs text-muted-foreground">UPI ID</div>
                  </div>
                </div>
              </div>

              <Button variant="outline" size="sm" className="w-full">
                <Plus className="mr-2 h-3 w-3" />
                Add Method
              </Button>
            </CardContent>
          </Card>

          {/* Payment Progress */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5" />
                <span>Monthly Progress</span>
              </CardTitle>
              <CardDescription>
                February 2024 payments
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Paid</span>
                  <span>₹18,000 / ₹30,000</span>
                </div>
                <Progress value={60} className="h-2" />
                <div className="text-xs text-muted-foreground">
                  60% complete • ₹12,000 remaining
                </div>
              </div>

              <div className="pt-4 border-t">
                <div className="text-sm font-medium mb-2">Upcoming Due Dates</div>
                <div className="space-y-2">
                  <div className="flex justify-between text-xs">
                    <span>Monthly Fee</span>
                    <span className="text-muted-foreground">Feb 15</span>
                  </div>
                  <div className="flex justify-between text-xs">
                    <span>Electricity Bill</span>
                    <span className="text-muted-foreground">Feb 20</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
