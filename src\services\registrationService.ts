// Registration service for handling hostel registration and account creation requests

import { HostelRegistrationRequest, AccountCreationRequest } from '@/types/admin';
import { OwnerRegistrationFormData } from '@/types/owner';
import { 
  mockHostelRegistrationRequests, 
  mockAccountCreationRequests,
  mockUsers 
} from '@/data/mockData';

export interface RegistrationSubmissionResult {
  success: boolean;
  requestId?: string;
  error?: string;
}

export interface AccountCreationSubmissionData {
  name: string;
  email: string;
  phone: string;
  role: 'owner' | 'employee';
  businessType?: string;
  experienceYears?: number;
  referenceContact?: string;
  documents: {
    idProof: {
      type: 'aadhar' | 'pan' | 'passport' | 'driving_license';
      number: string;
      file?: File;
    };
    businessProof?: {
      type: 'gst' | 'shop_act' | 'trade_license';
      number: string;
      file?: File;
    };
  };
}

// Mock file upload service
const uploadFile = async (file: File): Promise<string> => {
  // Simulate file upload delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Return mock file URL
  return `/documents/${file.name}`;
};

// Submit hostel registration request
export const submitHostelRegistration = async (
  formData: OwnerRegistrationFormData,
  uploadedFiles: Record<string, File[]>,
  selectedAmenities: string[],
  userId: string
): Promise<RegistrationSubmissionResult> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Upload files and get URLs
    const documentUrls: Record<string, string> = {};
    const photoUrls: Record<string, string[]> = {
      exterior: [],
      rooms: [],
      commonAreas: [],
      amenities: [],
      kitchen: []
    };

    // Upload document files
    for (const [key, files] of Object.entries(uploadedFiles)) {
      if (files.length > 0) {
        if (key.includes('photo') || key.includes('image')) {
          // Handle photo uploads
          const category = key.includes('exterior') ? 'exterior' :
                          key.includes('room') ? 'rooms' :
                          key.includes('common') ? 'commonAreas' :
                          key.includes('amenity') ? 'amenities' : 'kitchen';
          
          for (const file of files) {
            const url = await uploadFile(file);
            photoUrls[category].push(url);
          }
        } else {
          // Handle document uploads
          documentUrls[key] = await uploadFile(files[0]);
        }
      }
    }

    // Generate unique request ID
    const requestId = `hr_${Date.now()}`;

    // Create hostel registration request
    const registrationRequest: HostelRegistrationRequest = {
      id: requestId,
      requestType: 'hostel_registration',
      submittedBy: userId,
      submittedAt: new Date().toISOString(),
      status: 'pending',
      hostelDetails: {
        name: formData.hostelName,
        address: formData.hostelAddress,
        city: formData.selectedCity,
        state: formData.selectedState || 'Maharashtra', // Default fallback
        pincode: formData.pincode,
        areaId: formData.selectedArea,
        totalBeds: parseInt(formData.totalBeds),
        amenities: selectedAmenities,
        pricePerBed: parseInt(formData.standardMonthlyRate || '0'),
        bedTypes: {
          standard: {
            count: parseInt(formData.sharing1NonAcCount || '0') + parseInt(formData.sharing2NonAcCount || '0'),
            monthlyRate: parseInt(formData.standardMonthlyRate || '0'),
            dailyRate: parseInt(formData.standardDailyRate || '0')
          },
          premium: {
            count: parseInt(formData.sharing1AcCount || '0') + parseInt(formData.sharing2AcCount || '0'),
            monthlyRate: parseInt(formData.premiumMonthlyRate || '0'),
            dailyRate: parseInt(formData.premiumDailyRate || '0')
          }
        },
        foodPackages: {
          withMeals: {
            dailyRate: parseInt(formData.withMealsDailyRate || '0'),
            description: 'Includes breakfast, lunch, and dinner'
          },
          withoutMeals: {
            dailyRate: parseInt(formData.withoutMealsDailyRate || '0'),
            description: 'No meals included'
          }
        },
        securityDeposit: parseInt(formData.securityDeposit || '0'),
        minimumStayHours: parseInt(formData.minimumStayHours || '24'),
        hourlyRate: parseInt(formData.hourlyRate || '0')
      },
      ownerDetails: {
        name: formData.ownerName,
        email: formData.email,
        phone: formData.phone,
        alternatePhone: formData.alternatePhone,
        businessExperience: parseInt(formData.businessExperience || '0'),
        previousHostelExperience: formData.previousHostelExperience || false
      },
      documents: {
        ownerIdProof: {
          type: formData.ownerIdType as any || 'aadhar',
          number: formData.ownerIdNumber,
          fileUrl: documentUrls['ownerIdProof'],
          verified: false
        },
        businessRegistration: formData.businessRegNumber ? {
          type: 'gst',
          number: formData.businessRegNumber,
          fileUrl: documentUrls['businessRegistration'],
          verified: false
        } : undefined,
        propertyDocuments: {
          type: formData.propertyDocType as any || 'ownership',
          fileUrl: documentUrls['propertyDocuments'],
          verified: false
        },
        bankDetails: {
          accountNumber: formData.bankAccountNumber,
          ifscCode: formData.bankIfscCode,
          bankName: formData.bankName,
          branchName: formData.bankName + ' Branch', // Simplified
          accountHolderName: formData.accountHolderName,
          accountType: 'current'
        }
      },
      photos: photoUrls
    };

    // Add to mock data (in real app, this would be an API call)
    mockHostelRegistrationRequests.push(registrationRequest);

    return {
      success: true,
      requestId: requestId
    };

  } catch (error) {
    console.error('Error submitting hostel registration:', error);
    return {
      success: false,
      error: 'Failed to submit registration request'
    };
  }
};

// Submit account creation request
export const submitAccountCreationRequest = async (
  data: AccountCreationSubmissionData
): Promise<RegistrationSubmissionResult> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Check if email already exists
    const existingUser = mockUsers.find(user => user.email.toLowerCase() === data.email.toLowerCase());
    if (existingUser) {
      return {
        success: false,
        error: 'An account with this email already exists'
      };
    }

    // Upload documents
    const documentUrls: Record<string, string> = {};
    if (data.documents.idProof.file) {
      documentUrls.idProof = await uploadFile(data.documents.idProof.file);
    }
    if (data.documents.businessProof?.file) {
      documentUrls.businessProof = await uploadFile(data.documents.businessProof.file);
    }

    // Generate unique request ID
    const requestId = `ac_${Date.now()}`;

    // Create account creation request
    const accountRequest: AccountCreationRequest = {
      id: requestId,
      requestType: 'account_creation',
      submittedAt: new Date().toISOString(),
      status: 'pending',
      requestedUserDetails: {
        name: data.name,
        email: data.email,
        phone: data.phone,
        role: data.role,
        businessType: data.businessType,
        experienceYears: data.experienceYears,
        referenceContact: data.referenceContact
      },
      documents: {
        idProof: {
          type: data.documents.idProof.type,
          number: data.documents.idProof.number,
          fileUrl: documentUrls.idProof
        },
        businessProof: data.documents.businessProof ? {
          type: data.documents.businessProof.type,
          number: data.documents.businessProof.number,
          fileUrl: documentUrls.businessProof
        } : undefined
      }
    };

    // Add to mock data (in real app, this would be an API call)
    mockAccountCreationRequests.push(accountRequest);

    return {
      success: true,
      requestId: requestId
    };

  } catch (error) {
    console.error('Error submitting account creation request:', error);
    return {
      success: false,
      error: 'Failed to submit account creation request'
    };
  }
};

// Get registration request status
export const getRegistrationStatus = async (requestId: string) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));

  const hostelRequest = mockHostelRegistrationRequests.find(r => r.id === requestId);
  const accountRequest = mockAccountCreationRequests.find(r => r.id === requestId);

  return hostelRequest || accountRequest || null;
};
