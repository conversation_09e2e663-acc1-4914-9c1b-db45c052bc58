import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Bed,
  Users,
  MapPin,
  Calendar,
  Clock,
  CheckCircle,
  AlertTriangle,
  XCircle,
  RefreshCw,
  Settings,
  User,
  Building2,
  Wrench,
  Eye,
  Move,
  Save,
  Undo,
  Filter,
  Search,
  Grid3X3,
  List,
  Zap
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/hooks/use-toast';
import { mockUsers, mockBookings } from '@/data/mockData';
import { RoomAllocation, AllocationStatus, AllocationConflict } from '@/types/owner';

// Mock room allocation data
const mockRoomAllocations: RoomAllocation[] = [
  {
    id: 'ROOM_A101_1',
    hostelId: '1',
    roomNumber: 'A101',
    bedNumber: 'A101-1',
    sharingType: 'sharing2',
    roomType: 'ac',
    status: 'occupied',
    currentOccupant: {
      userId: '11',
      name: 'Rohit Verma',
      checkInDate: '2024-01-15',
      expectedCheckOut: '2024-07-15',
      bookingId: 'BOOK_001'
    },
    allocationHistory: [
      {
        userId: '11',
        userName: 'Rohit Verma',
        checkInDate: '2024-01-15',
        bookingId: 'BOOK_001',
        rating: 4.5
      }
    ],
    lastCleaned: '2024-02-10',
    lastMaintenance: '2024-01-20'
  },
  {
    id: 'ROOM_A101_2',
    hostelId: '1',
    roomNumber: 'A101',
    bedNumber: 'A101-2',
    sharingType: 'sharing1',
    roomType: 'nonAc',
    status: 'available',
    allocationHistory: [
      {
        userId: '12',
        userName: 'Priya Sharma',
        checkInDate: '2023-12-01',
        checkOutDate: '2024-01-31',
        bookingId: 'BOOK_002',
        rating: 4.8
      }
    ],
    lastCleaned: '2024-02-09',
    lastMaintenance: '2024-01-15'
  },
  {
    id: 'ROOM_A102_1',
    hostelId: '1',
    roomNumber: 'A102',
    bedNumber: 'A102-1',
    sharingType: 'sharing3',
    roomType: 'ac',
    status: 'maintenance',
    allocationHistory: [],
    lastCleaned: '2024-02-08',
    lastMaintenance: '2024-02-10',
    maintenanceNotes: 'AC repair in progress'
  },
  {
    id: 'ROOM_A102_2',
    hostelId: '1',
    roomNumber: 'A102',
    bedNumber: 'A102-2',
    sharingType: 'sharing4',
    roomType: 'nonAc',
    status: 'reserved',
    allocationHistory: [],
    lastCleaned: '2024-02-10',
    lastMaintenance: '2024-01-25'
  }
];

// Mock pending applications for allocation
const mockPendingAllocations = [
  {
    id: 'PENDING_001',
    memberName: 'Amit Kumar',
    memberEmail: '<EMAIL>',
    sharingType: 'sharing1',
    roomType: 'ac',
    checkInDate: '2024-02-15',
    duration: 180,
    priority: 'high',
    score: 92
  },
  {
    id: 'PENDING_002',
    memberName: 'Sneha Patel',
    memberEmail: '<EMAIL>',
    sharingType: 'sharing2',
    roomType: 'ac',
    checkInDate: '2024-02-20',
    duration: 90,
    priority: 'medium',
    score: 88
  }
];

export const RoomAllocationInterface: React.FC = () => {
  const { user } = useAuth();
  const [allocations, setAllocations] = useState<RoomAllocation[]>(mockRoomAllocations);
  const [pendingAllocations, setPendingAllocations] = useState(mockPendingAllocations);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterBedType, setFilterBedType] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBed, setSelectedBed] = useState<RoomAllocation | null>(null);
  const [selectedMember, setSelectedMember] = useState<any>(null);
  const [showAllocationDialog, setShowAllocationDialog] = useState(false);
  const [conflicts, setConflicts] = useState<AllocationConflict[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [draggedMember, setDraggedMember] = useState<any>(null);

  // Filter allocations
  const filteredAllocations = allocations.filter(allocation => {
    const matchesStatus = filterStatus === 'all' || allocation.status === filterStatus;
    const matchesBedType = filterBedType === 'all' || allocation.roomType === filterBedType;
    const matchesSearch = allocation.bedNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         allocation.roomNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (allocation.currentOccupant?.name.toLowerCase().includes(searchTerm.toLowerCase()));
    
    return matchesStatus && matchesBedType && matchesSearch;
  });

  // Group allocations by room
  const groupedAllocations = filteredAllocations.reduce((groups, allocation) => {
    const room = allocation.roomNumber;
    if (!groups[room]) {
      groups[room] = [];
    }
    groups[room].push(allocation);
    return groups;
  }, {} as Record<string, RoomAllocation[]>);

  const getStatusColor = (status: AllocationStatus) => {
    const colors = {
      available: 'bg-green-100 text-green-800 border-green-200',
      occupied: 'bg-blue-100 text-blue-800 border-blue-200',
      maintenance: 'bg-red-100 text-red-800 border-red-200',
      reserved: 'bg-yellow-100 text-yellow-800 border-yellow-200'
    };
    return colors[status];
  };

  const getStatusIcon = (status: AllocationStatus) => {
    const icons = {
      available: CheckCircle,
      occupied: User,
      maintenance: Wrench,
      reserved: Clock
    };
    return icons[status];
  };

  const handleDragStart = (member: any) => {
    setDraggedMember(member);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, allocation: RoomAllocation) => {
    e.preventDefault();
    
    if (!draggedMember || allocation.status !== 'available') {
      return;
    }

    // Check for conflicts
    const roomTypeMatch = allocation.sharingType === draggedMember.sharingType && allocation.roomType === draggedMember.roomType;
    if (!roomTypeMatch) {
      toast({
        title: "Room Type Mismatch",
        description: `Member requested ${draggedMember.sharingType.replace('sharing', '')} sharing ${draggedMember.roomType === 'ac' ? 'AC' : 'Non-AC'} room, but selected room is ${allocation.sharingType.replace('sharing', '')} sharing ${allocation.roomType === 'ac' ? 'AC' : 'Non-AC'}`,
        variant: "destructive"
      });
      return;
    }

    setSelectedBed(allocation);
    setSelectedMember(draggedMember);
    setShowAllocationDialog(true);
    setDraggedMember(null);
  };

  const handleManualAllocation = (allocation: RoomAllocation) => {
    if (allocation.status !== 'available') {
      toast({
        title: "Bed Not Available",
        description: "This bed is not available for allocation",
        variant: "destructive"
      });
      return;
    }
    
    setSelectedBed(allocation);
    setShowAllocationDialog(true);
  };

  const handleConfirmAllocation = async () => {
    if (!selectedBed || !selectedMember) return;

    setIsProcessing(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Update allocation
      setAllocations(prev => 
        prev.map(allocation => 
          allocation.id === selectedBed.id
            ? {
                ...allocation,
                status: 'occupied' as AllocationStatus,
                currentOccupant: {
                  userId: selectedMember.id || 'new_user',
                  name: selectedMember.memberName,
                  checkInDate: selectedMember.checkInDate,
                  expectedCheckOut: new Date(
                    new Date(selectedMember.checkInDate).getTime() + 
                    selectedMember.duration * 24 * 60 * 60 * 1000
                  ).toISOString().split('T')[0],
                  bookingId: `BOOK_${Date.now()}`
                }
              }
            : allocation
        )
      );

      // Remove from pending allocations
      setPendingAllocations(prev => 
        prev.filter(pending => pending.id !== selectedMember.id)
      );

      toast({
        title: "Allocation Successful",
        description: `${selectedMember.memberName} has been allocated to bed ${selectedBed.bedNumber}`,
      });

      setShowAllocationDialog(false);
      setSelectedBed(null);
      setSelectedMember(null);

    } catch (error) {
      toast({
        title: "Allocation Failed",
        description: "Failed to allocate bed. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleStatusChange = async (allocationId: string, newStatus: AllocationStatus) => {
    setIsProcessing(true);

    try {
      await new Promise(resolve => setTimeout(resolve, 1000));

      setAllocations(prev => 
        prev.map(allocation => 
          allocation.id === allocationId
            ? { 
                ...allocation, 
                status: newStatus,
                ...(newStatus === 'available' ? { currentOccupant: undefined } : {})
              }
            : allocation
        )
      );

      toast({
        title: "Status Updated",
        description: `Bed status has been updated to ${newStatus}`,
      });

    } catch (error) {
      toast({
        title: "Update Failed",
        description: "Failed to update bed status. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Calculate statistics
  const stats = {
    total: allocations.length,
    available: allocations.filter(a => a.status === 'available').length,
    occupied: allocations.filter(a => a.status === 'occupied').length,
    maintenance: allocations.filter(a => a.status === 'maintenance').length,
    occupancyRate: Math.round((allocations.filter(a => a.status === 'occupied').length / allocations.length) * 100)
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold tracking-tight">Room Allocation</h1>
          <p className="text-muted-foreground">
            Manage bed allocations and room assignments with drag-and-drop interface
          </p>
        </div>
        <div className="flex flex-col space-y-2 md:flex-row md:space-x-2 md:space-y-0">
          <Button variant="outline" className="w-full md:w-auto">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button variant="outline" className="w-full md:w-auto">
            <Settings className="mr-2 h-4 w-4" />
            Room Settings
          </Button>
          <Button className="w-full md:w-auto">
            <Zap className="mr-2 h-4 w-4" />
            Auto Allocate
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Beds</CardTitle>
            <Bed className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.available}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Occupied</CardTitle>
            <User className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.occupied}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Maintenance</CardTitle>
            <Wrench className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.maintenance}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Occupancy Rate</CardTitle>
            <Building2 className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{stats.occupancyRate}%</div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 lg:grid-cols-4">
        {/* Pending Allocations Sidebar */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>Pending Allocations</span>
              </CardTitle>
              <CardDescription>
                Drag members to available beds
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {pendingAllocations.map((member) => (
                <div
                  key={member.id}
                  draggable
                  onDragStart={() => handleDragStart(member)}
                  className="p-3 border rounded-lg cursor-move hover:shadow-md transition-shadow bg-white"
                >
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${member.memberName}`} />
                      <AvatarFallback className="text-xs">
                        {member.memberName.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-sm truncate">{member.memberName}</div>
                      <div className="text-xs text-muted-foreground">
                        {member.sharingType.replace('sharing', '')} sharing {member.roomType === 'ac' ? 'AC' : 'Non-AC'} • {member.duration}d
                      </div>
                      <div className="flex items-center space-x-1 mt-1">
                        <Badge variant="outline" className="text-xs">
                          Score: {member.score}
                        </Badge>
                        <Badge
                          className={`text-xs ${
                            member.priority === 'high' ? 'bg-red-100 text-red-800' :
                            member.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-green-100 text-green-800'
                          }`}
                        >
                          {member.priority}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              {pendingAllocations.length === 0 && (
                <div className="text-center py-8">
                  <Users className="mx-auto h-8 w-8 text-gray-400" />
                  <p className="text-sm text-muted-foreground mt-2">
                    No pending allocations
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Room Allocation Grid */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Grid3X3 className="h-5 w-5" />
                <span>Room Layout</span>
              </CardTitle>
              <CardDescription>
                Click on available beds to manually allocate or drag members from the sidebar
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Filters */}
              <div className="flex flex-col space-y-4 md:flex-row md:items-center md:space-x-4 md:space-y-0 mb-6">
                <div className="relative flex-1">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search by bed number, room, or occupant..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8"
                  />
                </div>
                <div className="flex space-x-2">
                  <Select value={filterStatus} onValueChange={setFilterStatus}>
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="available">Available</SelectItem>
                      <SelectItem value="occupied">Occupied</SelectItem>
                      <SelectItem value="maintenance">Maintenance</SelectItem>
                      <SelectItem value="reserved">Reserved</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={filterBedType} onValueChange={setFilterBedType}>
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="Room Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="ac">AC</SelectItem>
                      <SelectItem value="nonAc">Non-AC</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Room Grid */}
              <div className="space-y-6">
                {Object.entries(groupedAllocations).map(([roomNumber, beds]) => (
                  <div key={roomNumber} className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Building2 className="h-4 w-4 text-muted-foreground" />
                      <h3 className="font-medium">Room {roomNumber}</h3>
                      <Badge variant="outline">{beds.length} beds</Badge>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                      {beds.map((allocation) => {
                        const StatusIcon = getStatusIcon(allocation.status);
                        return (
                          <div
                            key={allocation.id}
                            className={`p-4 border-2 rounded-lg transition-all cursor-pointer ${getStatusColor(allocation.status)} ${
                              allocation.status === 'available' ? 'hover:shadow-md' : ''
                            }`}
                            onDragOver={handleDragOver}
                            onDrop={(e) => handleDrop(e, allocation)}
                            onClick={() => handleManualAllocation(allocation)}
                          >
                            <div className="flex items-center justify-between mb-3">
                              <div className="flex items-center space-x-2">
                                <StatusIcon className="h-4 w-4" />
                                <span className="font-medium">{allocation.bedNumber}</span>
                              </div>
                              <Badge variant="outline" className="text-xs">
                                {allocation.sharingType.replace('sharing', '')} sharing {allocation.roomType === 'ac' ? 'AC' : 'Non-AC'}
                              </Badge>
                            </div>

                            {allocation.currentOccupant ? (
                              <div className="space-y-2">
                                <div className="flex items-center space-x-2">
                                  <Avatar className="h-6 w-6">
                                    <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${allocation.currentOccupant.name}`} />
                                    <AvatarFallback className="text-xs">
                                      {allocation.currentOccupant.name.split(' ').map(n => n[0]).join('')}
                                    </AvatarFallback>
                                  </Avatar>
                                  <span className="text-sm font-medium truncate">
                                    {allocation.currentOccupant.name}
                                  </span>
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  <div>Check-in: {new Date(allocation.currentOccupant.checkInDate).toLocaleDateString()}</div>
                                  <div>Check-out: {new Date(allocation.currentOccupant.expectedCheckOut).toLocaleDateString()}</div>
                                </div>
                              </div>
                            ) : (
                              <div className="space-y-2">
                                <div className="text-sm text-muted-foreground">
                                  {allocation.status === 'available' && 'Available for allocation'}
                                  {allocation.status === 'maintenance' && allocation.maintenanceNotes}
                                  {allocation.status === 'reserved' && 'Reserved for future booking'}
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  Last cleaned: {new Date(allocation.lastCleaned).toLocaleDateString()}
                                </div>
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Allocation Confirmation Dialog */}
      <Dialog open={showAllocationDialog} onOpenChange={setShowAllocationDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Confirm Bed Allocation</DialogTitle>
            <DialogDescription>
              Review the allocation details before confirming
            </DialogDescription>
          </DialogHeader>

          {selectedBed && selectedMember && (
            <div className="space-y-4">
              <div className="p-4 bg-muted rounded-lg">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Member:</span>
                    <div>{selectedMember.memberName}</div>
                  </div>
                  <div>
                    <span className="font-medium">Bed:</span>
                    <div>{selectedBed.bedNumber}</div>
                  </div>
                  <div>
                    <span className="font-medium">Room Type:</span>
                    <div className="capitalize">{selectedBed.sharingType.replace('sharing', '')} sharing {selectedBed.roomType === 'ac' ? 'AC' : 'Non-AC'}</div>
                  </div>
                  <div>
                    <span className="font-medium">Check-in:</span>
                    <div>{new Date(selectedMember.checkInDate).toLocaleDateString()}</div>
                  </div>
                </div>
              </div>

              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  This allocation will generate an invoice and send confirmation to the member.
                </AlertDescription>
              </Alert>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAllocationDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleConfirmAllocation} disabled={isProcessing}>
              {isProcessing ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Allocating...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Confirm Allocation
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
