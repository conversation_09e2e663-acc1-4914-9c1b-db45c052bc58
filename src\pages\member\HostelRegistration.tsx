import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Building2, 
  MapPin,
  Users,
  Calendar,
  CheckCircle,
  AlertCircle,
  ArrowRight,
  ArrowLeft,
  Home,
  CreditCard,
  Clock
} from 'lucide-react';
import { LocationSelector } from '@/pages/member/LocationSelector';
import { BedAvailabilityGrid } from '@/pages/member/BedAvailabilityGrid';
import { MemberRegistrationFlow } from '@/pages/member/MemberRegistrationFlow';
import { BillingCalculator } from '@/pages/member/BillingCalculator';
import { type City, type Area, type Hostel } from '@/data/mockData';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/hooks/use-toast';

type RegistrationStep = 'location' | 'membership' | 'bed-selection' | 'registration' | 'billing' | 'confirmation';

interface RegistrationState {
  step: RegistrationStep;
  selectedLocation: {
    city?: City;
    area?: Area;
    hostel?: Hostel;
  };
  membershipType?: 'regular' | 'guest';
  bedSelection?: {
    bedType: 'standard' | 'premium';
    foodPackage: 'with_meals' | 'without_meals';
    pricing: {
      bedRate: number;
      foodRate: number;
      total: number;
    };
  };
  registrationData?: any;
  bookingId?: string;
}

export const HostelRegistration: React.FC = () => {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const [registrationState, setRegistrationState] = useState<RegistrationState>({
    step: 'location',
    selectedLocation: {}
  });

  const steps = [
    { id: 'location', title: 'Select Location', icon: MapPin },
    { id: 'membership', title: 'Membership Type', icon: Users },
    { id: 'bed-selection', title: 'Bed & Food', icon: Building2 },
    { id: 'registration', title: 'Registration', icon: Calendar },
    { id: 'billing', title: 'Billing', icon: CreditCard },
    { id: 'confirmation', title: 'Confirmation', icon: CheckCircle }
  ];

  const getCurrentStepIndex = () => {
    return steps.findIndex(step => step.id === registrationState.step);
  };

  const handleLocationChange = (selection: { city?: City; area?: Area; hostel?: Hostel }) => {
    setRegistrationState(prev => ({
      ...prev,
      selectedLocation: selection
    }));
  };

  const handleMembershipTypeSelect = (type: 'regular' | 'guest') => {
    setRegistrationState(prev => ({
      ...prev,
      membershipType: type,
      step: 'bed-selection'
    }));
  };

  const handleBedSelection = (selection: {
    bedType: 'standard' | 'premium';
    foodPackage: 'with_meals' | 'without_meals';
    pricing: { bedRate: number; foodRate: number; total: number };
  }) => {
    setRegistrationState(prev => ({
      ...prev,
      bedSelection: selection
    }));
  };

  const handleRegistrationComplete = async (data: any) => {
    setIsLoading(true);
    setError(null);

    try {
      // Simulate API call to create user and booking
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Generate booking ID
      const bookingId = `BK${Date.now()}`;

      // Send notification to hostel owner for approval
      if (registrationState.selectedLocation.hostel) {
        // Simulate owner notification API call
        await new Promise(resolve => setTimeout(resolve, 500));
        console.log(`Notification sent to hostel owner for registration approval:`, {
          hostelId: registrationState.selectedLocation.hostel.id,
          membershipType: registrationState.membershipType,
          memberDetails: data.personalInfo,
          bookingId
        });
      }

      setRegistrationState(prev => ({
        ...prev,
        registrationData: data,
        bookingId,
        step: 'billing'
      }));

      toast({
        title: "Registration Successful",
        description: "Your registration has been completed successfully. Hostel owner has been notified for approval.",
      });
    } catch (err) {
      setError('Registration failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePaymentComplete = async () => {
    setIsLoading(true);
    
    try {
      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      setRegistrationState(prev => ({
        ...prev,
        step: 'confirmation'
      }));

      toast({
        title: "Payment Successful",
        description: "Your booking has been confirmed.",
      });
    } catch (err) {
      setError('Payment failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const canProceedToNext = () => {
    switch (registrationState.step) {
      case 'location':
        return !!registrationState.selectedLocation.hostel;
      case 'membership':
        return !!registrationState.membershipType;
      case 'bed-selection':
        return !!registrationState.bedSelection;
      case 'registration':
        return !!registrationState.registrationData;
      case 'billing':
        return true;
      default:
        return false;
    }
  };

  const handleNext = () => {
    const currentIndex = getCurrentStepIndex();
    if (currentIndex < steps.length - 1 && canProceedToNext()) {
      const nextStep = steps[currentIndex + 1].id as RegistrationStep;
      setRegistrationState(prev => ({ ...prev, step: nextStep }));
    }
  };

  const handlePrevious = () => {
    const currentIndex = getCurrentStepIndex();
    if (currentIndex > 0) {
      const prevStep = steps[currentIndex - 1].id as RegistrationStep;
      setRegistrationState(prev => ({ ...prev, step: prevStep }));
    }
  };

  const renderStepContent = () => {
    switch (registrationState.step) {
      case 'location':
        return (
          <LocationSelector
            onSelectionChange={handleLocationChange}
            selectedCity={registrationState.selectedLocation.city?.id}
            selectedArea={registrationState.selectedLocation.area?.id}
            selectedHostel={registrationState.selectedLocation.hostel?.id}
          />
        );

      case 'membership':
        return (
          <Card>
            <CardHeader>
              <CardTitle>Choose Membership Type</CardTitle>
              <CardDescription>
                Select the membership type that best suits your needs
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <Card 
                  className="cursor-pointer transition-all hover:shadow-md"
                  onClick={() => handleMembershipTypeSelect('regular')}
                >
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <div className="flex items-center space-x-3">
                        <Users className="h-6 w-6 text-blue-600" />
                        <h4 className="font-semibold">Regular Member</h4>
                      </div>
                      <ul className="text-sm space-y-1">
                        <li>• Monthly subscription</li>
                        <li>• Better rates for long-term stays</li>
                        <li>• Priority booking</li>
                        <li>• Member benefits</li>
                      </ul>
                      <Badge className="bg-blue-100 text-blue-800">Recommended</Badge>
                    </div>
                  </CardContent>
                </Card>

                <Card 
                  className="cursor-pointer transition-all hover:shadow-md"
                  onClick={() => handleMembershipTypeSelect('guest')}
                >
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <div className="flex items-center space-x-3">
                        <Calendar className="h-6 w-6 text-green-600" />
                        <h4 className="font-semibold">Guest Member</h4>
                      </div>
                      <ul className="text-sm space-y-1">
                        <li>• Pay per day/hour</li>
                        <li>• Flexible check-in/out</li>
                        <li>• No commitment</li>
                        <li>• Instant booking</li>
                      </ul>
                      <Badge className="bg-green-100 text-green-800">Flexible</Badge>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        );

      case 'bed-selection':
        return registrationState.selectedLocation.hostel && registrationState.membershipType ? (
          <BedAvailabilityGrid
            hostel={registrationState.selectedLocation.hostel}
            membershipType={registrationState.membershipType}
            onBedSelect={handleBedSelection}
            selectedBedType={registrationState.bedSelection?.bedType}
            selectedFoodPackage={registrationState.bedSelection?.foodPackage}
          />
        ) : null;

      case 'registration':
        return (
          <MemberRegistrationFlow
            onComplete={handleRegistrationComplete}
            onCancel={() => setRegistrationState(prev => ({ ...prev, step: 'bed-selection' }))}
          />
        );

      case 'billing':
        return registrationState.selectedLocation.hostel && 
               registrationState.membershipType && 
               registrationState.bedSelection ? (
          <div className="space-y-6">
            <BillingCalculator
              hostel={registrationState.selectedLocation.hostel}
              membershipType={registrationState.membershipType}
              bedType={registrationState.bedSelection.bedType}
              foodPackage={registrationState.bedSelection.foodPackage}
            />
            
            <Card>
              <CardContent className="pt-6">
                <Button 
                  onClick={handlePaymentComplete}
                  disabled={isLoading}
                  className="w-full"
                  size="lg"
                >
                  {isLoading ? (
                    <>
                      <Clock className="h-4 w-4 mr-2 animate-spin" />
                      Processing Payment...
                    </>
                  ) : (
                    <>
                      <CreditCard className="h-4 w-4 mr-2" />
                      Proceed to Payment
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </div>
        ) : null;

      case 'confirmation':
        return (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center space-y-4">
                <CheckCircle className="h-16 w-16 text-green-600 mx-auto" />
                <h3 className="text-2xl font-bold">Registration Successful!</h3>
                <p className="text-muted-foreground">
                  Your hostel registration has been completed successfully.
                </p>
                
                {registrationState.bookingId && (
                  <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <p className="font-medium">Booking ID: {registrationState.bookingId}</p>
                    <p className="text-sm text-muted-foreground">
                      Please save this ID for future reference
                    </p>
                  </div>
                )}

                <div className="flex justify-center space-x-4">
                  <Button variant="outline">
                    <Home className="h-4 w-4 mr-2" />
                    Go to Dashboard
                  </Button>
                  <Button>
                    View Booking Details
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">Hostel Registration</h1>
          <p className="text-muted-foreground">
            Complete your registration to book your preferred hostel
          </p>
        </div>

        {/* Progress Steps */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                    getCurrentStepIndex() >= index 
                      ? 'bg-primary border-primary text-primary-foreground' 
                      : 'border-muted-foreground text-muted-foreground'
                  }`}>
                    <step.icon className="h-5 w-5" />
                  </div>
                  <div className="ml-3 hidden md:block">
                    <p className={`text-sm font-medium ${
                      getCurrentStepIndex() >= index ? 'text-primary' : 'text-muted-foreground'
                    }`}>
                      {step.title}
                    </p>
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`w-12 h-0.5 mx-4 ${
                      getCurrentStepIndex() > index ? 'bg-primary' : 'bg-muted'
                    }`} />
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Step Content */}
        {renderStepContent()}

        {/* Navigation */}
        {registrationState.step !== 'registration' && 
         registrationState.step !== 'billing' && 
         registrationState.step !== 'confirmation' && (
          <Card>
            <CardContent className="pt-6">
              <div className="flex justify-between">
                <Button 
                  variant="outline" 
                  onClick={handlePrevious}
                  disabled={getCurrentStepIndex() === 0}
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Previous
                </Button>
                
                <Button 
                  onClick={handleNext}
                  disabled={!canProceedToNext() || getCurrentStepIndex() === steps.length - 1}
                >
                  Next
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};
