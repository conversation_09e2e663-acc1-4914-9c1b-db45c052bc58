import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Users, 
  Search, 
  Plus,
  MoreHorizontal,
  Eye,
  Edit,
  Phone,
  Mail,
  Calendar,
  Bed,
  CheckCircle,
  AlertTriangle,
  Clock,
  UserPlus,
  MessageSquare
} from 'lucide-react';
import { mockBookings, mockUsers, getUserById } from '@/data/mockData';

export const ResidentsManagement: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'checkout_pending' | 'issues'>('all');

  // Get current residents (confirmed bookings)
  const currentResidents = mockBookings
    .filter(booking => booking.status === 'confirmed')
    .map(booking => {
      const user = getUserById(booking.userId);
      return {
        ...booking,
        user,
        checkInDate: new Date(booking.checkIn),
        checkOutDate: new Date(booking.checkOut),
        isActive: new Date() >= new Date(booking.checkIn) && new Date() <= new Date(booking.checkOut),
        isCheckoutPending: new Date() > new Date(booking.checkOut),
        hasIssues: Math.random() > 0.8, // Mock issue status
      };
    })
    .filter(resident => resident.user);

  const filteredResidents = currentResidents.filter(resident => {
    const matchesSearch = resident.user?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         resident.user?.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         resident.bedNumber.toLowerCase().includes(searchTerm.toLowerCase());
    
    let matchesStatus = true;
    if (statusFilter === 'active') matchesStatus = resident.isActive && !resident.isCheckoutPending;
    else if (statusFilter === 'checkout_pending') matchesStatus = resident.isCheckoutPending;
    else if (statusFilter === 'issues') matchesStatus = resident.hasIssues;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (resident: typeof currentResidents[0]) => {
    if (resident.isCheckoutPending) {
      return <Badge className="bg-orange-100 text-orange-800">Checkout Pending</Badge>;
    }
    if (resident.hasIssues) {
      return <Badge className="bg-red-100 text-red-800">Has Issues</Badge>;
    }
    if (resident.isActive) {
      return <Badge className="bg-green-100 text-green-800">Active</Badge>;
    }
    return <Badge variant="secondary">Inactive</Badge>;
  };

  const getUserInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getResidentStats = () => {
    return {
      total: currentResidents.length,
      active: currentResidents.filter(r => r.isActive && !r.isCheckoutPending).length,
      checkoutPending: currentResidents.filter(r => r.isCheckoutPending).length,
      withIssues: currentResidents.filter(r => r.hasIssues).length,
    };
  };

  const stats = getResidentStats();

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Residents Management</h1>
          <p className="text-muted-foreground">
            Manage current residents and their stay details
          </p>
        </div>
        <Button>
          <UserPlus className="mr-2 h-4 w-4" />
          Check-in New Resident
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Residents</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              Currently staying
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Residents</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.active}</div>
            <p className="text-xs text-muted-foreground">
              In good standing
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Checkout Pending</CardTitle>
            <Clock className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.checkoutPending}</div>
            <p className="text-xs text-muted-foreground">
              Need attention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">With Issues</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.withIssues}</div>
            <p className="text-xs text-muted-foreground">
              Require follow-up
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Residents Directory */}
      <Card>
        <CardHeader>
          <CardTitle>Current Residents</CardTitle>
          <CardDescription>
            View and manage all current hostel residents
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search residents by name, email, or bed number..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  Status: {statusFilter === 'all' ? 'All' : statusFilter.replace('_', ' ')}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setStatusFilter('all')}>All Residents</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('active')}>Active Only</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('checkout_pending')}>Checkout Pending</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('issues')}>With Issues</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Resident</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>Bed</TableHead>
                  <TableHead>Check-in</TableHead>
                  <TableHead>Check-out</TableHead>
                  <TableHead>Duration</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredResidents.map((resident) => {
                  const stayDuration = Math.ceil((resident.checkOutDate.getTime() - resident.checkInDate.getTime()) / (1000 * 60 * 60 * 24));
                  return (
                    <TableRow key={resident.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={resident.user?.avatar} alt={resident.user?.name} />
                            <AvatarFallback>
                              {getUserInitials(resident.user?.name || '')}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{resident.user?.name}</div>
                            <div className="text-sm text-muted-foreground">ID: {resident.user?.id}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center space-x-1">
                            <Mail className="h-3 w-3 text-muted-foreground" />
                            <span className="text-sm">{resident.user?.email}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Phone className="h-3 w-3 text-muted-foreground" />
                            <span className="text-sm">{resident.user?.phone}</span>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Bed className="h-3 w-3 text-muted-foreground" />
                          <span className="font-medium">{resident.bedNumber}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm">{resident.checkInDate.toLocaleDateString()}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm">{resident.checkOutDate.toLocaleDateString()}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm">{stayDuration} days</span>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(resident)}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              View Profile
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <MessageSquare className="mr-2 h-4 w-4" />
                              Send Message
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {resident.isCheckoutPending && (
                              <DropdownMenuItem>
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Process Checkout
                              </DropdownMenuItem>
                            )}
                            {resident.hasIssues && (
                              <DropdownMenuItem>
                                <AlertTriangle className="mr-2 h-4 w-4" />
                                Resolve Issues
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>

          {filteredResidents.length === 0 && (
            <div className="text-center py-8">
              <Users className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No residents found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Try adjusting your search criteria or check-in new residents.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
