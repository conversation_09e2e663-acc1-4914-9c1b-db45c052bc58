import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User, mockUsers } from '@/data/mockData';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string, rememberMe?: boolean) => Promise<{ success: boolean; error?: string }>;
  logout: () => void;
  forgotPassword: (email: string) => Promise<{ success: boolean; error?: string }>;
  resetPassword: (token: string, newPassword: string) => Promise<{ success: boolean; error?: string }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

// Mock authentication service - replace with real API calls
const AUTH_STORAGE_KEY = 'hostel_hub_auth';
const REMEMBER_ME_KEY = 'hostel_hub_remember';

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize auth state from localStorage
  useEffect(() => {
    const initializeAuth = () => {
      try {
        const storedAuth = localStorage.getItem(AUTH_STORAGE_KEY);
        const rememberMe = localStorage.getItem(REMEMBER_ME_KEY) === 'true';
        
        if (storedAuth && rememberMe) {
          const authData = JSON.parse(storedAuth);
          // Verify user still exists in mock data
          const foundUser = mockUsers.find(u => u.id === authData.userId && u.status === 'active');
          if (foundUser) {
            setUser(foundUser);
          } else {
            // Clean up invalid auth data
            localStorage.removeItem(AUTH_STORAGE_KEY);
            localStorage.removeItem(REMEMBER_ME_KEY);
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        localStorage.removeItem(AUTH_STORAGE_KEY);
        localStorage.removeItem(REMEMBER_ME_KEY);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (email: string, password: string, rememberMe = false): Promise<{ success: boolean; error?: string }> => {
    setIsLoading(true);
    
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock authentication - find user by email
      const foundUser = mockUsers.find(u => u.email.toLowerCase() === email.toLowerCase() && u.status === 'active');
      
      if (!foundUser) {
        return { success: false, error: 'Invalid email or password' };
      }
      
      // Mock password validation (in real app, this would be handled by backend)
      // For demo purposes, accept any password for existing users
      if (password.length < 6) {
        return { success: false, error: 'Password must be at least 6 characters' };
      }
      
      // Set user and store auth data
      setUser(foundUser);
      
      const authData = {
        userId: foundUser.id,
        email: foundUser.email,
        loginTime: new Date().toISOString(),
      };
      
      localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(authData));
      localStorage.setItem(REMEMBER_ME_KEY, rememberMe.toString());
      
      return { success: true };
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: 'An unexpected error occurred' };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem(AUTH_STORAGE_KEY);
    localStorage.removeItem(REMEMBER_ME_KEY);
  };

  const forgotPassword = async (email: string): Promise<{ success: boolean; error?: string }> => {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Check if user exists
      const foundUser = mockUsers.find(u => u.email.toLowerCase() === email.toLowerCase() && u.status === 'active');
      
      if (!foundUser) {
        return { success: false, error: 'No account found with this email address' };
      }
      
      // In a real app, this would send a password reset email
      console.log(`Password reset email would be sent to: ${email}`);
      
      return { success: true };
    } catch (error) {
      console.error('Forgot password error:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  };

  const resetPassword = async (token: string, newPassword: string): Promise<{ success: boolean; error?: string }> => {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock token validation
      if (!token || token.length < 10) {
        return { success: false, error: 'Invalid or expired reset token' };
      }
      
      if (newPassword.length < 6) {
        return { success: false, error: 'Password must be at least 6 characters' };
      }
      
      // In a real app, this would update the password in the database
      console.log('Password would be reset for token:', token);
      
      return { success: true };
    } catch (error) {
      console.error('Reset password error:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    logout,
    forgotPassword,
    resetPassword,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
