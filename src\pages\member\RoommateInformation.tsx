import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  User, 
  Phone,
  Mail,
  Calendar,
  MapPin,
  Shield,
  Key,
  AlertTriangle,
  Clock,
  Users,
  MessageSquare,
  Info,
  Eye,
  UserCheck,
  Building2
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

// Roommate interface
interface Roommate {
  id: string;
  name: string;
  email: string;
  phone: string;
  avatar?: string;
  bedNumber: string;
  checkInDate: string;
  checkOutDate?: string;
  emergencyContact: {
    name: string;
    phone: string;
    relationship: string;
  };
  preferences: {
    sleepTime: string;
    wakeUpTime: string;
    studyHours: string;
    quietHours: boolean;
    guestPolicy: 'strict' | 'moderate' | 'flexible';
  };
  keyAccess: {
    hasRoomKey: boolean;
    keyNumber: string;
    depositPaid: boolean;
  };
  status: 'active' | 'checking_out' | 'checked_out';
  membershipType: 'regular' | 'guest';
}

// Mock roommate data
const mockRoommates: Roommate[] = [
  {
    id: '1',
    name: 'Rahul Sharma',
    email: '<EMAIL>',
    phone: '+91 9876543210',
    avatar: '/placeholder.svg',
    bedNumber: 'A101-1',
    checkInDate: '2024-01-01',
    emergencyContact: {
      name: 'Suresh Sharma',
      phone: '+91 9876543211',
      relationship: 'Father'
    },
    preferences: {
      sleepTime: '23:00',
      wakeUpTime: '06:30',
      studyHours: '20:00-22:00',
      quietHours: true,
      guestPolicy: 'moderate'
    },
    keyAccess: {
      hasRoomKey: true,
      keyNumber: 'A101-K1',
      depositPaid: true
    },
    status: 'active',
    membershipType: 'regular'
  },
  {
    id: '2',
    name: 'Priya Patel',
    email: '<EMAIL>',
    phone: '+91 9876543212',
    bedNumber: 'A101-2',
    checkInDate: '2024-01-15',
    emergencyContact: {
      name: 'Meera Patel',
      phone: '+91 9876543213',
      relationship: 'Mother'
    },
    preferences: {
      sleepTime: '22:30',
      wakeUpTime: '07:00',
      studyHours: '19:00-21:00',
      quietHours: true,
      guestPolicy: 'strict'
    },
    keyAccess: {
      hasRoomKey: true,
      keyNumber: 'A101-K2',
      depositPaid: true
    },
    status: 'active',
    membershipType: 'regular'
  },
  {
    id: '3',
    name: 'Amit Kumar',
    email: '<EMAIL>',
    phone: '+91 9876543214',
    bedNumber: 'A101-3',
    checkInDate: '2024-01-20',
    checkOutDate: '2024-01-25',
    emergencyContact: {
      name: 'Rajesh Kumar',
      phone: '+91 9876543215',
      relationship: 'Father'
    },
    preferences: {
      sleepTime: '24:00',
      wakeUpTime: '08:00',
      studyHours: 'Flexible',
      quietHours: false,
      guestPolicy: 'flexible'
    },
    keyAccess: {
      hasRoomKey: true,
      keyNumber: 'A101-K3',
      depositPaid: true
    },
    status: 'checking_out',
    membershipType: 'guest'
  }
];

export const RoommateInformation: React.FC = () => {
  const { user } = useAuth();
  const [selectedRoommate, setSelectedRoommate] = useState<Roommate | null>(null);
  
  // In real app, this would be filtered by user's actual room
  const roommates = mockRoommates.filter(roommate => roommate.id !== user?.id);
  const currentRoom = 'A101'; // This would come from user's booking data

  const getUserInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const getStatusBadge = (status: Roommate['status']) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case 'checking_out':
        return <Badge className="bg-orange-100 text-orange-800">Checking Out</Badge>;
      case 'checked_out':
        return <Badge className="bg-gray-100 text-gray-800">Checked Out</Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  const getMembershipBadge = (type: Roommate['membershipType']) => {
    return type === 'regular' 
      ? <Badge className="bg-blue-100 text-blue-800">Regular Member</Badge>
      : <Badge className="bg-purple-100 text-purple-800">Guest Member</Badge>;
  };

  const getGuestPolicyColor = (policy: string) => {
    switch (policy) {
      case 'strict': return 'text-red-600';
      case 'moderate': return 'text-yellow-600';
      case 'flexible': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  const formatTime = (time: string) => {
    if (time === 'Flexible') return time;
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const calculateStayDuration = (checkIn: string, checkOut?: string) => {
    const startDate = new Date(checkIn);
    const endDate = checkOut ? new Date(checkOut) : new Date();
    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Roommate Information</h1>
          <p className="text-muted-foreground">
            Information about your current roommates in Room {currentRoom}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline" className="bg-blue-50">
            <Building2 className="h-3 w-3 mr-1" />
            Room {currentRoom}
          </Badge>
          <Badge variant="outline" className="bg-green-50">
            <Users className="h-3 w-3 mr-1" />
            {roommates.filter(r => r.status === 'active').length} Active
          </Badge>
        </div>
      </div>

      {/* Security Notice */}
      <Card className="border-orange-200 bg-orange-50">
        <CardContent className="pt-6">
          <div className="flex items-start space-x-3">
            <Shield className="h-5 w-5 text-orange-600 mt-0.5" />
            <div>
              <h3 className="font-semibold text-orange-800">Privacy & Security Notice</h3>
              <p className="text-sm text-orange-700 mt-1">
                This information is shared only with your assigned roommates for security purposes, 
                emergency contact, and key sharing coordination. Personal details are kept confidential 
                and not shared with other hostel residents.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Roommates List */}
      {roommates.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <Users className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Roommates</h3>
              <p className="text-muted-foreground">
                You currently don't have any roommates in your room.
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {roommates.map((roommate) => (
            <Card key={roommate.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start space-x-3">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={roommate.avatar} alt={roommate.name} />
                    <AvatarFallback>
                      {getUserInitials(roommate.name)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold truncate">{roommate.name}</h3>
                      {getStatusBadge(roommate.status)}
                    </div>
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge variant="outline" className="text-xs">
                        Bed {roommate.bedNumber}
                      </Badge>
                      {getMembershipBadge(roommate.membershipType)}
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                {/* Contact Information */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-2 text-sm">
                    <Phone className="h-3 w-3 text-muted-foreground" />
                    <span>{roommate.phone}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm">
                    <Mail className="h-3 w-3 text-muted-foreground" />
                    <span className="truncate">{roommate.email}</span>
                  </div>
                </div>

                {/* Stay Information */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-2 text-sm">
                    <Calendar className="h-3 w-3 text-muted-foreground" />
                    <span>Checked in: {formatDate(roommate.checkInDate)}</span>
                  </div>
                  {roommate.checkOutDate && (
                    <div className="flex items-center space-x-2 text-sm">
                      <Calendar className="h-3 w-3 text-orange-500" />
                      <span>Checking out: {formatDate(roommate.checkOutDate)}</span>
                    </div>
                  )}
                  <div className="text-xs text-muted-foreground">
                    Stay duration: {calculateStayDuration(roommate.checkInDate, roommate.checkOutDate)} days
                  </div>
                </div>

                {/* Key Information */}
                <div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Key className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium">Room Key</span>
                  </div>
                  <div className="text-right">
                    <div className="text-xs text-muted-foreground">
                      {roommate.keyAccess.keyNumber}
                    </div>
                    <Badge variant={roommate.keyAccess.hasRoomKey ? 'default' : 'secondary'} className="text-xs">
                      {roommate.keyAccess.hasRoomKey ? 'Has Key' : 'No Key'}
                    </Badge>
                  </div>
                </div>

                {/* Quick Preferences */}
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-muted-foreground">Sleep Time:</span>
                    <span>{formatTime(roommate.preferences.sleepTime)}</span>
                  </div>
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-muted-foreground">Wake Time:</span>
                    <span>{formatTime(roommate.preferences.wakeUpTime)}</span>
                  </div>
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-muted-foreground">Guest Policy:</span>
                    <span className={`capitalize ${getGuestPolicyColor(roommate.preferences.guestPolicy)}`}>
                      {roommate.preferences.guestPolicy}
                    </span>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-2 pt-2">
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="flex-1"
                        onClick={() => setSelectedRoommate(roommate)}
                      >
                        <Eye className="h-3 w-3 mr-1" />
                        View Details
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-md">
                      <DialogHeader>
                        <DialogTitle className="flex items-center space-x-2">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={roommate.avatar} alt={roommate.name} />
                            <AvatarFallback className="text-sm">
                              {getUserInitials(roommate.name)}
                            </AvatarFallback>
                          </Avatar>
                          <span>{roommate.name}</span>
                        </DialogTitle>
                        <DialogDescription>
                          Detailed roommate information and preferences
                        </DialogDescription>
                      </DialogHeader>
                      
                      <div className="space-y-4">
                        {/* Emergency Contact */}
                        <div>
                          <h4 className="font-semibold text-sm mb-2 flex items-center">
                            <AlertTriangle className="h-4 w-4 mr-1 text-red-500" />
                            Emergency Contact
                          </h4>
                          <div className="bg-gray-50 p-3 rounded-lg space-y-1">
                            <div className="flex justify-between text-sm">
                              <span className="text-muted-foreground">Name:</span>
                              <span>{roommate.emergencyContact.name}</span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-muted-foreground">Phone:</span>
                              <span>{roommate.emergencyContact.phone}</span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-muted-foreground">Relation:</span>
                              <span>{roommate.emergencyContact.relationship}</span>
                            </div>
                          </div>
                        </div>

                        {/* Detailed Preferences */}
                        <div>
                          <h4 className="font-semibold text-sm mb-2 flex items-center">
                            <Clock className="h-4 w-4 mr-1 text-blue-500" />
                            Preferences & Schedule
                          </h4>
                          <div className="bg-gray-50 p-3 rounded-lg space-y-2">
                            <div className="flex justify-between text-sm">
                              <span className="text-muted-foreground">Sleep Time:</span>
                              <span>{formatTime(roommate.preferences.sleepTime)}</span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-muted-foreground">Wake Time:</span>
                              <span>{formatTime(roommate.preferences.wakeUpTime)}</span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-muted-foreground">Study Hours:</span>
                              <span>{roommate.preferences.studyHours}</span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-muted-foreground">Quiet Hours:</span>
                              <Badge variant={roommate.preferences.quietHours ? 'default' : 'secondary'} className="text-xs">
                                {roommate.preferences.quietHours ? 'Prefers Quiet' : 'Flexible'}
                              </Badge>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-muted-foreground">Guest Policy:</span>
                              <span className={`capitalize ${getGuestPolicyColor(roommate.preferences.guestPolicy)}`}>
                                {roommate.preferences.guestPolicy}
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* Key Access Details */}
                        <div>
                          <h4 className="font-semibold text-sm mb-2 flex items-center">
                            <Key className="h-4 w-4 mr-1 text-green-500" />
                            Key Access Information
                          </h4>
                          <div className="bg-gray-50 p-3 rounded-lg space-y-1">
                            <div className="flex justify-between text-sm">
                              <span className="text-muted-foreground">Key Number:</span>
                              <span className="font-mono">{roommate.keyAccess.keyNumber}</span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-muted-foreground">Has Room Key:</span>
                              <Badge variant={roommate.keyAccess.hasRoomKey ? 'default' : 'secondary'} className="text-xs">
                                {roommate.keyAccess.hasRoomKey ? 'Yes' : 'No'}
                              </Badge>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-muted-foreground">Deposit Status:</span>
                              <Badge variant={roommate.keyAccess.depositPaid ? 'default' : 'destructive'} className="text-xs">
                                {roommate.keyAccess.depositPaid ? 'Paid' : 'Pending'}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                  
                  <Button variant="outline" size="sm" className="flex-1">
                    <MessageSquare className="h-3 w-3 mr-1" />
                    Message
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Room Guidelines */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Info className="h-5 w-5 mr-2" />
            Room Sharing Guidelines
          </CardTitle>
          <CardDescription>
            Important guidelines for harmonious room sharing
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-semibold text-sm">Key Sharing Protocol</h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Always inform roommates when leaving with the key</li>
                <li>• Return keys promptly to avoid inconvenience</li>
                <li>• Report lost keys immediately to management</li>
                <li>• Do not duplicate keys without permission</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold text-sm">Emergency Procedures</h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Keep emergency contact numbers accessible</li>
                <li>• Inform roommates of extended absences</li>
                <li>• Know the location of emergency exits</li>
                <li>• Report safety concerns immediately</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
