import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Calculator, 
  Clock, 
  Utensils,
  Bed,
  Shield,
  Receipt,
  AlertCircle,
  CheckCircle,
  Info
} from 'lucide-react';
import { type Hostel, type BillingItem } from '@/data/mockData';

interface BillingCalculatorProps {
  hostel: Hostel;
  membershipType: 'regular' | 'guest';
  bedType: 'standard' | 'premium';
  foodPackage: 'with_meals' | 'without_meals';
  checkInTime?: Date;
  checkOutTime?: Date;
  stayDuration?: {
    days: number;
    hours: number;
    minutes: number;
  };
  foodConsumption?: Array<{
    date: string;
    meals: Array<'breakfast' | 'lunch' | 'dinner'>;
  }>;
  onBillingUpdate?: (billing: {
    items: BillingItem[];
    subtotal: number;
    securityDeposit: number;
    total: number;
  }) => void;
  className?: string;
}

export const BillingCalculator: React.FC<BillingCalculatorProps> = ({
  hostel,
  membershipType,
  bedType,
  foodPackage,
  checkInTime,
  checkOutTime,
  stayDuration,
  foodConsumption = [],
  onBillingUpdate,
  className
}) => {
  const [billingItems, setBillingItems] = useState<BillingItem[]>([]);
  const [isCalculating, setIsCalculating] = useState(false);

  // Calculate billing whenever inputs change
  useEffect(() => {
    calculateBilling();
  }, [hostel, membershipType, bedType, foodPackage, checkInTime, checkOutTime, stayDuration, foodConsumption]);

  const calculateBilling = async () => {
    setIsCalculating(true);
    
    // Simulate calculation delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const items: BillingItem[] = [];
    let itemId = 1;

    // Calculate bed charges
    if (membershipType === 'regular') {
      // Monthly subscription
      const monthlyRate = hostel.bedTypes[bedType].monthlyRate;
      items.push({
        id: `bill_${itemId++}`,
        bookingId: 'temp',
        type: 'bed_charge',
        description: `${bedType.charAt(0).toUpperCase() + bedType.slice(1)} bed - Monthly subscription`,
        amount: monthlyRate,
        quantity: 1,
        date: new Date().toISOString(),
        category: 'accommodation'
      });
    } else {
      // Guest daily booking
      if (stayDuration) {
        const { days, hours } = stayDuration;
        const dailyRate = hostel.bedTypes[bedType].dailyRate;
        const hourlyRate = hostel.hourlyRate || 0;
        
        // Full days
        if (days > 0) {
          items.push({
            id: `bill_${itemId++}`,
            bookingId: 'temp',
            type: 'bed_charge',
            description: `${bedType.charAt(0).toUpperCase() + bedType.slice(1)} bed - ${days} day${days > 1 ? 's' : ''}`,
            amount: dailyRate * days,
            quantity: days,
            date: new Date().toISOString(),
            category: 'accommodation'
          });
        }
        
        // Partial day (hours)
        if (hours > 0) {
          const minHours = Math.max(hours, hostel.minimumStayHours);
          const hourlyCharge = hourlyRate * minHours;
          
          // If hourly charge exceeds daily rate, charge daily rate instead
          const finalCharge = Math.min(hourlyCharge, dailyRate);
          
          items.push({
            id: `bill_${itemId++}`,
            bookingId: 'temp',
            type: 'bed_charge',
            description: `${bedType.charAt(0).toUpperCase() + bedType.slice(1)} bed - ${minHours} hours (min ${hostel.minimumStayHours}h)`,
            amount: finalCharge,
            quantity: minHours,
            date: new Date().toISOString(),
            category: 'accommodation'
          });
        }
      }
    }

    // Calculate food charges
    if (foodPackage === 'with_meals') {
      const dailyFoodRate = hostel.foodPackages.withMeals.dailyRate;
      
      if (membershipType === 'regular') {
        // Monthly food package (30 days)
        items.push({
          id: `bill_${itemId++}`,
          bookingId: 'temp',
          type: 'food_charge',
          description: 'Monthly food package (30 days)',
          amount: dailyFoodRate * 30,
          quantity: 30,
          date: new Date().toISOString(),
          category: 'food'
        });
      } else {
        // Guest food consumption
        if (foodConsumption.length > 0) {
          let totalMeals = 0;
          foodConsumption.forEach(day => {
            totalMeals += day.meals.length;
          });
          
          const mealRate = dailyFoodRate / 3; // Assuming 3 meals per day
          items.push({
            id: `bill_${itemId++}`,
            bookingId: 'temp',
            type: 'food_charge',
            description: `Individual meals (${totalMeals} meals)`,
            amount: mealRate * totalMeals,
            quantity: totalMeals,
            date: new Date().toISOString(),
            category: 'food'
          });
        } else if (stayDuration) {
          // Estimate based on stay duration
          const estimatedDays = stayDuration.days + (stayDuration.hours > 12 ? 1 : 0);
          items.push({
            id: `bill_${itemId++}`,
            bookingId: 'temp',
            type: 'food_charge',
            description: `Food package (${estimatedDays} day${estimatedDays > 1 ? 's' : ''})`,
            amount: dailyFoodRate * estimatedDays,
            quantity: estimatedDays,
            date: new Date().toISOString(),
            category: 'food'
          });
        }
      }
    }

    // Security deposit
    items.push({
      id: `bill_${itemId++}`,
      bookingId: 'temp',
      type: 'security_deposit',
      description: 'Refundable security deposit',
      amount: hostel.securityDeposit,
      quantity: 1,
      date: new Date().toISOString(),
      category: 'deposit'
    });

    setBillingItems(items);
    
    // Calculate totals
    const subtotal = items
      .filter(item => item.type !== 'security_deposit')
      .reduce((sum, item) => sum + item.amount, 0);
    
    const securityDeposit = hostel.securityDeposit;
    const total = subtotal + securityDeposit;

    if (onBillingUpdate) {
      onBillingUpdate({
        items,
        subtotal,
        securityDeposit,
        total
      });
    }
    
    setIsCalculating(false);
  };

  const getItemIcon = (type: string) => {
    switch (type) {
      case 'bed_charge': return <Bed className="h-4 w-4" />;
      case 'food_charge': return <Utensils className="h-4 w-4" />;
      case 'security_deposit': return <Shield className="h-4 w-4" />;
      default: return <Receipt className="h-4 w-4" />;
    }
  };

  const getItemColor = (type: string) => {
    switch (type) {
      case 'bed_charge': return 'text-blue-600';
      case 'food_charge': return 'text-green-600';
      case 'security_deposit': return 'text-orange-600';
      default: return 'text-gray-600';
    }
  };

  const subtotal = billingItems
    .filter(item => item.type !== 'security_deposit')
    .reduce((sum, item) => sum + item.amount, 0);
  
  const securityDeposit = hostel.securityDeposit;
  const total = subtotal + securityDeposit;

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Calculator className="h-5 w-5" />
          <span>Billing Breakdown</span>
          {isCalculating && <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>}
        </CardTitle>
        <CardDescription>
          Transparent pricing with itemized charges
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Billing Items */}
        <div className="space-y-3">
          {billingItems.map((item) => (
            <div key={item.id} className="flex items-center justify-between p-3 rounded-lg border">
              <div className="flex items-center space-x-3">
                <div className={getItemColor(item.type)}>
                  {getItemIcon(item.type)}
                </div>
                <div>
                  <p className="font-medium text-sm">{item.description}</p>
                  {item.quantity > 1 && (
                    <p className="text-xs text-muted-foreground">
                      ₹{(item.amount / item.quantity).toFixed(0)} × {item.quantity}
                    </p>
                  )}
                </div>
              </div>
              <div className="text-right">
                <p className="font-medium">₹{item.amount.toLocaleString()}</p>
                {item.type === 'security_deposit' && (
                  <p className="text-xs text-muted-foreground">Refundable</p>
                )}
              </div>
            </div>
          ))}
        </div>

        <Separator />

        {/* Summary */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm">Subtotal:</span>
            <span className="font-medium">₹{subtotal.toLocaleString()}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm">Security Deposit:</span>
            <span className="font-medium">₹{securityDeposit.toLocaleString()}</span>
          </div>
          <Separator />
          <div className="flex items-center justify-between text-lg font-bold">
            <span>Total Amount:</span>
            <span>₹{total.toLocaleString()}</span>
          </div>
        </div>

        {/* Important Notes */}
        <div className="space-y-2 p-3 bg-muted/50 rounded-lg">
          <div className="flex items-center space-x-2 text-sm">
            <Info className="h-4 w-4 text-blue-500" />
            <span className="font-medium">Important Notes:</span>
          </div>
          <ul className="text-xs text-muted-foreground space-y-1 ml-6">
            <li>• Security deposit is fully refundable upon checkout</li>
            {membershipType === 'guest' && (
              <>
                <li>• Minimum stay: {hostel.minimumStayHours} hours</li>
                <li>• Hourly rate applies for stays less than 24 hours</li>
              </>
            )}
            {foodPackage === 'with_meals' && (
              <li>• Food charges are based on actual consumption for guests</li>
            )}
            <li>• All prices are inclusive of applicable taxes</li>
          </ul>
        </div>

        {/* Pricing Comparison */}
        {membershipType === 'guest' && stayDuration && stayDuration.days >= 7 && (
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center space-x-2 text-sm text-yellow-800">
              <AlertCircle className="h-4 w-4" />
              <span className="font-medium">Consider Monthly Membership</span>
            </div>
            <p className="text-xs text-yellow-700 mt-1">
              For stays longer than 7 days, a monthly membership might be more economical.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
