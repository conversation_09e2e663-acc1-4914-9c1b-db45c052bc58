// Floor Management Service for hostel owners

import { 
  Floor, 
  FloorModificationRequest, 
  AddFloorRequest,
  ModifyFloorRequest,
  FloorStatistics,
  HostelStatistics,
  HostelInfo
} from '@/types/roomManagement';
import { 
  mockFloors, 
  mockFloorModificationRequests,
  mockHostels,
  getFloorsByHostelId,
  getFloorById,
  getHostelById,
  getHostelsByOwnerId,
  getFloorModificationRequestsByHostelId,
  getFloorStatistics,
  getHostelStatistics
} from '@/data/mockData';

export interface FloorOperationResult {
  success: boolean;
  data?: any;
  error?: string;
  requestId?: string;
}

// Generate unique IDs
const generateFloorId = (): string => `floor_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
const generateFloorRequestId = (): string => `fmr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

// Hostel Management Functions
export const getOwnerHostels = async (ownerId: string): Promise<FloorOperationResult> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    const hostels = getHostelsByOwnerId(ownerId);
    
    return {
      success: true,
      data: hostels
    };

  } catch (error) {
    console.error('Error getting owner hostels:', error);
    return {
      success: false,
      error: 'Failed to get hostels'
    };
  }
};

export const getHostelDetails = async (hostelId: string): Promise<FloorOperationResult> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 200));

    const hostel = getHostelById(hostelId);
    
    if (!hostel) {
      return {
        success: false,
        error: 'Hostel not found'
      };
    }

    return {
      success: true,
      data: hostel
    };

  } catch (error) {
    console.error('Error getting hostel details:', error);
    return {
      success: false,
      error: 'Failed to get hostel details'
    };
  }
};

// Floor Management Functions
export const getHostelFloors = async (hostelId: string): Promise<FloorOperationResult> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 400));

    const floors = getFloorsByHostelId(hostelId);
    
    return {
      success: true,
      data: floors
    };

  } catch (error) {
    console.error('Error getting hostel floors:', error);
    return {
      success: false,
      error: 'Failed to get floors'
    };
  }
};

export const submitAddFloorRequest = async (
  hostelId: string,
  ownerId: string,
  floorData: AddFloorRequest
): Promise<FloorOperationResult> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Validate floor number uniqueness
    const existingFloors = getFloorsByHostelId(hostelId);
    const floorExists = existingFloors.some(floor => floor.floorNumber === floorData.floorNumber);
    
    if (floorExists) {
      return {
        success: false,
        error: `Floor ${floorData.floorNumber} already exists`
      };
    }

    // Validate floor number sequence (should be next in sequence)
    const maxFloorNumber = Math.max(...existingFloors.map(f => f.floorNumber), 0);
    if (floorData.floorNumber !== maxFloorNumber + 1) {
      return {
        success: false,
        error: `Floor number should be ${maxFloorNumber + 1} (next in sequence)`
      };
    }

    // Create floor modification request
    const requestId = generateFloorRequestId();
    const newRequest: FloorModificationRequest = {
      id: requestId,
      hostelId,
      ownerId,
      requestType: 'add_floor',
      status: 'pending',
      submittedAt: new Date().toISOString(),
      requestDetails: floorData
    };

    // Add to mock data
    mockFloorModificationRequests.push(newRequest);

    return {
      success: true,
      requestId,
      data: newRequest
    };

  } catch (error) {
    console.error('Error submitting add floor request:', error);
    return {
      success: false,
      error: 'Failed to submit floor addition request'
    };
  }
};

export const updateFloorDetails = async (
  floorId: string,
  ownerId: string,
  updates: Partial<Floor>
): Promise<FloorOperationResult> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800));

    const floor = getFloorById(floorId);
    if (!floor) {
      return {
        success: false,
        error: 'Floor not found'
      };
    }

    // Check if updates require approval
    const requiresApproval = !!(
      updates.floorType || 
      updates.totalRooms || 
      (updates.details && (
        updates.details.area !== floor.details.area ||
        updates.details.hasElevatorAccess !== floor.details.hasElevatorAccess
      ))
    );

    if (requiresApproval) {
      const requestId = generateFloorRequestId();
      const modifyRequest: ModifyFloorRequest = {
        floorId,
        changes: updates,
        reason: 'Updating floor configuration'
      };

      const newRequest: FloorModificationRequest = {
        id: requestId,
        hostelId: floor.hostelId,
        ownerId,
        requestType: 'modify_floor',
        status: 'pending',
        submittedAt: new Date().toISOString(),
        requestDetails: modifyRequest
      };

      mockFloorModificationRequests.push(newRequest);

      return {
        success: true,
        requestId,
        data: { message: 'Request submitted for admin approval', request: newRequest }
      };
    }

    // For minor updates, apply immediately
    const floorIndex = mockFloors.findIndex(f => f.id === floorId);
    if (floorIndex !== -1) {
      mockFloors[floorIndex] = {
        ...mockFloors[floorIndex],
        ...updates,
        updatedAt: new Date().toISOString()
      };
    }

    return {
      success: true,
      data: { floor: mockFloors[floorIndex] }
    };

  } catch (error) {
    console.error('Error updating floor details:', error);
    return {
      success: false,
      error: 'Failed to update floor details'
    };
  }
};

// Statistics Functions
export const getFloorStatisticsService = async (floorId: string): Promise<FloorOperationResult> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    const statistics = getFloorStatistics(floorId);
    
    return {
      success: true,
      data: statistics
    };

  } catch (error) {
    console.error('Error getting floor statistics:', error);
    return {
      success: false,
      error: 'Failed to get floor statistics'
    };
  }
};

export const getHostelStatisticsService = async (hostelId: string): Promise<FloorOperationResult> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    const statistics = getHostelStatistics(hostelId);
    
    return {
      success: true,
      data: statistics
    };

  } catch (error) {
    console.error('Error getting hostel statistics:', error);
    return {
      success: false,
      error: 'Failed to get hostel statistics'
    };
  }
};

// Floor Filtering and Search
export const searchFloors = async (
  hostelId: string,
  filters: {
    floorType?: string[];
    status?: string[];
    hasElevatorAccess?: boolean;
    hasCommonArea?: boolean;
    minOccupancy?: number;
    maxOccupancy?: number;
  }
): Promise<FloorOperationResult> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 400));

    let floors = getFloorsByHostelId(hostelId);

    // Apply filters
    if (filters.floorType && filters.floorType.length > 0) {
      floors = floors.filter(floor => filters.floorType!.includes(floor.floorType));
    }

    if (filters.status && filters.status.length > 0) {
      floors = floors.filter(floor => filters.status!.includes(floor.status));
    }

    if (filters.hasElevatorAccess !== undefined) {
      floors = floors.filter(floor => floor.details.hasElevatorAccess === filters.hasElevatorAccess);
    }

    if (filters.hasCommonArea !== undefined) {
      floors = floors.filter(floor => floor.details.hasCommonArea === filters.hasCommonArea);
    }

    if (filters.minOccupancy !== undefined) {
      floors = floors.filter(floor => floor.currentOccupancy >= filters.minOccupancy!);
    }

    if (filters.maxOccupancy !== undefined) {
      floors = floors.filter(floor => floor.currentOccupancy <= filters.maxOccupancy!);
    }

    return {
      success: true,
      data: floors
    };

  } catch (error) {
    console.error('Error searching floors:', error);
    return {
      success: false,
      error: 'Failed to search floors'
    };
  }
};

// Floor Modification Requests Management
export const getFloorModificationRequests = async (hostelId: string): Promise<FloorOperationResult> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    const requests = getFloorModificationRequestsByHostelId(hostelId);
    
    return {
      success: true,
      data: requests
    };

  } catch (error) {
    console.error('Error getting floor modification requests:', error);
    return {
      success: false,
      error: 'Failed to get floor modification requests'
    };
  }
};
