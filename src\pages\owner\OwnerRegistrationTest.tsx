import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export const OwnerRegistrationTest: React.FC = () => {
  console.log('OwnerRegistrationTest component rendering');

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold">Owner Registration Test</h1>
        <p className="text-muted-foreground">This is a test page to verify routing works</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Test Component</CardTitle>
          <CardDescription>
            If you can see this, the routing and basic component rendering is working.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={() => console.log('Button clicked')}>
            Test Button
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};
